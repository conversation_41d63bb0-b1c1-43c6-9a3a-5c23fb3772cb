#ifndef LIDARPROTOCOL_H
#define LIDARPROTOCOL_H

#include <QObject>
#include "serial_base.h"
#include <QDebug>
#include <QTime>
#include <QElapsedTimer>
#include <Windows.h>
#include <QAbstractNativeEventFilter>
#include <dbt.h>
#include <math.h>


#pragma pack(1)
typedef struct {
    uint8_t     id;
    uint8_t     ack_type;//0 read,1 write
    uint8_t     ack_result;
    QByteArray  data;

}iap_ack_t;
#pragma pack()


namespace lidardevice {
    enum {
            k_H2D = 0x01,	/*host->device*/
            k_D2H = 0X02,	/*device->host*/
            k_HWS = 0x04,	/*host write success*/
            k_HRS = 0x08,	/*host read success*/
            k_HSS = 0x10,	/*host send ok*/
            k_DFF = 0x20,  /*device fixed frequency send*/
            k_DNP = 0x40,  /*device not parse logic*/
    };



    #pragma pack(1)
    typedef struct {
        uint16_t header;
        uint16_t cmd;
        uint16_t vol;
        uint8_t  def;
        uint8_t  check;
        uint8_t  status;
        uint16_t code;

    }cspc_dds_protocol_t;
    #pragma pack()

    #pragma pack(1)
    typedef struct {
        uint8_t header;
        uint8_t cmd;
        uint8_t id;
        uint8_t check;
        uint16_t data_length;
        uint8_t data[1];


    }cspc_iap_protocol_t;
    #pragma pack()

    #pragma pack(1)
    typedef struct {
        uint16_t header;
        uint16_t data_length;
        uint16_t check;
        uint8_t  type;
        uint8_t  data[1];

    }cspc_lidar_info_protocol_t;
    #pragma pack()

    #pragma pack(1)
    typedef struct {
        uint16_t header;
        uint8_t mt;
        uint8_t lsn;
        uint16_t fsa;
        uint16_t lsa;
        uint16_t check;
        uint8_t data[1];

    }cspc_protocol_t;
    #pragma pack()


    #pragma pack(1)
    typedef struct {
        uint8_t header;
        uint8_t index;
        uint16_t spd;
        uint32_t data[4];
        uint16_t check_sum;

    }rock_protocol_t;
    #pragma pack()

    #pragma pack(1)
    typedef struct
    {
        uint16_t header;
        uint8_t information;
        uint8_t data_number;
        uint16_t speed;
        uint16_t first_angle;
        uint8_t data[1];
    } camsense_protocol_t;
    #pragma pack()


    class ProcessProtocol : public SerialBase {
        Q_OBJECT
    public:
        enum ProtocolType {
          kNarwal = 0,
          kSamsung,
          kCoin,
          kOther
        };

        enum NarwalHead {
          kFrameHead1 = 0xA5,
          kFrameHead2 = 0x5A,
          kFrameHead3 = 0xAA
        };
        enum NarwalStatus {
          kParseData1 = 1,
          kParseData2 = 2,
          kParseData3 = 3,
          kParseNone
        };


        ProcessProtocol();
        ~ProcessProtocol();
        void ParseProtocol();
    signals:
        void ProcessSendToMainPointCloud(std::vector<ProtocolData> data);
        void FeedbackInfo(QByteArray fdb);
        void transmitHistogramData(QByteArray tr);
        void transmitGreymapData(QByteArray tr);
        void transmitCalibrationData(QByteArray tr);
        void transmitPointCloudData(std::vector<ProtocolData> data);
        void TransmitTemperature(float temp);
        void sig_iap_ack(iap_ack_t ack);

    public slots:
        void QuitThread(bool isClose);
        void TransmitData(QByteArray str);
        void SetProtocolType(int protocolType);
        void RecordData(bool isOk);

    private:
        bool isQuitThread;
        QByteArray ringBuff,data;
        int protocolTyp;
        ProtocolData protocolData;
        std::vector<ProtocolData> vprotocolData;
        uint8_t ver;
        uint16_t health;
        uint16_t voltage;

        uint checkSumCnt;
        bool isRecordDataToDisk;
    };
}

#endif // NARWALPROTOCOL_H
