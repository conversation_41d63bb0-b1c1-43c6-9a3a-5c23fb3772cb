#include "serial_base.h"
#include <QThread>
#include <QDebug>

SerialBase::SerialBase(QObject *parent) : QObject(parent)
{

}

SerialBase::~SerialBase()
{
    if(serialPort != nullptr){
        delete serialPort;
    }
}

void SerialBase::InitSerialPtr(bool isInit, QString threadName)
{
    if(isInit == true) {
        if(serialPort == nullptr) {
            serialPort = new QSerialPort();
            connect(serialPort,&QSerialPort::readyRead,this,&SerialBase::ParseProtocol);
        }
        thName = threadName;
    }
}

void SerialBase::OpenSerialDevice(bool isOpen, QString comPort, int buad)
{
    if(serialPort == nullptr) {
        return;
    }
    if(isOpen == true) {
        serialPort->setPortName(comPort);
        serialPort->setBaudRate(buad);
        serialPort->setDataBits(QSerialPort::Data8);
        serialPort->setParity(QSerialPort::NoParity);
        serialPort->setStopBits(QSerialPort::OneStop);
        serialPort->setFlowControl(QSerialPort::NoFlowControl);
        if(serialPort->open(QIODevice::ReadWrite)) {
            serialPort->open(QIODevice::ReadWrite);
            emit  Opened();
            qDebug()<< "open finish!";
        }
    }
    else {
        serialPort->close();
        qDebug()<< "close finish!";
        emit Closed();
    }


    if(isOpen == true) {
        qDebug()<<thName<<": thread OpenSerialDevice id:"<< QThread::currentThreadId();
    }
    else {
        qDebug()<<thName<<": thread CloseSerialDevice id:"<< QThread::currentThreadId();
    }

}


