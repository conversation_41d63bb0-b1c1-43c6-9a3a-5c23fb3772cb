#ifndef SERIALBASE_H
#define SERIALBASE_H

#include <QObject>
#include <QSerialPort>
#include <QWidget>
#include <QSerialPortInfo>
/*#include <Windows.h>
#include <QAbstractNativeEventFilter>
#include <dbt.h>*/

typedef std::vector<uint16_t> vUint16_t;
typedef std::vector<float>    vfloat;

typedef struct {
    float           angle;
    float           deepth;
    uint16_t        indensity;
    uint8_t         HrDenote;
}PolarData;

typedef struct {
    std::vector<PolarData>    data;
    vUint16_t       debugUintData;
    vfloat          debufFloatData;
    float           speed;
    uint8_t         version;
    uint16_t        mcuVoltage;
    uint16_t         healthCode;
}ProtocolData;

class SerialBase : public QObject
{
    Q_OBJECT
public:
    explicit SerialBase(QObject *parent = nullptr);
    virtual  ~SerialBase();
    virtual void ParseProtocol() = 0;
    virtual void TransmitData(QByteArray str) = 0;
    virtual void SetProtocolType(int protocolType) = 0;
    QSerialPort* GetSerialPtr() {
        return serialPort;
    }
    QString thName;
public slots:
    void OpenSerialDevice(bool isOpen, QString comPort, int buad);
    void InitSerialPtr(bool isInit, QString threadName);

signals:
    void Opened();
    void Closed();

private:
    QSerialPort  *serialPort = nullptr;
};

#endif // SERIALBASE_H
