QT       += core gui serialport printsupport axcontainer

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets sql

CONFIG += c++11 console

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS
DEFINES += QT_MESSAGELOGCONTEXT

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

include         ($$PWD/polynomialFit/polynomialFit.pri)
include         ($$PWD/customPlot/customPlot.pri)
include         ($$PWD/task_process/task_base.pri)

INCLUDEPATH     += $$PWD/polynomialFit
INCLUDEPATH     += $$PWD/customPlot
INCLUDEPATH     += $$PWD/eigen-3.4.0
INCLUDEPATH     += $$PWD/dll/

LIBS += $$PWD/libdToF_calibration.a

SOURCES += \
    dynamic_calibration.cpp \
    main.cpp \
    import_pointcloud.cpp \
    pointcloud_quality.cpp \
    show_image.cpp \
    lidar_protocol.cpp \
    other_protocol.cpp \
    record_pointcloud.cpp \
    saveas_capture.cpp \
    serial_base.cpp \
    calibration_chart.cpp \
    greymap_chart.cpp \
    histogram_chart.cpp \
    pointclouds_chart.cpp \
    lidar_database.cpp \
    task_process/task_base.cpp \
    widget.cpp \

HEADERS += \
    dynamic_calibration.h \
    import_pointcloud.h \
    lidar_protocol.h \
    other_protocol.h \
    pointcloud_quality.h \
    record_pointcloud.h \
    saveas_capture.h \
    serial_base.h \
    calibration_chart.h \
    greymap_chart.h \
    histogram_chart.h \
    pointclouds_chart.h \
    lidar_database.h \
    show_image.h \
    task_process/task_base.h \
    widget.h \
    dtof_calibration.h \


FORMS += \
    dynamiccalibration.ui \
    pointcloudquality.ui \
    showimage.ui \
    importpointcloud.ui \
    recordpointcloud.ui \
    saveascapture.ui \
    calibrationchart.ui \
    greymapchart.ui \
    histogramchart.ui \
    pointcloudschart.ui \
    lidardatabase.ui \
    widget.ui \

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target


RESOURCES += \
    logo.qrc \
    logo2.qrc \
    res.qrc

#TARGET = WIN_BASE-V2.3.1#PCS_APP-V1.0.1#



DEFINES += USING_LOG_TO_FILE=0 #用于默认将log日志开启输出到文件
DEFINES += USING_CUSTOM_STYLE=0
DEFINES += USING_CUSTOM_HIDE=0

VERSION = 2.3.11
DEFINES += APP_VERSION=\\\"$$VERSION\\\"
TARGET = "WIN_BASE-V2_3_11_T5"#"$$VERSION"

QMAKE_CXXFLAGS += "-Wa,-mbig-obj"

#DISTFILES += \
#    logo.rc

#RC_FILE += logo.rc
RC_ICONS = logo.ico
#v1.0.4
#1)添加了调试参数表格
#2)设计师若不成功  则删除头文件然后清除在生产

#v1.05
#1)添加tof值1000取mean


#v1.06
#1)添加了参数使用的tips信息
#2)添加了两个两字节的调试   两个单个浮点型的调试
#3)添加了stylesheet的修饰

#v1.07
#1)修复了接收异常长度的数据导致取数据溢出问题

#v1.0.8
#1)添加了多端线性拟合

#v1.0.9
#1)修改读写寄存器为适应1/2两个字节的方式

#v1.0.10
#1)新增校正参数与反射率参数
#2)新增peak值1000的mean

#v1.0.11
#1)添加了peak列数据
#2)下发1m以内最大最小得peak

#v1.0.12
#1)10cm拟合数据不参与计算

#v1.0.13
#1)添加了点云显示ui

#v1.0.14
#1)点云图中添加了各种action 用于控制数据

#v1.0.15
#1)点击相应控件后弹出窗口
#2)整个显示雷达的整体数据

#v1.0.16
#1)完成了scan模式下所有数据的显示功能；
#2)将scan模式下的图标替换成相应功能的图标

#v2.0.1
#1)将串口接收提取到了另外一个窗口
#2)添加数据库用于存储雷达的各种数据与插入图片等

#v2.0.2
#1)供给客户使用的软件 通过宏来编译是否有其他功能   通过密码来触发其他功能

#v2.0.3
#1)通过设置记录接收串口的数据
#2)统一greymap histogram calibration 的ui风格等

#v2.1.1
#1)新增了三角法静态与动态校正

#v2.1.2
#1)新增了三角法和tof法的rolling data数据的获取与解析

#v2.1.3
#1)优化了rolling data 数据刷新的异常逻辑

#v2.1.4
#1)优化了版本信息的提取
#2)修复了零位包对应点的深度值的提取
#3)添加校正时候协议

#v2.1.5
#1)优化了校正协议下数据的获取


#v2.1.6
#1)完成了多端线性拟合与参数的传输


#v2.1.7
#1)修复了接收动静态校正tof参数到发送行(原在接收行)


#v2.1.8
#1)修复了转速判断

#v2.1.9
#1)修复dynamic calibration 采集数据无法获得小数点问题

#v2.1.10
#1)添加peak 用于滤波

#v2.1.11
#1)dynamic calibration 导入导出数据

#v2.1.12
#1)将peak值获取改为float

#v2.1.13
#1)添加5*5的greymap显示窗口

#v2.1.14
#1)显示直方图逻辑

#v2.1.15
#1)屏蔽校正参数的窗口

#v2.1.16
#1)修改动态校正模式下计算距离参数

#v2.1.17
#1)添加了点云数据保存功能  "-" 开启  "*"关闭

#v2.1.17
#1)优化了串口接收逻辑与添加异常信息打印

#v2.1.18
#1)优化了采集数据的个数

#v2.1.20
#1)添加了2*4宇称greymap

#v2.1.21
#1)添加了角度异常的日志输出

#v2.1.22
#1)添加了所有数据的显示与切换模式的分类

#v2.1.23
#1)调整一些显示逻辑

#v2.1.27
#1)添加了导轨标定算法与数据传输逻辑

#v2.1.28
#1)优化了显示界面与一些控制逻辑

#v2.1.29
#1)添加了零度角补偿模块

#v2.1.30
#1)添加了部分动态采集数据的任务列表

#v2.1.31
#1)添加验证精度逻辑；
#2)添加标定算法计算；

#v2.1.32
#1)修改了出口显示数据逻辑
#2)去掉显示健康信息提示

#v2.1.33
#1)零度角识别采集90度的
#2)增加高反材质识别

#v2.1.34
#1)添加密码隐藏
#2)添加laser led控制

#v2.1.36
#1)离线文件导入与选取点
#2)零度角校正逻辑
#3)加入jamooz通信协议
#4)加入高反表示文件导出与导入



#v2.1.37
#1) 直方图加入卷积处理并计算质心

#v2.2.1
#1) 改为9板校正方式-动态与静态校正，需要配合9板校正的固件


#v2.2.4
#1) 添加了去畸变前角度补偿

#v2.2.5
#1) 导轨高反标定系数

#v2.2.6
#1) 将误码信息打印的去掉
#2）读取校正信息

#v2.2.8
#*整理直方图算法
#-去掉开启菜单隐藏

#v2.2.9
#+点云累积功能

#v2.3.0
#+新增控件

#v2.3.1
#*优化串口数据解析逻辑
#+增加OTA功能

#v2.3.3
#+新增石头科技协议

#v2.3.4
#+新增ranging模式的切换

#v2.3.5
#+版本号读取扩展
#*寄存器读取修改逻辑

#v2.3.6
#-去掉文件导入点云前三个点滤除逻辑
#*调整零位包：零位包属于一圈的起始，遇到零位包需要将之前的数据更新，改包作为次更新的起始

#v2.3.7
#+添加解锁ack反馈显示
#+添加8kB OTA


#v2.3.9
#+nova A4数据解析排除

#v2.3.10
#*改为标定全局线性补偿
#+新增启动停止指令控制T5

#v2.3.11
#+新增追觅协议


