#ifndef IMPORT_POINTCLOUD_H
#define IMPORT_POINTCLOUD_H

#include <QWidget>
#include "serial_base.h"
#include <QTimer>

namespace Ui {
class ImportPointCloud;
}

class ImportPointCloud : public QWidget
{
    Q_OBJECT

public:
    explicit ImportPointCloud(QWidget *parent = nullptr);
    ~ImportPointCloud();
public slots:
     void TimeOut();

signals:
    void transmitPointCloudData(std::vector<ProtocolData> data);
    void transmitShowFileDataCmd(bool isFileData);

private slots:
    void on_pushButtonImportPointCloud_clicked();

    void on_pushButtonStart_clicked();

    void on_pushButtonStop_clicked();

    void on_radioButtonFileData_clicked();

    void on_horizontalSliderSetSpeed_sliderMoved(int position);

private:
    Ui::ImportPointCloud *ui;
    std::vector<ProtocolData>  replayData;
    bool isStartEmitData;
    uint16_t timeInterval;
    uint32_t  curDataCnt,totalDataCnt;
    QTimer *time=nullptr;
};

#endif // IMPORT_POINTCLOUD_H
