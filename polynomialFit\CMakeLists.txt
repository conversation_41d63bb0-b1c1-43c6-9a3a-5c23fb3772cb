# polynomialFit library CMakeLists.txt

# Source files for polynomialFit
set(POLYNOMIAL_FIT_SOURCES
    augmentedMatrix.cpp
    linearFit.cpp
    polynomialFit.cpp
)

# Header files for polynomialFit
set(POLYNOM<PERSON>L_FIT_HEADERS
    augmentedMatrix.h
    linearFit.h
    polynomialFit.h
)

# Create static library
add_library(polynomialFit STATIC
    ${POLYNOMIAL_FIT_SOURCES}
    ${POLYNOMIAL_FIT_HEADERS}
)

# Set include directories for this library
target_include_directories(polynomialFit PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link Qt5 if needed (uncomment if this module uses Qt)
# target_link_libraries(polynomialFit
#     Qt5::Core
# )
