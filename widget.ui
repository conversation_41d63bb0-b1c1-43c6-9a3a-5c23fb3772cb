<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>459</width>
    <height>202</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>459</width>
    <height>202</height>
   </size>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>140</y>
     <width>421</width>
     <height>51</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QComboBox" name="comboBox">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="comboBox_baud1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="editable">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string>open</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>210</y>
     <width>421</width>
     <height>51</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <widget class="QComboBox" name="comboBox_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QComboBox" name="comboBox_baud2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string>open</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QPushButton" name="pushButton_POINTCLOUD">
   <property name="geometry">
    <rect>
     <x>21</x>
     <y>31</y>
     <width>80</width>
     <height>80</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/new/prefix1/icon/logo/lidar.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_GREYMAP">
   <property name="geometry">
    <rect>
     <x>272</x>
     <y>31</y>
     <width>80</width>
     <height>80</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/new/prefix1/icon/logo/greymap2.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_CALIBRATION">
   <property name="geometry">
    <rect>
     <x>356</x>
     <y>31</y>
     <width>80</width>
     <height>80</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/new/prefix1/icon/logo/fit2.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_HISTONGRAM">
   <property name="geometry">
    <rect>
     <x>189</x>
     <y>31</y>
     <width>80</width>
     <height>80</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/new/prefix1/icon/logo/histogram.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="iconSize">
    <size>
     <width>99</width>
     <height>69</height>
    </size>
   </property>
   <property name="autoRepeat">
    <bool>false</bool>
   </property>
   <property name="autoExclusive">
    <bool>false</bool>
   </property>
   <property name="autoDefault">
    <bool>false</bool>
   </property>
   <property name="flat">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButton_DATABASE">
   <property name="geometry">
    <rect>
     <x>105</x>
     <y>31</y>
     <width>80</width>
     <height>80</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="styleSheet">
    <string notr="true">border-image: url(:/new/prefix1/icon/logo/database.png);</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <action name="action_his">
   <property name="text">
    <string>直方图</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
