#ifndef DYNAMIC_CALIBRATION_H
#define DYNAMIC_CALIBRATION_H

#include <QMainWindow>
#include "customPlot/qcustomplot.h"
#include <QTextCodec>
#include "serial_base.h"

namespace Ui {
class DynamicCalibration;
}

class DynamicCalibration : public QMainWindow
{
    Q_OBJECT

public:
    explicit DynamicCalibration(QWidget *parent = nullptr);
    ~DynamicCalibration();
    void SetupGui();
    void SetupTable();
    void ReceivePointCloudData(std::vector<ProtocolData> data);
    std::vector<float> CalcTriParam(std::vector<float> realDis,std::vector<float> sampleDis,std::vector<float> samplePeak);
    bool LeastSquareMethodLinearFit(std::vector<float> x,std::vector<float> y,std::vector<float> &param);
    bool LinearFit(std::vector<float> x,std::vector<float> y,std::vector<float> &param);
    bool Polynomial(std::vector<float> x,std::vector<float> y,std::vector<float> &param);
    bool CalcLinearMarkPoint(std::vector<float> param1,std::vector<float> param2,std::vector<float> &coordinate);
    bool AllLinearFitAndMarkpoint(std::vector<float> x,std::vector<float> y);
    bool nterpolationLookupTable(std::vector<float> x,std::vector<float> y,std::vector<float> table);

    enum {kMiniMode = 0,kTofMode = 1};

signals:
    void TransmitSerialCmd(QByteArray data);
    void TransmitCalibrationData(std::vector<float> data);
    void TransmitCompensation(bool isExe,std::vector<float> samplePoint);
    void TransmitSamplePoint(std::vector<float> hr,std::vector<float> wt,std::vector<float> bk,std::vector<int> dis,uint type);

private slots:
    void on_comboBoxMode_currentIndexChanged(int index);

    void on_pushButtonDynamicCalibration_clicked();

    void on_actionTriTransmit_triggered();

    void on_actionTriClear_triggered();

    void on_actionTofTransmit_triggered();

    void on_actionTofClear_triggered();

    void on_pushButtonStaticCalibration_clicked();

    void on_actionImptParam_triggered();

    void on_actionExptParam_triggered();

    void on_actioncompensationAngle_triggered();

    void on_actionSamplePoint_triggered();

    void on_actionCompareData_triggered();

private:
    Ui::DynamicCalibration *ui;
    QCPGraph *subGraphRando = nullptr;
    QCPGraph *subGraphRandoPoint = nullptr;
    QCPAxisRect *subRectLeft = nullptr;

    int currentMode;
    std::vector<float> sampleAngle;
    std::vector<std::vector<float>> sampleDistance;
    std::vector<std::vector<uint16_t>> sampleIntensity;

    bool isStartSample;
    uint16_t currentSampleCnt;

    double triParamA,triParamB,triParamC;
    double triParamP2,triParamP1,triParamP0;

    std::vector<std::vector<float>> tofLinearFitParam;
    std::vector<std::vector<float>> tofLinearFitMark;
    uint maxPeak,minPeak;
    float offsetAng;
    float angle_offset;
};

#endif // DYNAMIC_CALIBRATION_H
