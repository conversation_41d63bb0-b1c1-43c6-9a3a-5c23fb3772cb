<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DynamicCalibration</class>
 <widget class="QMainWindow" name="DynamicCalibration">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1011</width>
    <height>606</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_2" stretch="5,2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="2,3">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout" stretch="1,9">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="label">
              <property name="text">
               <string>CalibrationMode</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="comboBoxMode">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QTableWidget" name="tableWidget">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="rowCount">
             <number>18</number>
            </property>
            <property name="columnCount">
             <number>4</number>
            </property>
            <attribute name="horizontalHeaderVisible">
             <bool>false</bool>
            </attribute>
            <attribute name="horizontalHeaderDefaultSectionSize">
             <number>88</number>
            </attribute>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <row/>
            <column/>
            <column/>
            <column/>
            <column/>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QCustomPlot" name="widget" native="true"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0,0">
        <property name="spacing">
         <number>5</number>
        </property>
        <item>
         <widget class="QTextEdit" name="textEditDisLog">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTextEdit" name="textEditPeakLog">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0" colspan="2">
           <widget class="QRadioButton" name="radioButton">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Rolling Sample Data</string>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="pushButtonStaticCalibration">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>StaticCalibration</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="pushButtonDynamicCalibration">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>DynamicCalibration</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="toolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionTriTransmit"/>
   <addaction name="actionTriClear"/>
   <addaction name="separator"/>
   <addaction name="actionTofTransmit"/>
   <addaction name="actionTofClear"/>
   <addaction name="separator"/>
   <addaction name="actionImptParam"/>
   <addaction name="actionExptParam"/>
   <addaction name="separator"/>
   <addaction name="actioncompensationAngle"/>
   <addaction name="actionSamplePoint"/>
   <addaction name="actionCompareData"/>
  </widget>
  <action name="actionTriTransmit">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/share.png</normaloff>:/new/prefix1/icon/logo/share.png</iconset>
   </property>
   <property name="text">
    <string>TriTransmit</string>
   </property>
   <property name="toolTip">
    <string>三角法参数发送</string>
   </property>
  </action>
  <action name="actionTriClear">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/delete.png</normaloff>:/new/prefix1/icon/logo/delete.png</iconset>
   </property>
   <property name="text">
    <string>TriClear</string>
   </property>
   <property name="toolTip">
    <string>三角法参数清除</string>
   </property>
  </action>
  <action name="actionTofTransmit">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/share.png</normaloff>:/new/prefix1/icon/logo/share.png</iconset>
   </property>
   <property name="text">
    <string>TofTransmit</string>
   </property>
   <property name="toolTip">
    <string>tof参数发送</string>
   </property>
  </action>
  <action name="actionTofClear">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/delete.png</normaloff>:/new/prefix1/icon/logo/delete.png</iconset>
   </property>
   <property name="text">
    <string>TofClear</string>
   </property>
   <property name="toolTip">
    <string>tof参数清除</string>
   </property>
  </action>
  <action name="actionImptParam">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/import.png</normaloff>:/new/prefix1/icon/logo/import.png</iconset>
   </property>
   <property name="text">
    <string>ImptParam</string>
   </property>
   <property name="toolTip">
    <string>导入校正参数</string>
   </property>
  </action>
  <action name="actionExptParam">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/download2.png</normaloff>:/new/prefix1/icon/logo/download2.png</iconset>
   </property>
   <property name="text">
    <string>ExptParam</string>
   </property>
   <property name="toolTip">
    <string>导出参数</string>
   </property>
  </action>
  <action name="actioncompensationAngle">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/revert.png</normaloff>:/new/prefix1/icon/logo/revert.png</iconset>
   </property>
   <property name="text">
    <string>compensationAngle</string>
   </property>
   <property name="toolTip">
    <string>补偿零度角</string>
   </property>
  </action>
  <action name="actionSamplePoint">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/scan5.png</normaloff>:/new/prefix1/icon/logo/scan5.png</iconset>
   </property>
   <property name="text">
    <string>SampleData</string>
   </property>
   <property name="toolTip">
    <string>开始采集数据</string>
   </property>
  </action>
  <action name="actionCompareData">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/searchOnce.png</normaloff>:/new/prefix1/icon/logo/searchOnce.png</iconset>
   </property>
   <property name="text">
    <string>CompareData</string>
   </property>
   <property name="toolTip">
    <string>采集数据并比对</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="logo2.qrc"/>
 </resources>
 <connections/>
</ui>
