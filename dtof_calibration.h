#ifndef DTOF_CALIBRATION_H
#define DTOF_CALIBRATION_H

#include <iostream>
#include <stdio.h>
#include <vector>
#include <numeric>  // 正确的头文件

int multiCalibrationProcess(std::vector<std::vector<std::vector<float>>> dataHr,std::vector<std::vector<std::vector<float>>> dataWt,std::vector<std::vector<std::vector<float>>> dataBk,std::vector<float> realDis,uint8_t type,std::vector<float> *calbParam);

#endif // DTOF_CALIBRATION_H
