#include "saveas_capture.h"
#include "ui_saveascapture.h"

SaveAsCapture::SaveAsCapture(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::SaveAsCapture)
{
    ui->setupUi(this);
    // 注册plot document对象
    plotObjectHandler = new QCPDocumentObject(this);
    ui->textEditScreenShot->document()->documentLayout()->registerHandler(QCPDocumentObject::PlotTextFormat, plotObjectHandler);
}

SaveAsCapture::~SaveAsCapture()
{
    delete ui;
    /*if(customPlot != nullptr) {
        delete customPlot;//外部传进来  不可以delete
    }*/
    if(plotObjectHandler != nullptr) {
        delete plotObjectHandler;
    }
}


void SaveAsCapture::getCustomPlot(QCustomPlot *plot)
{
    customPlot = plot;
}

/*

 */
/*

 */

void SaveAsCapture::on_pushButtonClearAll_clicked()
{
    ui->textEditScreenShot->clear();
}

void SaveAsCapture::on_pushButtonSavePdf_clicked()
{
    QString fileName = QFileDialog::getSaveFileName(this, "Save document...", qApp->applicationDirPath(), "*.pdf");
    if (!fileName.isEmpty())
    {
        QPrinter printer;
        printer.setOutputFormat(QPrinter::PdfFormat);
        printer.setOutputFileName(fileName);
        QMargins pageMargins(10, 10, 10, 10);
        QPageLayout pageLayout;
        pageLayout.setMode(QPageLayout::StandardMode);
        pageLayout.setOrientation(QPageLayout::Portrait);
        pageLayout.setPageSize(QPageSize(QPageSize::A4));
        pageLayout.setUnits(QPageLayout::Millimeter);
        pageLayout.setMargins(QMarginsF(pageMargins));
        printer.setPageLayout(pageLayout);
        ui->textEditScreenShot->document()->setPageSize(printer.pageRect().size());//
        ui->textEditScreenShot->document()->print(&printer);
    }
}

void SaveAsCapture::on_pushButtonInsert_clicked()
{
    QTextCursor cursor = ui->textEditScreenShot->textCursor();
    cursor.insertText("\n");
    cursor.insertText(QString(QChar::ObjectReplacementCharacter), QCPDocumentObject::generatePlotFormat(customPlot, customPlot->width(), customPlot->height()));
    cursor.insertText("\n                ");
    ui->textEditScreenShot->setTextCursor(cursor);
}
