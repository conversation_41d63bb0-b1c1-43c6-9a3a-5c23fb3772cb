<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CalibrationChart</class>
 <widget class="QMainWindow" name="CalibrationChart">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1327</width>
    <height>698</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>calibration</string>
  </property>
  <property name="windowIcon">
   <iconset resource="logo2.qrc">
    <normaloff>:/new/prefix1/icon/logo/fit2.png</normaloff>:/new/prefix1/icon/logo/fit2.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="5,2">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_3" stretch="5,3">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QCustomPlot" name="widgetCalibration" native="true"/>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="4,1">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QTextEdit" name="textEdit_TOF_PEAK">
            <property name="styleSheet">
             <string notr="true">font: 25 10pt &quot;Microsoft YaHei&quot;;</string>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <widget class="QPushButton" name="pushButton_SAVE_LOG">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>saveLog</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_CLEAR_LOG">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>StopLog</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_FILE_NAME">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4" stretch="2,3,5">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QPushButton" name="stopCMD">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Agency FB</family>
                <pointsize>20</pointsize>
                <weight>50</weight>
                <italic>false</italic>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 12, 44);
font: 20pt &quot;Agency FB&quot;;</string>
              </property>
              <property name="text">
               <string>STOP</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="resetCMD">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(0, 85, 0);
font: 20pt &quot;Agency FB&quot;;</string>
              </property>
              <property name="text">
               <string>RESET</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="label">
              <property name="text">
               <string>OFFSET:(mm)</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="offset">
              <property name="text">
               <string>50</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="title">
           <string>setp over:</string>
          </property>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>50</x>
             <y>30</y>
             <width>281</width>
             <height>151</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <widget class="QLabel" name="label_2">
                <property name="text">
                 <string>setting Dis(mm)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="settingDistance">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_3">
                <property name="text">
                 <string>setting Spd(mm/s)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="settingSpeed">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QPushButton" name="RunDis">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">font: 20pt &quot;Agency FB&quot;;</string>
              </property>
              <property name="text">
               <string>RUN</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="title">
           <string>auto run</string>
          </property>
          <widget class="QTableView" name="tableView">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>80</y>
             <width>321</width>
             <height>151</height>
            </rect>
           </property>
          </widget>
          <widget class="QPushButton" name="exeRun">
           <property name="geometry">
            <rect>
             <x>218</x>
             <y>243</y>
             <width>121</width>
             <height>71</height>
            </rect>
           </property>
           <property name="text">
            <string>RUN</string>
           </property>
          </widget>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>240</y>
             <width>191</width>
             <height>71</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="label_4">
              <property name="text">
               <string>exeTimes:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="label_5">
              <property name="text">
               <string>fileName</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLineEdit" name="exeNum">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="saveFileName">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>30</y>
             <width>321</width>
             <height>32</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="text">
               <string>add SamplePoint</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="samplePointDis">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="addSamplePoint">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>add</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="logo2.qrc"/>
 </resources>
 <connections/>
</ui>
