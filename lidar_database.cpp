#include "lidar_database.h"
#include "ui_lidardatabase.h"
#include <iostream>

using namespace std;


void task0(void)
{
    for(uint m=0; m<10; m++) {
        qDebug()<<"task0...";
    }
}
void task9(void)
{
    qDebug()<<"task9...";
}


LidarDatabase::LidarDatabase(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::LidarDatabase),
    tCmd(0x05),
    rCmd(0),
    tId(0xA1),
    rId(0),
    isStartReadParam(false),
    isStartReadCalibration(false),
    isMarch(true),
    paramCnt(0),
    lastParamCnt(0),
    readParamIndex(0),
    timeCount(0)
{
    ui->setupUi(this);
//    ui->pushButtonTransmit->setEnabled(false);
    //taskList.taskFlag.resize(10);
    //taskList.functionPtr.resize(10);

   /* taskList.isExeTask = true;
    taskList.lastCnt = 0;
    taskList.taskFlag[KStarTask0/2].isExeCmd = (KStarTask0%2) == 0 ? true : false;
    taskList.taskFlag[KStarTask0/2].isWaitAck = false;
    taskList.taskFlag[KStarTask0/2].isLockExe = false;

    taskList.taskFlag[KStarTask9/2].isExeCmd = (KStarTask9%2) == 0 ? true : false;
    taskList.taskFlag[KStarTask9/2].isWaitAck = false;
    taskList.taskFlag[KStarTask9/2].isLockExe = false;

    for(uint m=0; m<10; m++) {
        taskList.functionPtr[m] = NULL;
    }
    taskList.functionPtr[0] = &task0;
    taskList.functionPtr[9] = &task9;*/

    GuiSetup();
    SqlSetup();
    readParamFlag.allBits = false;
}


void LidarDatabase::GuiSetup()
{
    /*cmd组合框*/
    connect(ui->radioButtonWrite,SIGNAL(clicked(bool)),this,SLOT(CmdSelectSlots()));
    connect(ui->radioButtonRead,SIGNAL(clicked(bool)),this,SLOT(CmdSelectSlots()));
    /*id单选框*/
    connect(ui->radioButtonTofMode,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonTriMode,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonControlFreq,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonCalibration,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonZeroAngle,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonRegister,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonMcuID,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonTemperature,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonMeasureRange,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonVersionBaud,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonTG,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonReflectivity,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonDebug1,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonDebug2,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonDebug3,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonDebug4,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonLaser,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonLed,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonReserve3,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));
    connect(ui->radioButtonReserve4,SIGNAL(clicked(bool)),this,SLOT(IdSelectSlots()));

    ui->comboBoxRunningMode->addItem("Standby");
    ui->comboBoxRunningMode->addItem("Histogram");
    ui->comboBoxRunningMode->addItem("Greymap");
    ui->comboBoxRunningMode->addItem("Scan");
    ui->comboBoxRunningMode->addItem("Calibration");
    ui->comboBoxRunningMode->addItem("SinglePoint");
    ui->comboBoxRunningMode->addItem("Ranging");

}

void LidarDatabase::SqlSetup()
{
    //创建名为 firstConnect 的数据库连接
    CreateConnectionByName("QSqliteConnect");
    //获取数据库
    db = GetConnectionByName("QSqliteConnect");

    /*********************************************/
    curItemModel = new QStandardItemModel();
     /* 设置列数 */
    curItemModel->setColumnCount(kParamTitleCnt);
    for(uint16_t m=0; m<kParamTitleCnt; m++) {
        curItemModel->setHeaderData(m, Qt::Horizontal, paramTitle[m]);
    }

    /* 设置行数 */
    curItemModel->setRowCount(2);
    curItemModel->setHeaderData(0, Qt::Vertical, "transmit");
    curItemModel->setHeaderData(1, Qt::Vertical, "reception");
    /******other tableview*******/
    ui->tableViewLidarInfomation->setModel(curItemModel);
    /* 设置列宽在可视界面自适应宽度 */
    ui->tableViewLidarInfomation->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableViewLidarInfomation->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(192, 192, 192);}");

    /* 行颜色交替显示 */
    ui->tableViewLidarInfomation->setAlternatingRowColors(true);
    /* 不允许在图形界面修改内容 */
    //ui->tableViewCurLidar->setEditTriggers(QAbstractItemView::NoEditTriggers);

    /* 显示表 */
    ui->tableViewLidarInfomation->show();
    /****************************************************/
    model = new QSqlTableModel(this,db);
    model->setEditStrategy(QSqlTableModel::OnManualSubmit);
    if(db.tables().contains(kTableName)) {
        model->setTable(kTableName);//显示表头 字段
    }
    else {
        QSqlQuery query(db);
        if(!query.exec("create table lidarparam(\
                      McuId         varchar,\
                      Mode          varchar,\
                      Temperature   varchar,\
                      Register      varchar,\
                      Version       varchar,\
                      Baud          varchar,\
                      Date          varchar,\
                      ZeroAngle     float,\
                      Cal_p02 float,\
                      Cal_p01 float,\
                      Cal_p00 float,\
                      Cal_mk0 float,\
                      Cal_p12 float,\
                      Cal_p11 float,\
                      Cal_p10 float,\
                      Cal_mk1 float,\
                      Cal_p22 float,\
                      Cal_p21 float,\
                      Cal_p20 float,\
                      Cal_mk2 float,\
                      Cal_p32 float,\
                      Cal_p31 float,\
                      Cal_p30 float,\
                      Cal_mk3 float,\
                      Cal_p42 float,\
                      Cal_p41 float,\
                      Cal_p40 float,\
                      Cal_mk4 float,\
                      Cal_p52 float,\
                      Cal_p51 float,\
                      Cal_p50 float,\
                      Cal_mk5 float,\
                      Cal_p62 float,\
                      Cal_p61 float,\
                      Cal_p60 float,\
                      Cal_mk6 float,\
                      Cal_p72 float,\
                      Cal_p71 float,\
                      Cal_p70 float,\
                      Cal_mk7 float,\
                      Tg_p2   float,\
                      Tg_p1   float,\
                      Tg_p0     float,\
                      Refle_p03 float,\
                      Refle_p02 float,\
                      Refle_p01 float,\
                      Refle_p00 float,\
                      Refle_p13 float,\
                      Refle_p12 float,\
                      Refle_p11 float,\
                      Refle_p10 float,\
                      Refle_p23 float,\
                      Refle_p22 float,\
                      Refle_p21 float,\
                      Refle_p20 float,\
                      RangeLow       varchar,\
                      RangeUp        varchar,\
                      His_freq       varchar,\
                      Facula_freq    varchar,\
                      Greymap     blob,\
                      Histogram   blob,\
                      Calibration blob,\
                      Debug1 varchar,\
                      Debug2 varchar,\
                      Debug3 varchar,\
                      Debug4 varchar,\
                      Image1 blob,\
                      Image2 blob,\
                      Image3 blob,\
                      Image4 blob,\
                      Image5 blob,\
                      Image6 blob,\
                      Image7 blob,\
                      Image8 blob,\
                      Image9 blob,\
                      Image10 blob,\
                      Laser,\
                      Led )")) {


             qDebug() << "create table error";
        }
        model->setTable(kTableName);

    }

    ui->tableViewLidarDatabase->setModel(model);
    ui->tableViewLidarDatabase->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableViewLidarDatabase->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(192, 192, 192);}");


    /* 行颜色交替显示 */
    ui->tableViewLidarDatabase->setAlternatingRowColors(true);
    /* 不允许在图形界面修改内容 */
    ui->tableViewLidarDatabase->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableViewLidarDatabase->show();



    /*table 行统计*/
    std::vector<uint8_t> tmpRowCnt;
    tmpRowCnt.push_back(kMcuIdRowCnt);
    tmpRowCnt.push_back(kSystemModeRowCnt);
    tmpRowCnt.push_back(kTemperatureRowCnt);
    tmpRowCnt.push_back(KRegisterRowCnt);   
    tmpRowCnt.push_back(kVersionBaudRowCnt);
    tmpRowCnt.push_back(kDateRowCnt);
    tmpRowCnt.push_back(KZeroAngleRowCnt);
    tmpRowCnt.push_back(KCalibrationRowCnt);
    tmpRowCnt.push_back(kTgRowCnt);
    tmpRowCnt.push_back(KRefleRowCnt);
    tmpRowCnt.push_back(kMeasureRangeRowCnt);
    tmpRowCnt.push_back(KHisFacFreqRowCnt);
    tmpRowCnt.push_back(KGreymapImageRowCnt);
    tmpRowCnt.push_back(KHistogramImageRowCnt);
    tmpRowCnt.push_back(KCalibrationImageRowCnt);
    tmpRowCnt.push_back(KDebug1RowCnt);
    tmpRowCnt.push_back(KDebug2RowCnt);
    tmpRowCnt.push_back(KDebug3RowCnt);
    tmpRowCnt.push_back(KDebug4RowCnt);
    tmpRowCnt.push_back(KImageRowCnt);
    tmpRowCnt.push_back(KLaserRowCnt);
    tmpRowCnt.push_back(KLedRowCnt);

    IntegralItemRowCnt.push_back(0);
    for(uint8_t n=0; n<tmpRowCnt.size(); n++) {
        IntegralItemRowCnt.push_back(tmpRowCnt.at(n) + IntegralItemRowCnt.at(n));
    }

    tableWidgetRowNum =  IntegralItemRowCnt.at(IntegralItemRowCnt.size()-1);/*获取总行数*/

    tableRowCntMap = {
        {"McuId" ,                  IntegralItemRowCnt.at(0)},
        {"SysMode",                 IntegralItemRowCnt.at(1)},
        {"Temperature",             IntegralItemRowCnt.at(2)},
        {"Register",                IntegralItemRowCnt.at(3)},      
        {"VerBaud",                 IntegralItemRowCnt.at(4)},
        {"Date",                    IntegralItemRowCnt.at(5)},
        {"ZeroAngle",               IntegralItemRowCnt.at(6)},
        {"Calibration",             IntegralItemRowCnt.at(7)},
        {"Tg",                      IntegralItemRowCnt.at(8)},
        {"Refle",                   IntegralItemRowCnt.at(9)},
        {"Measure",                 IntegralItemRowCnt.at(10)},
        {"ControlFreq",             IntegralItemRowCnt.at(11)},
        {"GreymapImage",            IntegralItemRowCnt.at(12)},
        {"HistImage",               IntegralItemRowCnt.at(13)},
        {"CaliImage",               IntegralItemRowCnt.at(14)},
        {"Degug1",                  IntegralItemRowCnt.at(15)},
        {"Degug2",                  IntegralItemRowCnt.at(16)},
        {"Degug3",                  IntegralItemRowCnt.at(17)},
        {"Degug4",                  IntegralItemRowCnt.at(18)},
        {"Image",                   IntegralItemRowCnt.at(19)},
        {"Laser",                   IntegralItemRowCnt.at(20)},
        {"Led",                     IntegralItemRowCnt.at(21)},
        {"Vbd",                     IntegralItemRowCnt.at(21)},
    };




    /*模式映射*/
    modeMap =  {
        {kTofStopMode,"Standby"},
        {kTofTestMode,"Histogram"},
        {kTofFaculaMode,"Greymap"},
        {kTofScanMode,"Scan"},
        {ktRICalibrationMode,"Calibration"},
        {kTofSinglePointMode,"SinglePoint"},
        {kTofRangingMode,"Ranging"},
    };
    /*波特率映射*/
    baudMap =  {
        {0,57600},
        {1,115200},
        {2,153600},
        {3,230400},
        {4,460800},
    };
    timer = new QTimer();
    timer->start(20);
    connect(timer, &QTimer::timeout, this, &LidarDatabase::timeOut);

}

void LidarDatabase::timeOut()
{
    paramCnt++;
    timeCount++;
    if(isStartReadParam == true) {
        if(readParamIndex == 0) {// && readParamFlag.bit.readMcuID == false && (lastParamCnt - paramCnt) > 2000) {
            ReadParamThroughId(kMcuId);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 1) {
            if(readParamFlag.bit.readMcuID == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readMcuID error!");
            }
            else if(readParamFlag.bit.readMcuID == true) {
               readParamIndex = 2;
            }
        }
        else if(readParamIndex == 2) {
            ReadParamThroughId(kVersionBuad);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 3) {
            if(readParamFlag.bit.readVersion == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readVersion error!");
            }
            else if(readParamFlag.bit.readVersion == true) {
               readParamIndex = 4;
            }
        }
        else if(readParamIndex == 4) {
            ReadParamThroughId(kZeroAngleSetting);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 5) {
            if(readParamFlag.bit.readZeroAngle == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readZeroAngle error!");
            }
            else if(readParamFlag.bit.readVersion == true) {
               readParamIndex = 6;
            }
        }
        else if(readParamIndex == 6) {
            ReadParamThroughId(kCalibrationParam);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 7) {
            if(readParamFlag.bit.readCalibration == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readCalibrationParam error!");
            }
            else if(readParamFlag.bit.readCalibration == true) {
               readParamIndex = 8;
            }
        }
        else if(readParamIndex == 8) {
            ReadParamThroughId(kReflelParam);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 9) {
            if(readParamFlag.bit.readReflectivity == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readReflelParam error!");
            }
            else if(readParamFlag.bit.readReflectivity == true) {
               readParamIndex = 10;
            }
        }
        else if(readParamIndex == 10) {
            ReadParamThroughId(kMeasureRange);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 11) {
            if(readParamFlag.bit.readRange == false && (lastParamCnt - paramCnt) > 1000) {
               isStartReadParam = false;
               QMessageBox::warning(NULL, "warning", "readRange error!");
            }
            else if(readParamFlag.bit.readRange == true) {
               readParamIndex = 12;
               isStartReadParam = false;
               int rowNum=0;
               rowNum = model->rowCount(); //获得表的行数
               model->insertRow(rowNum); //添加一行
               for(uint8_t m=0; m<kParamTitleCnt; m++) {
                   if((m >= tableRowCntMap.at("GreymapImage") && m <= tableRowCntMap.at("CaliImage"))
                      || (m >= tableRowCntMap.at("Image") ))
                   {
                      if(curItemModel->index(1,m).data().toString() != "") {
                          model->setData(model->index(rowNum,m),QVariant(curItemModel->index(1,m).data()));
                          //qDebug()<< curItemModel->index(1,m).data();
                      }
                   }
                   else {
                      model->setData(model->index(rowNum,m),curItemModel->index(1,m).data().toString());
                   }
               }
            }
        }
    }
    else if(isStartReadCalibration == true && (paramCnt - lastParamCnt) > 20) {
        if(readParamIndex == 0) {
            ReadParamThroughId(kCalibrationParam);
            readParamIndex++;
            lastParamCnt = paramCnt;
        }
        else if(readParamIndex == 1) {
            if(readParamFlag.bit.readCalibration == false && (paramCnt-lastParamCnt) > 1000) {
               isStartReadCalibration = false;
               QMessageBox::warning(NULL, "warning", "readCalibrationParam error!");
            }
            else if(isMarch == false) {
                readParamIndex = 0;
                isStartReadCalibration = false;
                QMessageBox::information(NULL, "error", "read calibration is not correct!");
            }
            else if(readParamFlag.bit.readCalibration == true && isMarch == true) {
               readParamIndex = 0;
               isStartReadCalibration = false;
               QMessageBox::information(NULL, "ok", "read calibration is correct!");
            }
        }

    }

   /*if(taskList.isExeTask == true) {
        for(uint m=0; m<taskList.taskFlag.size(); m++) {
            if(taskList.taskFlag[m].isExeCmd == true && taskList.taskFlag[m].isLockExe == false) {
                //execute task;
                taskList.functionPtr[m]();
                taskList.taskFlag[m].isLockExe  = true;
                taskList.lastCnt = timeCount;
                break;
            }

            if(taskList.taskFlag[m].isExeCmd == true)
            {
                if(taskList.taskFlag[m].isWaitAck == false || timeCount - taskList.lastCnt > 1000) {
                    taskList.isExeTask = false;
                    taskList.taskFlag[m].isExeCmd = false;
                    QMessageBox::warning(NULL, "warning", QString::number(m,10)+" task is error");
                }
                else if(taskList.taskFlag[m].isWaitAck == true){
                    taskList.taskFlag[m].isExeCmd = false;
                }
                break;
            }
        }
    }
    */
}

LidarDatabase::~LidarDatabase()
{
    delete ui;
}

void LidarDatabase::CreateConnectionByName(const QString &connectionName){

    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
    // 数据库连接需要设置的信息
    db.setHostName("127.0.0.1"); // 数据库服务器IP，本地电脑
    db.setDatabaseName("database.db");// 数据库名
    db.setUserName("root");// 用户名
    db.setPassword("123456");// 密码
    db.setPort(8080);// 端口号

    // 连接数据库判断
    isConnectioned = db.open();

    if (isConnectioned){
        qDebug() << "database connect is ok";
    } else {
        qDebug() << "database connect is fail";
    }

}

QSqlDatabase LidarDatabase::GetConnectionByName(const QString &connectionName) {
    // 获取数据库连接
    return QSqlDatabase::database(connectionName);
}

void LidarDatabase::CmdSelectSlots(void)
{
    bool h2d = true;
    bool d2h = false;
    bool write = ui->radioButtonWrite->isChecked();
    bool read = ui->radioButtonRead->isChecked();
    bool reserve = false;
    tCmd = (h2d&0x01) | (d2h<<1&0x02) | (write<<2&0x04) |   (read<<3&0x08) | (reserve<<7&0x80);

}

void LidarDatabase::IdSelectSlots()
{
    if(ui->radioButtonMcuID->isChecked()) {
        tId = kMcuId;
    }
    else if(ui->radioButtonTofMode->isChecked()) {
        tId = kTofMode;
    }
    else if(ui->radioButtonTriMode->isChecked()) {
        tId = kTriMode;
    }
    else if(ui->radioButtonTG->isChecked()) {
        tId = kTgParam;
    }
    else if(ui->radioButtonTemperature->isChecked()) {
        tId = kTemperature;
    }
    else if(ui->radioButtonVersionBaud->isChecked()) {
        tId = kVersionBuad;
    }
    else if(ui->radioButtonMeasureRange->isChecked()) {
        tId = kMeasureRange;
    }
    else if(ui->radioButtonReflectivity->isChecked()) {
        tId = kReflelParam;
    }
    else if(ui->radioButtonCalibration->isChecked()) {
        tId = kCalibrationParam;
    }
    else if(ui->radioButtonControlFreq->isChecked()) {
        tId = kHisAndFacula;
    }
    else if(ui->radioButtonTriMode->isChecked()) {
        tId = kTriMode;
    }
    else if(ui->radioButtonRegister->isChecked()) {
        tId = kRegisterSetting;
    }
    else if(ui->radioButtonZeroAngle->isChecked()) {
        tId = kZeroAngleSetting;
    }
    else if(ui->radioButtonLaser->isChecked()) {
        tId = kLaserControl;
    }
    else if(ui->radioButtonLed->isChecked()) {
        tId = kLedControl;
    }
    else if(ui->radioButtonReserve3->isChecked()) {
        tId = kVbdControl;
    }
    else if(ui->radioButtonDebug1->isChecked()) {
        tId = kDebugSetting;
    }
    else if(ui->radioButtonDebug2->isChecked()) {
        tId = kDebugSetting2;
    }
    else if(ui->radioButtonDebug3->isChecked()) {
        tId = kDebugSetting3;
    }
    else if(ui->radioButtonDebug4->isChecked()) {
        tId = kDebugSetting4;
    }
    else if(ui->radioButtonReserve4->isChecked()) {
        tId = kXtalk;
    }
}

void LidarDatabase::FeedbackInfoLidar(QByteArray fdb)
{
    QByteArray tmpFdb;//
    uint8_t *tmp = (uint8_t *)fdb.data();
    tmpFdb.append((char*)&tmp[1],2);
    tmpFdb.append((char*)&tmp[6],fdb.size()-6);

    //uint8_t *data = (uint8_t *)fdb.data();
    uint8_t *data = (uint8_t *)tmpFdb.data();

    QDateTime curDateTime=QDateTime::currentDateTime();

    if(ui->textEditLog->toPlainText().length() > 102400) {
        ui->textEditLog->clear();
    }
    if(fdb.size() > 2 && data[0] == 0xF1 && data[1] == 0xF1) {
        return;
    }
    ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ") + "<- read pack: " + fdb.toHex());


    /*将反馈的参数填写到参数框中*/
    /*if(fdb.size() < 3 && data[1] == kTofMode) {
        return;
    }*/
    if(tmpFdb.size() < 3 && data[1] == kTofMode) {
        return;
    }
    if(data[1] == kTriMode || data[1] == kTofMode) {
        uint16_t mode = data[2] | (data[3]<<8);
        curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("SysMode")),modeMap.at(mode));
        //ui->tableWidget->item(tableRowCntMap.at("systemMode"),1)->setText(modeMap.at(mode));

    }
    else if(data[1] == kSysParam) {
        /*需要将所有的有关参数更新一遍*/
    }
    else if(data[1] == kMcuId) {
        QString st;
        if(tmpFdb.size() == (kMcuIdRowCnt*12 + 2)) {
           for(uint8_t n=13; n>1; n--) {
               st += QString("%1").arg(data[n],2,16, QLatin1Char('0'));
           }
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("McuId")),st);
           readParamFlag.bit.readMcuID = isStartReadParam == true ? true : false;
        }
    }
    else if(data[1] == kVersionBuad) {
        QString st;
        if(tmpFdb.size() <30) { //== (kVersionBaudRowCnt*4 + 2)) {
           st = QString::number(data[2],10)+"."+QString::number(data[3],10)+"."
                +QString("%1").arg(data[4],2,16, QLatin1Char('0'))+"."
                +QString::number(data[5],10)+" "
                +QString::number(data[6],10)+"-"+QString::number(data[7],10)+"-"+QString::number(data[8],10)+"-"+QString::number(data[9],10) \
                +QString::number(data[10],10)+"-"+QString::number(data[11],10)+"-"+QString::number(data[12],10)+"-"+QString::number(data[13],10)+"-" \
                +QString::number(data[14],10)+"-"+QString::number(data[15],10);
                ;
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("VerBaud")),st);
           QString _qBaud = "";
           if(data[9] == 0) {
                _qBaud = "230400";
           }
           else {
                _qBaud = "250000";
           }
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("VerBaud")+1),_qBaud);
           readParamFlag.bit.readVersion = isStartReadParam == true ? true : false;
        }
        else {
           for(int m=6; m<fdb.size(); m++) {
                st.push_back((char)fdb[m]);
           }
            QString _qBaud = "";
            uint16_t index = fdb.length()-2;
            if(st[index] == "0") {
                 _qBaud = "230400";
            }
            else {
                 _qBaud = "250000";
            }
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("VerBaud")+1),_qBaud);
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("VerBaud")),st);
           readParamFlag.bit.readVersion = isStartReadParam == true ? true : false;
        }
    }
    else if(data[1] == kMeasureRange) {
        QString st;
        if(tmpFdb.size() == (kMeasureRangeRowCnt*2 + 2)) {
           st = QString("%1").arg(data[3]<<8 | data[2],2,10);
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Measure")),st);
           st = QString("%1").arg(data[5]<<8 | data[4],2,10);
           curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Measure")+1),st);
           readParamFlag.bit.readRange = isStartReadParam == true ? true : false;
        }
    }
    else if(data[1] == kCalibrationParam) {
        QString st;
        if(tmpFdb.size() == (KCalibrationRowCnt*4 + 2)) {
          float p = 0;
          for(uint16_t m=0; m<KCalibrationRowCnt; m++) {
            memcpy(&p,&data[m*4+2],4);
            st = QString::number(p,'f',8);
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Calibration")+m),st);
            readParamFlag.bit.readCalibration = isStartReadParam == true ? true : false;
          }
        }
        else if(tmpFdb.size() == (1024 + 2) && tofCalibParamLast.size() != 0) {
            float p = 0;
            if(isStartReadCalibration == true) {
                for(uint16_t m=0; m<256; m++) {
                  memcpy(&p,&data[m*4+2],4);
                  if(qAbs(tofCalibParamLast[m] - p) > 0.01) {
                        isMarch = false;
                  }
                }
                if(isMarch == true) {
                    readParamFlag.bit.readCalibration = isStartReadCalibration == true ? true : false;
                }
                else {
                    readParamFlag.bit.readCalibration = isStartReadCalibration == true ? false : false;
                }
                tofCalibParamLast.clear();
            }
        }
        else if(tmpFdb.size() == (1024 + 2) && tofCalibParamLast.size() == 0) {
            if(isStartReadCalibration == false) {
                QByteArray tmp = tmpFdb;
                tmp.remove(0,2);
                emit TransmitZeroAngle(4,tmp,0);
                float value = 0;
                tofCalibParam.clear();
                int nn=0;
                for(uint mm=0; mm<1024; mm += 4) {
                    memcpy(&value,&(tmp.data()[mm]),4);
                    tofCalibParam.push_back(value);
                    if(nn == 8) {
                        nn = 0;
                        std::cout<<std::endl;
                    }
                    if(nn<8) {

                        std::cout<<value<<", ";
                    }

                    nn++;
                }

            }
        }
        else {

               if(tofCalibParamLast.size() != 0) {
                isStartReadCalibration = true;
               }
               lastParamCnt = paramCnt;
               emit TransmitZeroAngle(3,0,0);
        }
    }
    else if(data[1] == kTgParam) {
        QString st;
        if(tmpFdb.size() == (kTgRowCnt*4 + 2)) {
          float p = 0;
          memcpy(&p,&data[2],4);
          st = QString::number(p,'f',8);
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Tg")),st);
          memcpy(&p,&data[6],4);
          st = QString::number(p,'f',8);
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Tg")+1),st);
          memcpy(&p,&data[10],4);
          st = QString::number(p,'f',8);
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Tg")+2),st);

        }
    }
    else if(data[1] == kReflelParam) {
            QString st;
            if(tmpFdb.size() == (KRefleRowCnt*4 + 2)) {
              float p = 0;

              for(uint16_t m=0; m<KRefleRowCnt; m++) {
                memcpy(&p,&data[m*4+2],4);
                st = QString::number(p,'f',8);
                curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Refle")+m),st);
                readParamFlag.bit.readReflectivity = isStartReadParam == true ? true : false;
              }
            }
        }
    else if(data[1] == kHisAndFacula) {
        QString st;
        if(tmpFdb.size() == (KHisFacFreqRowCnt*4 + 2)) {
            uint32_t his = data[5]<<24 | data[4]<<16 | data[3]<<8 | data[2];
            st = QString::number(his,10);
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("ControlFreq")),st);
            uint32_t facula = data[9]<<24 | data[8]<<16 | data[7]<<8 | data[6];
            st = QString::number(facula,10);
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("ControlFreq")+1),st);
        }
    }
    else if(data[1] == kTemperature) {
        QString st;
        if(tmpFdb.size() == (kTemperatureRowCnt*4 + 2)) {
            float temperature; //=  (data[3]<<8 | data[2])/10.0;
            memcpy(&temperature,&data[2],4);
            st = QString::number(temperature,'f',1) + "℃";
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Temperature")),st);
        }
    }
    else if(data[1] == kRegisterSetting) {
        QString st;
        if(tmpFdb.size() == (KRegisterRowCnt*2 + 2)) {
          st =  QString("%1").arg(data[3],2,16, QLatin1Char('0'));
          st += QString("%1").arg(data[2],2,16, QLatin1Char('0'));
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Register")),st);
        }
    }
    else if(data[1] == kZeroAngleSetting) {
        QString st;
        if(tmpFdb.size() == (KZeroAngleRowCnt*4 + 2)) {
          float p = 0;
          memcpy(&p,&data[2],4);
          st = QString::number(p,'f',3);
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("ZeroAngle")),st);
          readParamFlag.bit.readZeroAngle = isStartReadParam == true ? true : false;
          emit TransmitZeroAngle(1,0,p);
        }
        else {
          emit TransmitZeroAngle(0,0,0);
        }
    }
    else if(data[1] == kDebugSetting) {
        QString st;
        if(tmpFdb.size() == (KDebug1RowCnt*2 + 2)) {
          st = QString("%1").arg(data[3],2,16, QLatin1Char('0'));
          st += QString("%1").arg(data[2],2,16, QLatin1Char('0'));
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Degug1")),st);
        }
        else if(tmpFdb.size() > 10){
            for(int m=6; m<fdb.size(); m++) {
                 st.push_back((char)fdb[m]);
            }
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Degug1")),st);
            readParamFlag.bit.readVersion = isStartReadParam == true ? true : false;
        }
        else {
          emit TransmitZeroAngle(2,0,0);
        }
    }
    else if(data[1] == kDebugSetting2) {
        QString st;
        if(tmpFdb.size() == (KDebug2RowCnt*2 + 2)) {
          st = QString("%1").arg(data[3],2,16, QLatin1Char('0'));
          st += QString("%1").arg(data[2],2,16, QLatin1Char('0'));
          curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Degug2")),st);
        }

    }
    else if(data[1] == kDebugSetting3) {
        QString st;
        if(tmpFdb.size() == (KDebug3RowCnt*2 + 4)) {
            float temperature; //=  (data[3]<<8 | data[2])/10.0;
            memcpy(&temperature,&data[2],4);
            st = QString::number(temperature,'f',3);
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Degug31")),st);
        }
    }
    else if(data[1] == kDebugSetting4) {
        QString st;
        if(tmpFdb.size() == (KDebug3RowCnt*2 + 4)) {
            float temperature; //=  (data[3]<<8 | data[2])/10.0;
            memcpy(&temperature,&data[2],4);
            st = QString::number(temperature,'f',3);
            curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Degug4")),st);
        }
    }
    else if(data[1] == 0xA6) {
         isStartReadCalibration = true;
                qDebug()<<"2";
    }
}

void LidarDatabase::on_pushButtonTransmit_clicked()
{
    /*header(1) + cmd(1) + id(1) + checkXor(1) + byteNumber(2) + data(n)*/
    int modeType = 0;
    float p3=0,p2=0,p1=0,p0=0,markpoint=0;
    Q_UNUSED(p3)
    Q_UNUSED(markpoint)
    uint8_t mode[2] = {0xA5,0x00};
    uint8_t float2Char[4] = {0};
    uint8_t fill[1] = {0};
    uint16_t tmp16[2],versionV;
    uint32_t hisFreq=0,faculaFreq=0,registerV=0;
    QByteArray transmitBuff;
    transmitBuff.append(0xA5);
    transmitBuff.append(tCmd);
    transmitBuff.append(tId);


    QByteArray str;
    float pid_speed = 0;
    uint32_t pid_u32 = 0;
    if(ui->radioButtonWrite->isChecked()) { /*写数据*/
        switch (tId) {
            case kTriMode :
                modeType = ui->comboBoxRunningMode->currentIndex();
                transmitBuff.append(0x01);
                transmitBuff.append(fill[0]);
                if(modeType == 0) {
                    mode[1] = 0xFF;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 1) {
                    mode[1] = 0xFE;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 2) {
                    mode[1] = 0xFD;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 3) {
                    mode[1] = 0xFC;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 4) {
                    mode[1] = 0xFB;
                    transmitBuff.append((char*)mode,2);
                }
                break;
            case kTofMode:
                modeType = ui->comboBoxRunningMode->currentIndex();
                transmitBuff.append(0x01);
                transmitBuff.append(fill[0]);
                if(modeType == 0) {
                    mode[1] = 0xFF;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 1) {
                    mode[1] = 0xFE;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 2) {
                    mode[1] = 0xFD;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 3) {
                    mode[1] = 0xFC;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 4) {
                    mode[1] = 0xFB;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 5) {
                    mode[1] = 0xFA;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 6) {
                    mode[1] = 0xF9;
                    transmitBuff.append((char*)mode,2);
                }

                break;
            case kSysParam:/*不可写*/
                transmitBuff.append(fill[0]);
                transmitBuff.append(fill[0]);
                break;
            case kMcuId:/*不可写*/
                transmitBuff.append(fill[0]);
                transmitBuff.append(fill[0]);
                break;
            case kVersionBuad:/*版本不建议写 可读*/
                /*versionV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("VerBaud"))).toString().toUInt(NULL,16);
                modeType = 3;
                transmitBuff.append(0x02);
                transmitBuff.append(fill[0]);
                transmitBuff.append(versionV);
                transmitBuff.append(versionV>>8);

                if(modeType == 0) {
                    mode[0] = 0x00;
                    mode[1] = 0x00;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 1) {
                    mode[0] = 0x01;
                    mode[1] = 0x00;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 2) {
                    mode[0] = 0x02;
                    mode[1] = 0x00;
                    transmitBuff.append((char*)mode,2);
                }
                else if(modeType == 3) {
                    mode[0] = 0x03;
                    mode[1] = 0x00;
                    transmitBuff.append((char*)mode,2);
                }*/

                break;
            case kMeasureRange :
                   tmp16[0] = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Measure"))).toUInt();
                   tmp16[1] = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Measure")+1)).toUInt();
                   transmitBuff.append(0x02);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(tmp16[0]&0xFF);
                   transmitBuff.append((tmp16[0]>>8));
                   transmitBuff.append(tmp16[1]&0xFF);
                   transmitBuff.append((tmp16[1]>>8));
                   break;

            case kCalibrationParam : /*需要将float转化为4字节整型*/
                   if(tofCalibParam.size() == 128) {
                       transmitBuff.append(2*32);
                       transmitBuff.append(fill[0]);
                       for(uint n=0; n<32; n++) {
                           p0 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Calibration")+n)).toFloat();
                           memcpy(float2Char,&p0,4);
                           transmitBuff.append(float2Char[0]);
                           transmitBuff.append(float2Char[1]);
                           transmitBuff.append(float2Char[2]);
                           transmitBuff.append(float2Char[3]);
                       }
                   }
                   else if(tofCalibParam.size() == 256) {
                        tofCalibParamLast.clear();
                        tofCalibParamLast = tofCalibParam;
                        transmitBuff.append(fill[0]);
                        transmitBuff.append(2);
                        for(uint n=0; n<256; n++) {
                            //p0 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Calibration")+n)).toFloat();
                            p0 = tofCalibParam[n];
                            memcpy(float2Char,&p0,4);
                            transmitBuff.append(float2Char[0]);
                            transmitBuff.append(float2Char[1]);
                            transmitBuff.append(float2Char[2]);
                            transmitBuff.append(float2Char[3]);
                        }

                        isMarch = true;
                        readParamIndex = 0;
                        paramCnt = 0;
                   }
                   break;
            case kTgParam :/*需要将float转化为4字节整型*/
                   p2 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Tg"))).toFloat();
                   p1 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Tg")+1)).toFloat();
                   p0 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Tg")+2)).toFloat();
                   transmitBuff.append(0x06);
                   transmitBuff.append(fill[0]);
                   memcpy(float2Char,&p2,4);
                   transmitBuff.append(float2Char[0]);
                   transmitBuff.append(float2Char[1]);
                   transmitBuff.append(float2Char[2]);
                   transmitBuff.append(float2Char[3]);
                   memcpy(float2Char,&p1,4);
                   transmitBuff.append(float2Char[0]);
                   transmitBuff.append(float2Char[1]);
                   transmitBuff.append(float2Char[2]);
                   transmitBuff.append(float2Char[3]);
                   memcpy(float2Char,&p0,4);
                   transmitBuff.append(float2Char[0]);
                   transmitBuff.append(float2Char[1]);
                   transmitBuff.append(float2Char[2]);
                   transmitBuff.append(float2Char[3]);
                   break;
            case kReflelParam :
                   transmitBuff.append(24);
                   transmitBuff.append(fill[0]);
                   for(uint n=0; n<12; n++) {
                       p0 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Refle")+n)).toFloat();
                       memcpy(float2Char,&p0,4);
                       transmitBuff.append(float2Char[0]);
                       transmitBuff.append(float2Char[1]);
                       transmitBuff.append(float2Char[2]);
                       transmitBuff.append(float2Char[3]);
                   }
                   break;
            case kHisAndFacula :    
                   hisFreq = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("ControlFreq"))).toUInt();
                   faculaFreq = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("ControlFreq")+1)).toUInt();
                   transmitBuff.append(0x04);
                   transmitBuff.append(fill[0]);

                   transmitBuff.append(hisFreq);
                   transmitBuff.append(hisFreq>>8);
                   transmitBuff.append(hisFreq>>16);
                   transmitBuff.append(hisFreq>>24);

                   transmitBuff.append(faculaFreq);
                   transmitBuff.append(faculaFreq>>8);
                   transmitBuff.append(faculaFreq>>16);
                   transmitBuff.append(faculaFreq>>24);

                   break;
            case kTemperature:
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(fill[0]);
                   break;
            case kRegisterSetting:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Register"))).toString().toUInt(NULL,16);
                   if(registerV > 0xFFFF) {/*两字节地址 两字节数据*/
                       transmitBuff.append(0x02);
                       transmitBuff.append(fill[0]);
                       transmitBuff.append(registerV);/*value low byte*/
                       transmitBuff.append(registerV>>8);/*value high byte*/
                       transmitBuff.append(registerV>>16);/*addr low byte*/
                       transmitBuff.append(registerV>>24);/*addr high byte*/

                   }
                   else {
                       transmitBuff.append(0x01);
                       transmitBuff.append(fill[0]);
                       transmitBuff.append(registerV);/*addr*/
                       transmitBuff.append(registerV>>8);/*value*/
                   }
                   break;
            case kZeroAngleSetting:
                   p2 = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("ZeroAngle"))).toFloat();
                   //p2 *= 18;
                   transmitBuff.append(0x02);
                   transmitBuff.append(fill[0]);
                   memcpy(float2Char,&p2,4);
                   transmitBuff.append(float2Char[0]);
                   transmitBuff.append(float2Char[1]);
                   transmitBuff.append(float2Char[2]);
                   transmitBuff.append(float2Char[3]);


                    //
                   /* str.append(0xF1);
                    str.append(0xF1);
                    str.append(0xAA);
                    str.append(0xF1);
                    str.append(0xF1);
                    str.append(0xF1);
                    pid_speed = p2;

                    memcpy(&pid_u32, &pid_speed, 4);
                    str.append((char*)&pid_u32,4);
                    emit FeedbackInfoTmp(str);
                    qDebug()<<pid_speed;
                    */

               break;
            case kLaserControl:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Laser"))).toString().toUInt(NULL,16);
                   transmitBuff.append(0x01);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(registerV);/*addr*/
                   transmitBuff.append(registerV>>8);/*value*/

               break;
            case kLedControl:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Vbd"))).toString().toUInt(NULL,16);
                   transmitBuff.append(0x01);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(registerV);/*addr*/
                   transmitBuff.append(registerV>>8);/*value*/

               break;
            case kVbdControl:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Led"))).toString().toUInt(NULL,16);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(fill[0]);

               break;
            case kDebugSetting:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Degug1"))).toString().toUInt(NULL,16);
                   transmitBuff.append(0x01);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(registerV);/*addr*/
                   transmitBuff.append(registerV>>8);/*value*/

               break;
            case kDebugSetting2:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Degug2"))).toString().toUInt(NULL,16);
                   transmitBuff.append(0x01);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(registerV);/*addr*/
                   transmitBuff.append(registerV>>8);/*value*/

               break;
            case kDebugSetting3:
                   registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Degug3"))).toString().toUInt(NULL,16);
                   transmitBuff.append(0x01);
                   transmitBuff.append(fill[0]);
                   transmitBuff.append(registerV);/*addr*/
                   transmitBuff.append(registerV>>8);/*value*/

               break;
            case kDebugSetting4:
                    registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Degug4"))).toString().toUInt(NULL,16);
                    transmitBuff.append(0x01);
                    transmitBuff.append(fill[0]);
                    transmitBuff.append(registerV);/*addr*/
                    transmitBuff.append(registerV>>8);/*value*/


//                transmitBuff.append(1);
//                transmitBuff.append(fill[0]);

//                memcpy(float2Char,&p0,4);
//                transmitBuff.append(float2Char[0]);
//                transmitBuff.append(float2Char[1]);
//                transmitBuff.append(float2Char[2]);
//                transmitBuff.append(float2Char[3]);
               break;
            case kXtalk:
                    transmitBuff.append(fill[0]);
                    transmitBuff.append(fill[0]);


                   break;

            default:
               return;
               break;
        }
    }
    else if(ui->radioButtonRead->isChecked()) {/*读数据*/
        if(tId == kRegisterSetting) {
            registerV = curItemModel->data(curItemModel->index(0,tableRowCntMap.at("Register"))).toString().toUInt(NULL,16);
            qDebug()<<hex<<registerV;
            if(registerV > 0xFFFF) {
                registerV = registerV>>8;

//                if(registerV > 255) {
//                    registerV = registerV>>8;
//                }

                transmitBuff.append(0x01);
                transmitBuff.append(fill[0]);
                transmitBuff.append(registerV);/*addr*/
                transmitBuff.append(registerV>>8);/*value*/
                qDebug()<<"1"<<hex<<registerV;
            }
            else {
                if(registerV > 255) {
                   // registerV = registerV>>8;
                }
                qDebug()<<"A"<<hex<<registerV;
                transmitBuff.append(0x01);
                transmitBuff.append(fill[0]);
                transmitBuff.append(registerV);/*addr*/
                transmitBuff.append(registerV>>8);/*value*/
            }
        }
        else {
            transmitBuff.append(fill[0]);
            transmitBuff.append(fill[0]);
        }
    }
    uint16_t byteNum = transmitBuff.at(3) | ((0x00ff&transmitBuff.at(4))<<8);
    uint8_t checkSum = 0xA5^tCmd^tId^ transmitBuff.at(3)^transmitBuff.at(4);
    for(uint16_t n=0; n<byteNum*2; n++) {
        checkSum ^= transmitBuff.at(5+n);
    }
    transmitBuff.insert(3,checkSum);
    emit TransmitDataLidar(transmitBuff);
    /*设置发送字节数*/

    /*添加时间戳*/
    QDateTime curDateTime=QDateTime::currentDateTime();
    ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ") + "-> send pack: " + transmitBuff.toHex());
}

void LidarDatabase::ReadParamThroughId(uint8_t id)
{
    QByteArray transmitBuff;
    uint8_t fill[1] = {0};
    transmitBuff.append(0xA5);
    transmitBuff.append(0x09);
    transmitBuff.append(id);
    transmitBuff.append(fill[0]);
    transmitBuff.append(fill[0]);
    uint16_t byteNum = transmitBuff.at(3) | ((0x00ff&transmitBuff.at(4))<<8);
        uint8_t checkSum = 0xA5^0x09^id^ transmitBuff.at(3)^transmitBuff.at(4);
    for(uint16_t n=0; n<byteNum*2; n++) {
        checkSum ^= transmitBuff.at(5+n);
    }
    transmitBuff.insert(3,checkSum);
    emit TransmitDataLidar(transmitBuff);
    /*设置发送字节数*/

    /*添加时间戳*/
    QDateTime curDateTime=QDateTime::currentDateTime();
    ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd-hh:mm:ss.zzz ") + "-> send pack: " + transmitBuff.toHex());

}

void LidarDatabase::ReceviceCalibrationData(std::vector<float> p)
{
    tofCalibParam.clear();
    tofCalibParam = p;
    QString st;
    qDebug() <<"******************************************************";
    if(p.size() == 32) {
        for(uint8_t m=0; m<32; m++) {
            curItemModel->setData(curItemModel->index(0,tableRowCntMap.at("Calibration")+m),QString::number(p.at(m),'f',2));
        }      
    }
    else if(p.size() == 32*8) {
        for(uint m=0; m<32; m++) {
            qDebug() << p[m*8+0]<<", "<< p[m*8+1]<<", "<< p[m*8+2]<<", "<< p[m*8+3]<<", "\
                    << p[m*8+4]<<", "<< p[m*8+5]<<", "<< p[m*8+6]<<", "<< p[m*8+7]<<", ";
        }
        for(uint m=26; m<32; m++) {
            tofCalibParam[8*m+0] = 1;
            tofCalibParam[8*m+1] = 1;
            tofCalibParam[8*m+2] = 1;
            tofCalibParam[8*m+3] = 1;
            tofCalibParam[8*m+4] = 1;
            tofCalibParam[8*m+5] = 1;
            tofCalibParam[8*m+6] = 1;
            tofCalibParam[8*m+7] = 1;
        }
    }

}

void LidarDatabase::on_actionDSchOnce_triggered()
{
    if(isConnectioned == false) {
        QMessageBox::warning(NULL, "warning", "sql connect error!");
        return;
    }
    QString id = curItemModel->index(1,0).data().toString();
    if(id == "") {
        QMessageBox::information(NULL, "information", "check the McuId id please!");
        return;
    }
    //进行筛选
    model->setFilter(QString("McuId = '%1'").arg(id));
    //显示结果
    model->select();
}

void LidarDatabase::on_actionDSchAll_triggered()
{
    if(isConnectioned == true) {
        model->setTable(kTableName);   //重新关联表
        model->select();   //这样才能再次显示整个表的内容
    }
}

void LidarDatabase::on_actionDSubmit_triggered()
{
    int curRow = ui->tableViewLidarInfomation->currentIndex().row();
    if(curRow == -1) {
        QMessageBox::information(NULL, "information", "check submit line please!");
        return;
    }
    int ok = QMessageBox::information(this,tr("submit current data?"),
                                  tr("sure?"),
                                  QMessageBox::Yes,QMessageBox::No);
    if(ok == QMessageBox::No) {
        return;
    }
    else {
        model->database().transaction(); //开始事务操作
        if (model->submitAll()) {
            model->database().commit(); //提交
        } else {
           model->database().rollback(); //回滚
           qDebug() << "commit error!";
        }
    }

}

void LidarDatabase::on_actionDSubmitAll_triggered()
{
    /*QString id = curItemModel->index(1,0).data().toString();
    if(id == "") {
        QMessageBox::information(NULL, "information", "check the McuId id please!");
        return;
    }*/
    int ok = QMessageBox::information(this,tr("submit current data?"),
                                  tr("sure?"),
                                  QMessageBox::Yes,QMessageBox::No);
    if(ok == QMessageBox::No) {
        return;
    }
    else {
        model->database().transaction(); //开始事务操作
        if (model->submitAll()) {
            model->database().commit(); //提交
        } else {
           model->database().rollback(); //回滚
           qDebug() << "commit error!";
        }
    }
}

void LidarDatabase::on_actionDInsert_triggered()
{
    isStartReadParam = true;
    readParamFlag.allBits = false;
    QDateTime curDateTime=QDateTime::currentDateTime();
    curDateTime.toString("yyyy-MM-dd-hh-mm-ss ");
    curItemModel->setData(curItemModel->index(1,tableRowCntMap.at("Date")),curDateTime);
    readParamFlag.bit.readDate = true;
    paramCnt = 0;
    lastParamCnt = 0;
    readParamIndex = 0;
}

void LidarDatabase::on_actionDIstImg_triggered()
{
    int curRow = ui->tableViewLidarInfomation->currentIndex().row();
    int curColumn = ui->tableViewLidarInfomation->currentIndex().column();

    if(curRow == -1 || curColumn ==-1) {
            QMessageBox::warning(NULL, "warning", "click insert Image uint please!");
    }
    else if(((curColumn >= tableRowCntMap.at("GreymapImage") && curColumn <= tableRowCntMap.at("CaliImage"))
       || (curColumn >= tableRowCntMap.at("Image") ) ) && (curRow == 1)) {

        QString fileName = QFileDialog::getOpenFileName(
                    this,
                    tr("Get image..."),
                    tr(""),
                    tr("Images (*.png *.xpm *.bmp)")
                    );

        if (!fileName.isEmpty())
        {
           QFile inImg(fileName);
           if(!inImg.open(QIODevice::ReadOnly)){
               QMessageBox::warning(0,"error","open img error");
           }
           QByteArray inByteArray = inImg.readAll();
           curItemModel->setData(curItemModel->index(curRow,curColumn),QVariant(inByteArray));
        }
    }
    else {
        QMessageBox::warning(NULL, "warning", "click insert Image column please!");
        return;
    }
}

void LidarDatabase::on_actionDRevert_triggered()
{
    //获取选中的行
    int curRow = ui->tableViewLidarDatabase->currentIndex().row();
    if(curRow == -1) {
        QMessageBox::warning(NULL, "warning", "select revert line please!");
    }
    else {
        model->revertRow(curRow);
    }
}

void LidarDatabase::on_actionDRevertAll_triggered()
{
    model->revertAll();
}

void LidarDatabase::on_actionLCopy_triggered()
{
    int rowNum=0;
    rowNum = ui->tableViewLidarDatabase->currentIndex().row();
    if(rowNum == -1) {
        QMessageBox::warning(NULL, "warning", "select valid database line please!");
    }
    else {
        for(uint8_t m=0; m<kParamTitleCnt; m++) {
            if((m >= tableRowCntMap.at("GreymapImage") && m <= tableRowCntMap.at("CaliImage"))
               || (m >= tableRowCntMap.at("Image") ))
            {

            }
            else {
               curItemModel->setData(curItemModel->index(0,m),model->index(rowNum,m).data().toString());
               //qDebug()<<model->index(rowNum,m).data().toString();
            }
        }
    }

}

void LidarDatabase::on_actionDShowIamge_triggered()
{
    if(showImage == nullptr) {
        showImage = new ShowImage();
        showImage->show();
        connect(this,&LidarDatabase::TransmitImage,showImage,&ShowImage::RecQPixmap);
        QTimer::singleShot(50,this, &LidarDatabase::ReShow);
    }
    else {
        int curColumn = ui->tableViewLidarDatabase->currentIndex().column();
        int curRow = ui->tableViewLidarDatabase->currentIndex().row();
        if((curColumn >= tableRowCntMap.at("GreymapImage") && curColumn <= tableRowCntMap.at("CaliImage"))
           || (curColumn >= tableRowCntMap.at("Image") ))
        {
            QByteArray ma = model->index(curRow,curColumn).data().toByteArray();
            emit TransmitImage(ma,0,0);
        }
        else if(curColumn == -1) {
            QMessageBox::warning(NULL, "warning", "select Image please!");
            return;
        }
        else {
            QMessageBox::warning(NULL, "warning", "click valid Image uint please!");
        }
        showImage->show();
    }


}

void LidarDatabase::ReShow()
{
    int curColumn = ui->tableViewLidarDatabase->currentIndex().column();
    int curRow = ui->tableViewLidarDatabase->currentIndex().row();
    if((curColumn >= tableRowCntMap.at("GreymapImage") && curColumn <= tableRowCntMap.at("CaliImage"))
       || (curColumn >= tableRowCntMap.at("Image") ))
    {
        QByteArray ma = model->index(curRow,curColumn).data().toByteArray();
        emit TransmitImage(ma,0,0);
    }
    else if(curColumn == -1) {
        QMessageBox::warning(NULL, "warning", "select Image please!");
        return;
    }
    else {
        QMessageBox::warning(NULL, "warning", "click valid Image uint please!");
    }
    showImage->show();
}

void LidarDatabase::on_comboBoxRunningMode_editTextChanged(const QString &arg1)
{
    if(arg1 == "0") {
      emit TransmitHistType(0);
    }
    else if(arg1 == "1") {
      emit TransmitHistType(1);
    }
    else if(arg1 == "2") {
      emit TransmitHistType(2);
    }
    else if(arg1 == "3") {
      emit TransmitHistType(3);
    }
    //qDebug()<<"TransmitHistType: "<< arg1;

}

/*
            QDateTime curDateTime=QDateTime::currentDateTime();
            ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+);
*/

void LidarDatabase::delay_ms(int msec)
{
    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while( QTime::currentTime() < dieTime )
        QCoreApplication::processEvents(QEventLoop::AllEvents, 3000);
}

void LidarDatabase::iap_app_unlock(uint8_t cmd, uint8_t id)
{
    uint16_t size = 6;
    QByteArray buff;
    buff.resize(size);
    uint8_t  *buff_ptr = (uint8_t*)buff.data();

    app_cmd_protocol  *protocol = (app_cmd_protocol*)buff_ptr;

    protocol->header = 0xA5;
    protocol->cmd = cmd;
    protocol->id = id;
    protocol->xor_check = 0;
    protocol->length = 0;
    /*protocol->data[0] = 0x00;
    protocol->data[1] = 0x00;*/

    uint8_t ckeck = 0;
    for(uint m=0; m<size; m++) {
        if(m != 3) {
            ckeck ^= buff[m];
        }

    }
    protocol->xor_check = ckeck;
    emit TransmitDataLidar(buff);

}

void LidarDatabase::iap_app_to_bootloader(uint8_t cmd, uint8_t id)
{
    uint16_t size = 6;
    QByteArray buff;
    buff.resize(size);
    uint8_t  *buff_ptr = (uint8_t*)buff.data();

    app_cmd_protocol  *protocol = (app_cmd_protocol*)buff_ptr;

    protocol->header = 0xA5;
    protocol->cmd = cmd;
    protocol->id = id;
    protocol->xor_check = 0;
    protocol->length = 0;
    /*protocol->data[0] = 0x01;
    protocol->data[1] = 0x00;*/

    uint8_t ckeck = 0;
    for(uint m=0; m<size; m++) {
        if(m != 3) {
            ckeck ^= buff[m];
        }

    }
    protocol->xor_check = ckeck;
    emit TransmitDataLidar(buff);

}

void LidarDatabase::iap_app_cmd_pack(uint8_t cmd, uint8_t id, app_bin_one_frame *frame)
{
    uint16_t size = 0;
    if(cmd != 0x09) {
        size = 6+(frame==nullptr?0:(frame->size+4+2));
    }
    else {
        size = 6+(frame==nullptr?0:(4+2));
    }
    QByteArray buff;
    buff.resize(size);

    uint8_t  *buff_ptr = (uint8_t*)buff.data();

    app_cmd_protocol  *protocol = (app_cmd_protocol*)buff_ptr;

    protocol->header = 0xA5;
    protocol->cmd = cmd;
    protocol->id = id;
    protocol->xor_check = 0;
    protocol->length = size-6;
    if(frame != nullptr) {
        memcpy(protocol->data,(uint8_t*)frame,size-6);
    }

    uint8_t ckeck = 0;
    for(uint m=0; m<size; m++) {
        if(m != 3) {
            ckeck ^= buff[m];
            //LOG_INFO("CHECK: %2X",ckeck);
        }

    }
    protocol->xor_check = ckeck;
    emit TransmitDataLidar(buff);

}

void LidarDatabase::sig_iap_ack(iap_ack_t ack)
{
    qDebug()<<"iap_ack:"<<hex<<ack.id;
    switch (ack.id) {
        case 0xBE :
            if(ack.ack_result == 1){
                _app_bin_status.app_unlock = true;
            }
            break;
        case 0xBD :
            if(ack.ack_result == 1){
                _app_bin_status.app_to_bootloader_ok = true;
            }
            break;
        case 0xC0 :
            if(ack.ack_result == 1){
                _app_bin_status.start_iap = true;
            }
            break;
        case 0xC1 :
            if(ack.ack_result == 1){
                if(ack.ack_type == 0){
                    memcpy((uint8_t*)&_app_bin_status.frame, (uint8_t*)ack.data.data(), ack.data.size());
                    _app_bin_status.read_one_frame_ok = true;
                }
                else{
                    _app_bin_status.load_one_frame_ok = true;
                }
            }
            break;
        case 0xC2 :
            if(ack.ack_result == 1){
                _app_bin_status.stop_iap = true;
            }
            break;
        default:
            break;
    }


}

void LidarDatabase::on_actionOTA_triggered()
{
    static QString file_info;
    QString file_name = QFileDialog::getOpenFileName(this,"load app bin...",file_info==""?qApp->applicationDirPath():file_info, "*.bin");

    if(file_name == "") {
        QMessageBox::warning(this,"WARRING","no bin file !");
        return;
    }


    QFileInfo fileInfo(file_name);
    file_info =  fileInfo.absolutePath();//路径
    /*slFileInfo =  fileInfo.fileName();//文件全名

    //---------------拿到文件的路径-----------------------------------
    slFileInfo = fileInfo.absolutePath()+"/"+fileInfo.fileName();

    //---------------拿到文件的路径-----------------------------------
    slFileInfo = tr("File Base Name: ") + fileInfo.baseName();//文件名
    slFileInfo = tr("File Type: ") + fileInfo.suffix();//后缀*/


     std::vector<app_bin_one_frame> _app_bin_op,_app_bin_op_x;
     QFile file(file_name);
     QDateTime curDateTime=QDateTime::currentDateTime();
     ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("bin size: %1 bytes").arg(file.size()));


     uint32_t file_size = file.size();

     uint16_t block_size = 2048;

     if(file_size > 64*1024 || ui->radioButtonMeasureRange->isChecked() == true) {
        block_size = 8192;
     }
     else {
        block_size = 2048;
     }

     //uint16_t block_size = 2048;
     qDebug()<<"block_size: "<<block_size;
     uint32_t start_arr = 0x08002000;
     uint16_t block_num = file_size/block_size;
     uint16_t block_last_length = file_size%block_size;

     QByteArray data;
     app_bin_one_frame  one_frame;
     _app_bin_op.clear();
     if(file.open(QIODevice::ReadOnly)) {
        data = file.readAll();
        uint8_t *data_ptr = (uint8_t *)data.data();
        for(uint m=0; m<block_num; m++) {
            one_frame.addr = start_arr + block_size*m;
            one_frame.size = block_size;
            memcpy(one_frame.data,&data_ptr[block_size*m],block_size);
            _app_bin_op.push_back(one_frame);
        }
        if(block_last_length > 0) {
            one_frame.addr = start_arr + block_size*block_num;
            one_frame.size = block_last_length;
            memset(one_frame.data,0,block_size);
            memcpy(one_frame.data,&data_ptr[block_size*block_num],block_last_length);
            _app_bin_op.push_back(one_frame);
        }
     }
     file.close();
     /******check again*******/
     QFile file_x(file_name);

     uint32_t file_size_x = file_x.size();
     curDateTime=QDateTime::currentDateTime();
     ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("bin size: %1 bytes").arg(file_size_x));

     if(file_size_x > 64*1024 || ui->radioButtonMeasureRange->isChecked() == true) {
        block_size = 8192;
     }
     else {
        block_size = 2048;
     }
     qDebug()<<"block_size: "<<block_size;
     uint16_t block_num_x = file_size_x/block_size;
     uint16_t block_last_length_x = file_size_x%block_size;

     QByteArray data_x;
     app_bin_one_frame  one_frame_x;
     _app_bin_op_x.clear();
     if(file_x.open(QIODevice::ReadOnly)) {
        data_x = file_x.readAll();
        uint8_t *data_ptr = (uint8_t *)data_x.data();
        for(uint m=0; m<block_num_x; m++) {
            one_frame_x.addr = start_arr + block_size*m;
            one_frame_x.size = block_size;
            memcpy(one_frame_x.data,&data_ptr[block_size*m],block_size);
            _app_bin_op_x.push_back(one_frame_x);
        }
        if(block_last_length_x > 0) {
            one_frame_x.addr = start_arr + block_size*block_num_x;
            one_frame_x.size = block_last_length_x;
            memset(one_frame_x.data,0,block_size);
            memcpy(one_frame_x.data,&data_ptr[block_size*block_num_x],block_last_length_x);
            _app_bin_op_x.push_back(one_frame_x);
        }
     }

//     qDebug()<<file_size_x<< " "<<file_size;
     if(file_size_x != file_size) {
         QMessageBox::warning(this,"WARRING","app bin check error!");
         return;
     }



     for(uint m=0; m<_app_bin_op_x.size(); m++) {
        if(_app_bin_op_x[m].addr != _app_bin_op[m].addr ||  _app_bin_op_x[m].size != _app_bin_op[m].size) {
            QMessageBox::warning(this,"WARRING","app bin check error!");
            return;
        }

        for(uint n=0; n<_app_bin_op_x[m].size; n++) {
            if(_app_bin_op_x[m].data[n] != _app_bin_op[m].data[n]) {
                QMessageBox::warning(this,"WARRING","app bin check error!");
                return;
            }
        }
     }
     file_x.close();
     _app_bin_op_t = _app_bin_op;
     curDateTime=QDateTime::currentDateTime();
     ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("firmware has readed!"));


     //QMessageBox::information(this,"INFORMATION","app bin has loaded!");


     /********load app flow*******/
     //app unlock
     uint repeat_cnt = 0;
     bool ack_ok = false;
     for(uint m=0; m<3; m++) {
        iap_app_unlock(0x05,0xBE);
        _app_bin_status.app_unlock = false;
        do {
            //check slot ack
            if(_app_bin_status.app_unlock == true) {
                ack_ok = true;
                break;
            }
            delay_ms(20);
            repeat_cnt++;

        }while(repeat_cnt < 25);
        repeat_cnt = 0;
        curDateTime=QDateTime::currentDateTime();
        ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("app unlock timeout..."));


        if(ack_ok == true) {
            break;
        }
     }
     if(ack_ok == false) {
         QMessageBox::warning(this,"warning","app unlock is error!");
         return;
     }
     else {
         //ui->label_iap->setText("app unlock!");
         curDateTime=QDateTime::currentDateTime();
         ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("app unlock is successful"));

         //delay_ms(1000);
     }





     //app to bootloader
     repeat_cnt = 0;
     ack_ok = false;
     for(uint m=0; m<3; m++) {
        iap_app_to_bootloader(0x05,0xBD);//app to bootloader
        _app_bin_status.app_to_bootloader_ok = false;
        do {
            //check slot ack
            if(_app_bin_status.app_to_bootloader_ok == true) {
                ack_ok = true;
                break;
            }
            delay_ms(20);
            repeat_cnt++;

        }while(repeat_cnt < 25);
        repeat_cnt = 0;
        if(ack_ok == true) {
            break;
        }
     }
     if(ack_ok == false) {
         QMessageBox::warning(this,"warning","app to bootloader is error!");
         return;
     }
     else {
         //ui->label_iap->setText("switch app to bootloader!");

         curDateTime=QDateTime::currentDateTime();
         ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("app to bootloader is successful"));

         delay_ms(200);
     }

     /******start loading**********/
     ack_ok = false;
     repeat_cnt = 0;
     for(uint m=0; m<3; m++) {
        iap_app_cmd_pack(0x05,0xC0,nullptr);//start loading
        _app_bin_status.start_iap = false;
        do {
            //check slot ack if(true) {break;}
            if(_app_bin_status.start_iap == true) {
                ack_ok = true;
                break;
            }
            delay_ms(20);
            repeat_cnt++;

        }while(repeat_cnt < 25);
        repeat_cnt = 0;
        if(ack_ok == true) {
            break;
        }
        curDateTime=QDateTime::currentDateTime();
        ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("start repeat %1").arg(m));

     }
     if(ack_ok == false) {
         QMessageBox::warning(this,"warning","starting loading is error!");
         return;
     }
     else {
         //ui->label_iap->setText("start load!");
         curDateTime=QDateTime::currentDateTime();
         ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("starting loading is successful"));

     }


     /*******loading*******/
     ui->progressBar_iap->setMaximum(_app_bin_op_t.size());
     //ui->label_iap->setText("firmware is loading!");
     for(uint i=0; i<_app_bin_op_t.size(); i++) {
         ack_ok = false;
         repeat_cnt = 0;
         ui->progressBar_iap->setValue(i+1);

         //write
         for(uint m=0; m<3; m++) {
            iap_app_cmd_pack(0x05,0xC1,&_app_bin_op_t[i]);// loading
            _app_bin_status.load_one_frame_ok = false;
            do {
                //check slot ack
                if(_app_bin_status.load_one_frame_ok == true) {
                    ack_ok = true;
                    break;
                }
                delay_ms(10);
                repeat_cnt++;

            }while(repeat_cnt < 300);

            repeat_cnt = 0;
            if(ack_ok == true) {
                break;
            }
            curDateTime=QDateTime::currentDateTime();
            ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("write repeat %1").arg(m+1));

         }

         if(ack_ok == false) {
             QMessageBox::warning(this,"warning",QString("导入到 %1/%2 失败").arg(i+1).arg(_app_bin_op_t.size()));
             return;
         }
         else {
             curDateTime=QDateTime::currentDateTime();
             ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("write processing step %1/%2 has finisded").arg(i+1).arg(_app_bin_op_t.size()));
         }

         //read
         ack_ok = false;
         repeat_cnt = 0;
         for(uint m=0; m<3; m++) {
            app_bin_one_frame frame;
            frame.addr = _app_bin_op_t[i].addr;
            frame.size = _app_bin_op_t[i].size;

            iap_app_cmd_pack(0x09,0xC1,&frame);// loading
            _app_bin_status.read_one_frame_ok = false;
            do {
                //check slot ack
                if(_app_bin_status.read_one_frame_ok == true) {

                    if(_app_bin_op_t[i].addr == _app_bin_status.frame.addr && \
                       _app_bin_op_t[i].size == _app_bin_status.frame.size ) {

                        bool check_ok = true;
                        for(uint mm=0; mm<_app_bin_status.frame.size; mm++) {
                            if(_app_bin_op_t[i].data[mm] != _app_bin_status.frame.data[mm]) {
                                check_ok = false;
                            }
                        }

                        if(check_ok == true) {
                            ack_ok = true;
                            break;
                        }
                        else {
                            ack_ok = false;
                        }


                    }
                    else {
                        ack_ok = false;
                        break;
                    }

                }
                delay_ms(10);
                repeat_cnt++;

            }while(repeat_cnt < 400);
            repeat_cnt = 0;
            if(ack_ok == true) {
                break;
            }
            curDateTime=QDateTime::currentDateTime();
            ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("read repeat %1").arg(m+1));

         }

         if(ack_ok == false) {
             QMessageBox::warning(this,"warning",QString("导入到 %1/%2 失败").arg(i+1).arg(_app_bin_op_t.size()));
             return;
         }
         else {
             curDateTime=QDateTime::currentDateTime();
             ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("read processing step %1/%2 has finished").arg(i+1).arg(_app_bin_op_t.size()));

         }
     }
     //ui->label_iap->setText("firmware hss loaded!");


     /*******stop loading*******/
     ack_ok = false;
     repeat_cnt = 0;
     for(uint m=0; m<3; m++) {
        iap_app_cmd_pack(0x05,0xC2,nullptr);//stop loading
        _app_bin_status.stop_iap = false;
        do {
            //check slot ack
            if(_app_bin_status.stop_iap == true) {
                ack_ok = true;
                break;
            }
            delay_ms(20);
            repeat_cnt++;

        }while(repeat_cnt < 50);
        repeat_cnt = 0;
        if(ack_ok == true) {
            break;
        }
        curDateTime=QDateTime::currentDateTime();
        ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("stop repeat %1").arg(m));

     }
     if(ack_ok == false) {
         QMessageBox::warning(this,"warning","stopping loading is error!");
         return;
     }
     else {
         curDateTime=QDateTime::currentDateTime();
         ui->textEditLog->append(curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz ")+QString("stopping loading is successful"));

         //ui->label_iap->setText("firmware has loaded successfully!");
     }

}
