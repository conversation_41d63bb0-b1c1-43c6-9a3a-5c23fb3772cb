#include "dynamic_calibration.h"
#include "ui_dynamiccalibration.h"
#include <iostream>
#include <QFile>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <Eigen/Dense>
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Cholesky>
float xxx[] = {
    2.11538461538462,
    2.17948717948718,
    2.17948717948718,
    2.24358974358974,
    2.17948717948718,
    2.30769230769231,
    2.24358974358974,
    2.37179487179487,
    2.37179487179487,
    2.37179487179487,
    2.43589743589744,
    2.43589743589744,
    2.43589743589744,
    2.5,
    2.56410256410256,
    2.62820512820513,
    2.62820512820513,
    2.62820512820513,
    2.62820512820513,
    2.62820512820513,
    2.56410256410256,
    2.56410256410256,
    2.69230769230769,
    2.69230769230769,
    2.69230769230769,
    2.69230769230769,
    2.75641025641026,
    2.75641025641026,
    2.75641025641026,
    2.69230769230769,
    2.75641025641026,
    2.75641025641026,
    2.69230769230769,
    2.75641025641026,
    2.75641025641026,
    2.88461538461538,
    2.82051282051282,
    2.88461538461538,
    3.01282051282051,
    2.94871794871795,
    2.94871794871795,
    2.88461538461538,
    3.01282051282051,
    3.01282051282051,
    3.01282051282051,
    3.01282051282051,
    3.07692307692308,
    3.07692307692308,
    3.14102564102564,
    3.07692307692308,
    3.14102564102564,
    3.07692307692308,
    3.14102564102564,
    3.14102564102564,
    3.07692307692308,
    3.20512820512821,
    3.26923076923077,
    3.14102564102564,
    3.26923076923077,
    3.26923076923077,
    3.26923076923077,
    3.3974358974359,
    3.33333333333333,
    3.3974358974359,
    3.3974358974359,
    3.3974358974359,
    3.3974358974359,
    3.3974358974359,
    3.58974358974359,
    3.52564102564103,
    3.46153846153846,
    3.46153846153846,
    3.46153846153846,
    3.58974358974359,
    3.58974358974359,
    3.52564102564103,
    3.58974358974359,
    3.65384615384615,
    3.58974358974359,
    3.65384615384615,
    3.65384615384615,
    3.65384615384615,
    3.58974358974359,
    3.52564102564103,
    3.58974358974359,
    3.58974358974359,
    3.58974358974359,
    3.65384615384615,
    3.65384615384615,
    3.65384615384615,
    3.71794871794872,
    3.71794871794872,
    3.71794871794872,
    3.71794871794872,
    3.84615384615385,
    3.91025641025641,
    3.91025641025641,
    3.84615384615385,
    3.84615384615385,
    3.84615384615385,
    3.84615384615385,
    3.91025641025641,
    3.84615384615385,
    3.91025641025641,
    3.84615384615385,
    3.78205128205128,
    3.91025641025641,
    3.91025641025641,
    3.84615384615385,
    3.84615384615385,
    3.91025641025641,
    3.97435897435897,
    3.91025641025641,
    3.91025641025641,
    3.84615384615385,
    3.91025641025641,
    4.03846153846154,
    3.91025641025641,
    3.91025641025641,
    3.91025641025641,
    3.91025641025641,
    3.97435897435897,
    3.97435897435897,
    3.97435897435897,
    3.97435897435897,
    3.91025641025641,
    3.97435897435897,
    3.91025641025641,
    4.03846153846154,
    3.97435897435897,
    4.03846153846154,
    4.03846153846154,
    4.03846153846154,
    4.03846153846154,
    4.03846153846154,
    4.03846153846154,
    4.1025641025641,
    4.1025641025641,
    4.1025641025641,
    4.1025641025641,
    4.1025641025641,
    4.1025641025641,
    4.16666666666667,
    4.1025641025641,
    4.16666666666667,
    4.1025641025641,
    4.16666666666667,
    4.16666666666667,
    4.16666666666667,
    4.16666666666667,
    4.16666666666667,
    4.16666666666667,
    4.16666666666667,
    4.23076923076923,
    4.23076923076923,
    4.23076923076923,
    4.29487179487179,
    4.23076923076923,
    4.29487179487179,
    4.29487179487179,
    4.29487179487179,
    4.29487179487179,
    4.35897435897436,
    4.42307692307692,
    4.35897435897436,
    4.35897435897436,
    4.35897435897436,
    4.48717948717949,
    4.42307692307692,
    4.48717948717949,
    4.42307692307692,
    4.48717948717949,
    4.61538461538462,
    4.55128205128205,
    4.67948717948718,
    4.67948717948718,
    4.67948717948718,
    4.74358974358974,
    4.80769230769231,
    4.80769230769231,
    4.80769230769231,
    4.80769230769231,
    4.87179487179487,
    4.80769230769231,
    4.87179487179487,
    4.93589743589744,
    4.93589743589744,
    5,
    4.93589743589744,
    4.93589743589744,
    5.06410256410256,
    5.06410256410256,
    5.06410256410256,
    5.06410256410256,
    5.06410256410256,
    5.12820512820513,
    5.12820512820513,
    5.12820512820513,
    5.19230769230769,
    5.19230769230769,
    5.19230769230769,
    5.25641025641026,
    5.25641025641026,
    5.25641025641026,
    5.32051282051282,
    5.25641025641026,
    5.25641025641026,
    5.32051282051282,
    5.44871794871795,
    5.32051282051282,
    5.38461538461539,
    5.32051282051282,
    5.44871794871795,
    5.51282051282051,
    5.44871794871795,
    5.44871794871795,
    5.51282051282051,
    5.57692307692308,
    5.64102564102564,
    5.70512820512821,
    5.76923076923077,
    5.70512820512821,
    5.76923076923077,
    5.83333333333333,
    5.76923076923077,
    5.83333333333333,
    5.8974358974359,
    5.8974358974359,
    5.8974358974359,
    5.8974358974359,
    5.96153846153846,
    5.96153846153846,
    6.02564102564103,
    5.96153846153846,
    6.02564102564103,
    6.02564102564103,
    6.08974358974359,
    6.08974358974359,
    6.08974358974359,
    6.08974358974359,
    6.08974358974359,
    6.08974358974359,
    6.15384615384615,
    6.15384615384615,
    6.15384615384615,
    6.15384615384615,
    6.21794871794872,
    6.15384615384615,
    6.21794871794872,
    6.21794871794872,
    6.21794871794872,
    6.21794871794872,
    6.21794871794872,
    6.21794871794872,
    6.28205128205128,
    6.28205128205128,
    6.28205128205128,
    6.34615384615385,
    6.34615384615385,
    6.34615384615385,
    6.34615384615385,
    6.34615384615385,
    6.41025641025641,
    6.34615384615385,
    6.47435897435897,
    6.41025641025641,
    6.47435897435897,
    6.47435897435897,
    6.53846153846154,
    6.53846153846154,
    6.6025641025641,
    6.6025641025641,
    6.66666666666667,
    6.6025641025641,
    6.66666666666667,
    6.73076923076923,
    6.73076923076923,
    6.73076923076923,
    6.79487179487179,
    6.79487179487179,
    6.79487179487179,
    6.85897435897436,
    6.85897435897436,
    6.85897435897436,
    6.92307692307692,
    6.92307692307692,
    6.92307692307692,
    6.92307692307692,
    7.05128205128205,
    6.98717948717949,
    7.05128205128205,
    7.05128205128205,
    7.05128205128205,
    7.05128205128205,
    7.05128205128205,
    7.05128205128205,
    7.11538461538462,
    7.11538461538462,
    7.11538461538462,
    7.11538461538462,
    7.17948717948718,
    7.17948717948718,
    7.17948717948718,
    7.17948717948718,
    7.24358974358974,
    7.17948717948718,
    7.30769230769231,
    7.24358974358974,
    7.30769230769231,
    7.30769230769231,
    7.30769230769231,
    7.30769230769231,
    7.30769230769231,
    7.30769230769231,
    7.37179487179487,
    7.37179487179487,
    7.43589743589744,
    7.5,
    7.5,
    7.5,
    7.56410256410256,
    7.56410256410256,
    7.62820512820513,
    7.62820512820513,
    7.69230769230769,
    7.75641025641026,
    7.75641025641026,
    7.82051282051282,
    7.82051282051282,
    7.82051282051282,
    7.82051282051282,
    7.88461538461539,
    7.88461538461539,
    7.94871794871795,
    7.94871794871795,
    8.01282051282051,
    8.01282051282051,
    8.07692307692308,
    8.07692307692308,
    8.07692307692308,
    8.07692307692308,
    8.14102564102564,
    8.14102564102564,
    8.20512820512821,
    8.20512820512821,
    8.20512820512821,
    8.26923076923077,
    8.26923076923077,
    8.26923076923077,
    8.33333333333333,
    8.33333333333333,
    8.33333333333333,
    8.33333333333333,
    8.3974358974359,
    8.3974358974359,
    8.3974358974359,
    8.3974358974359,
    8.46153846153846,
    8.52564102564103,
    8.58974358974359,
    8.58974358974359,
    8.52564102564103,
    8.65384615384615,
    8.65384615384615,
    8.71794871794872,
    8.71794871794872,
    8.78205128205128,
    8.78205128205128,
    8.84615384615385,
    8.84615384615385,
    8.78205128205128,
    8.84615384615385,
    8.91025641025641,
    8.91025641025641,
    8.91025641025641,
    8.91025641025641,
    8.97435897435897,
    8.97435897435897,
    9.03846153846154,
    9.03846153846154,
    9.03846153846154,
    9.1025641025641,
    9.16666666666667,
    9.1025641025641,
    9.1025641025641,
    9.16666666666667,
    9.16666666666667,
    9.16666666666667,
    9.23076923076923,
    9.23076923076923,
    9.2948717948718,
    9.2948717948718,
    9.2948717948718,
    9.35897435897436,
    9.2948717948718,
    9.2948717948718,
    9.2948717948718,
    9.42307692307692,
    9.35897435897436,
    9.35897435897436,
    9.42307692307692,
    9.48717948717949,
    9.42307692307692,
    9.42307692307692,
    9.48717948717949,
    9.48717948717949,
    9.55128205128205,
    9.55128205128205,
    9.67948717948718,
    9.61538461538461,
    9.67948717948718,
    9.67948717948718,
    9.67948717948718,
    9.67948717948718,
    9.87179487179487,
    9.80769230769231,
    9.87179487179487,
    9.93589743589744,
    9.87179487179487,
    9.87179487179487,
    10,
    10,
    9.87179487179487,
    10,
    10,
    10,
    10.0641025641026,
    10.0641025641026,
    10.0641025641026,
    10.0641025641026,
    10.1923076923077,
    10.1282051282051,
    10.1282051282051,
    10.1923076923077,
    10.2564102564103,
    10.2564102564103,
    10.2564102564103,
    10.3205128205128,
    10.3205128205128,
    10.3205128205128,
    10.3205128205128,
    10.3846153846154,
    10.3846153846154,
    10.3846153846154,
    10.4487179487179,
    10.4487179487179,
    10.4487179487179,
    10.5128205128205,
    10.5769230769231,
    10.5769230769231,
    10.5769230769231,
    10.5128205128205,
    10.5769230769231,
    10.6410256410256,
    10.7051282051282,
    10.6410256410256,
    10.7051282051282,
    10.7051282051282,
    10.7692307692308,
    10.7051282051282,
    10.8333333333333,
    10.8974358974359,
    10.8974358974359,
    10.8974358974359,
    10.8974358974359,
    10.9615384615385,
    10.9615384615385,
    11.025641025641,
    10.9615384615385,
    11.025641025641,
    11.025641025641,
    11.0897435897436,
    11.0897435897436,
    11.0897435897436,
    11.0897435897436,
    11.0897435897436,
    11.0897435897436,
    11.1538461538462,
    11.1538461538462,
    11.2179487179487,
    11.2179487179487,
    11.2179487179487,
    11.2820512820513,
    11.2179487179487,
    11.2820512820513,
    11.2820512820513,
    11.3461538461538,
    11.2820512820513,
    11.3461538461538,
    11.3461538461538,
    11.3461538461538,
    11.3461538461538,
    11.4102564102564,
    11.474358974359,
    11.4102564102564,
    11.474358974359,
    11.474358974359,
    11.5384615384615,
    11.5384615384615,
    11.6025641025641,
    11.6025641025641,
    11.6666666666667,
    11.6666666666667,
    11.7307692307692,
    11.6666666666667,
    11.7307692307692,
    11.7948717948718,
    11.7948717948718,
    11.8589743589744,
    11.8589743589744,
    11.8589743589744,
    11.8589743589744,
    11.8589743589744,
    11.9230769230769,
    11.9230769230769,
    11.9871794871795,
    12.0512820512821,
    12.0512820512821,
    12.0512820512821,
    12.0512820512821,
    12.1153846153846,
    12.0512820512821,
    12.1153846153846,
    12.0512820512821,
    12.1794871794872,
    12.1153846153846,
    12.1794871794872,
    12.1794871794872,
    12.2435897435897,
    12.2435897435897,
    12.2435897435897,
    12.1794871794872,
    12.2435897435897,
    12.1794871794872,
    12.3076923076923,
    12.3076923076923,
    12.2435897435897,
    12.3076923076923,
    12.3717948717949,
    12.3076923076923,
    12.3076923076923,
    12.3717948717949,
    12.4358974358974,
    12.3717948717949,
    12.4358974358974,
    12.4358974358974,
    12.5,
    12.5641025641026,
    12.5641025641026,
    12.5,
    12.5641025641026,
    12.5641025641026,
    12.5641025641026,
    12.5641025641026,
    12.6282051282051,
    12.6282051282051,
    12.6282051282051,
    12.5641025641026,
    12.6282051282051,
    12.6923076923077,
    12.6923076923077,
    12.7564102564103,
    12.7564102564103,
    12.7564102564103,
    12.7564102564103,
    12.8205128205128,
    12.7564102564103,
    12.8205128205128,
    12.8846153846154,
    12.8846153846154,
    12.9487179487179,
    12.9487179487179,
    12.9487179487179,
    12.8846153846154,
    12.9487179487179,
    12.9487179487179,
    13.0128205128205,
    12.9487179487179,
    13.0128205128205,
    13.0128205128205,
    13.0128205128205,
    13.0128205128205,
    13.0128205128205,
    13.0769230769231,
    13.1410256410256,
    13.2051282051282,
    13.1410256410256,
    13.2051282051282,
    13.2051282051282,
    13.2051282051282,
    13.2692307692308,
    13.2692307692308,
    13.2692307692308,
    13.3333333333333,
    13.2692307692308,
    13.2692307692308,
    13.3333333333333,
    13.3333333333333,
    13.3333333333333,
    13.3333333333333,
    13.3333333333333,
    13.3974358974359,
    13.3974358974359,
    13.4615384615385,
    13.3974358974359,
    13.3974358974359,
    13.4615384615385,
    13.4615384615385,
    13.5897435897436,
    13.525641025641,
    13.5897435897436,
    13.5897435897436,
    13.5897435897436,
    13.6538461538462,
    13.6538461538462,
    13.6538461538462,
    13.7179487179487,
    13.7179487179487,
    13.7820512820513,
    13.7179487179487,
    13.7820512820513,
    13.7820512820513,
    13.7820512820513,
    13.8461538461538,
    13.8461538461538,
    13.8461538461538,
    13.9102564102564,
    13.9102564102564,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    13.974358974359,
    14.0384615384615,
    14.0384615384615,
    14.0384615384615,
    14.0384615384615,
    14.0384615384615,
    14.0384615384615,
    14.0384615384615,
    14.1025641025641,
    14.1025641025641,
    14.1025641025641,
    14.1666666666667,
    14.2307692307692,
    14.1666666666667,
    14.2307692307692,
    14.2307692307692,
    14.2307692307692,
    14.2948717948718,
    14.2948717948718,
    14.3589743589744,
    14.2948717948718,
    14.3589743589744,
    14.2948717948718,
    14.4230769230769,
    14.3589743589744,
    14.3589743589744,
    14.4230769230769,
    14.4230769230769,
    14.4230769230769,
    14.4871794871795,
    14.4871794871795,
    14.5512820512821,
    14.5512820512821,
    14.6153846153846,
    14.4871794871795,
    14.5512820512821,
    14.6153846153846,
    14.5512820512821,
    14.6153846153846,
    14.6153846153846,
    14.6794871794872,
    14.6153846153846,
    14.6794871794872,
    14.7435897435897,
    14.7435897435897,
    14.7435897435897,
    14.6794871794872,
    14.7435897435897,
    14.8076923076923,
    14.8076923076923,
    14.8076923076923,
    14.8076923076923,
    14.8717948717949,
    14.8717948717949,
    14.8717948717949,
    14.9358974358974,
    15,
    15,
    15,
    15.0641025641026,
    15.1282051282051,
    15.1282051282051,
    15.0641025641026,
    15.1282051282051,
    15.1282051282051,
    15.1923076923077,
    15.2564102564103,
    15.1923076923077,
    15.2564102564103,
    15.3205128205128,
    15.2564102564103,
    15.2564102564103,
    15.2564102564103,
    15.3205128205128,
    15.3846153846154,
    15.3846153846154,
    15.4487179487179,
    15.4487179487179,
    15.5128205128205,
    15.5128205128205,
    15.5128205128205,
    15.5769230769231,
    15.5769230769231,
    15.6410256410256,
    15.6410256410256,
    15.6410256410256,
    15.6410256410256,
    15.6410256410256,
    15.6410256410256,
    15.7051282051282,
    15.7051282051282,
    15.7692307692308,
    15.7692307692308,
    15.7692307692308,
    15.7692307692308,
    15.7692307692308,
    15.8333333333333,
    15.8333333333333,
    15.8333333333333,
    15.8333333333333,
    15.8974358974359,
    15.8974358974359,
    15.8974358974359,
    15.8974358974359,
    15.8974358974359,
    15.8974358974359,
    15.8974358974359,
    15.9615384615385,
    15.9615384615385,
    15.9615384615385,
    16.0897435897436,


};

float yyy[] = {
    56.5274151436031,
    56.8537859007833,
    57.1801566579634,
    57.5065274151436,
    57.8328981723238,
    58.1592689295039,
    58.4856396866841,
    58.8120104438642,
    59.1383812010444,
    59.4647519582245,
    59.7911227154047,
    60.1174934725849,
    60.443864229765,
    60.7702349869452,
    61.0966057441253,
    61.4229765013055,
    61.7493472584856,
    62.0757180156658,
    62.402088772846,
    62.7284595300261,
    63.0548302872063,
    63.3812010443864,
    63.7075718015666,
    64.0339425587467,
    64.3603133159269,
    64.686684073107,
    65.0130548302872,
    65.3394255874674,
    65.6657963446475,
    65.9921671018277,
    66.3185378590078,
    66.644908616188,
    66.9712793733681,
    67.2976501305483,
    67.6240208877285,
    67.9503916449086,
    68.2767624020888,
    68.6031331592689,
    68.9295039164491,
    69.2558746736293,
    69.5822454308094,
    69.9086161879896,
    70.2349869451697,
    70.5613577023499,
    70.88772845953,
    71.2140992167102,
    71.5404699738903,
    71.8668407310705,
    72.1932114882507,
    72.5195822454308,
    72.845953002611,
    73.1723237597911,
    73.4986945169713,
    73.8250652741514,
    74.1514360313316,
    74.4778067885117,
    74.8041775456919,
    75.1305483028721,
    75.4569190600522,
    75.7832898172324,
    76.1096605744125,
    76.4360313315927,
    76.7624020887728,
    77.088772845953,
    77.4151436031332,
    77.7415143603133,
    78.0678851174935,
    78.3942558746736,
    78.7206266318538,
    79.0469973890339,
    79.3733681462141,
    79.6997389033943,
    80.0261096605744,
    80.3524804177546,
    80.6788511749347,
    81.0052219321149,
    81.331592689295,
    81.6579634464752,
    81.9843342036554,
    82.3107049608355,
    82.6370757180157,
    82.9634464751958,
    83.289817232376,
    83.6161879895561,
    83.9425587467363,
    84.2689295039165,
    84.5953002610966,
    84.9216710182768,
    85.2480417754569,
    85.5744125326371,
    85.9007832898172,
    86.2271540469974,
    86.5535248041776,
    86.8798955613577,
    87.2062663185379,
    87.532637075718,
    87.8590078328982,
    88.1853785900783,
    88.5117493472585,
    88.8381201044386,
    89.1644908616188,
    89.490861618799,
    89.8172323759791,
    90.1436031331593,
    90.4699738903394,
    90.7963446475196,
    91.1227154046997,
    91.4490861618799,
    91.77545691906,
    92.1018276762402,
    92.4281984334204,
    92.7545691906005,
    93.0809399477807,
    93.4073107049608,
    93.733681462141,
    94.0600522193212,
    94.3864229765013,
    94.7127937336815,
    95.0391644908616,
    95.3655352480418,
    95.6919060052219,
    96.0182767624021,
    96.3446475195823,
    96.6710182767624,
    96.9973890339426,
    97.3237597911227,
    97.6501305483029,
    97.976501305483,
    98.3028720626632,
    98.6292428198433,
    98.9556135770235,
    99.2819843342037,
    99.6083550913838,
    99.934725848564,
    100.261096605744,
    100.587467362924,
    100.913838120104,
    101.240208877285,
    101.566579634465,
    101.892950391645,
    102.219321148825,
    102.545691906005,
    102.872062663185,
    103.198433420366,
    103.524804177546,
    103.851174934726,
    104.177545691906,
    104.503916449086,
    104.830287206266,
    105.156657963446,
    105.483028720627,
    105.809399477807,
    106.135770234987,
    106.462140992167,
    106.788511749347,
    107.114882506527,
    107.441253263708,
    107.767624020888,
    108.093994778068,
    108.420365535248,
    108.746736292428,
    109.073107049608,
    109.399477806789,
    109.725848563969,
    110.052219321149,
    110.378590078329,
    110.704960835509,
    111.031331592689,
    111.357702349869,
    111.68407310705,
    112.01044386423,
    112.33681462141,
    112.66318537859,
    112.98955613577,
    113.31592689295,
    113.642297650131,
    113.968668407311,
    114.295039164491,
    114.621409921671,
    114.947780678851,
    115.274151436031,
    115.600522193211,
    115.926892950392,
    116.253263707572,
    116.579634464752,
    116.906005221932,
    117.232375979112,
    117.558746736292,
    117.885117493473,
    118.211488250653,
    118.537859007833,
    118.864229765013,
    119.190600522193,
    119.516971279373,
    119.843342036554,
    120.169712793734,
    120.496083550914,
    120.822454308094,
    121.148825065274,
    121.475195822454,
    121.801566579634,
    122.127937336815,
    122.454308093995,
    122.780678851175,
    123.107049608355,
    123.433420365535,
    123.759791122715,
    124.086161879896,
    124.412532637076,
    124.738903394256,
    125.065274151436,
    125.391644908616,
    125.718015665796,
    126.044386422977,
    126.370757180157,
    126.697127937337,
    127.023498694517,
    127.349869451697,
    127.676240208877,
    128.002610966057,
    128.328981723238,
    128.655352480418,
    128.981723237598,
    129.308093994778,
    129.634464751958,
    129.960835509138,
    130.287206266319,
    130.613577023499,
    130.939947780679,
    131.266318537859,
    131.592689295039,
    131.919060052219,
    132.245430809399,
    132.57180156658,
    132.89817232376,
    133.22454308094,
    133.55091383812,
    133.8772845953,
    134.20365535248,
    134.530026109661,
    134.856396866841,
    135.182767624021,
    135.509138381201,
    135.835509138381,
    136.161879895561,
    136.488250652741,
    136.814621409922,
    137.140992167102,
    137.467362924282,
    137.793733681462,
    138.120104438642,
    138.446475195822,
    138.772845953003,
    139.099216710183,
    139.425587467363,
    139.751958224543,
    140.078328981723,
    140.404699738903,
    140.731070496084,
    141.057441253264,
    141.383812010444,
    141.710182767624,
    142.036553524804,
    142.362924281984,
    142.689295039165,
    143.015665796345,
    143.342036553525,
    143.668407310705,
    143.994778067885,
    144.321148825065,
    144.647519582245,
    144.973890339426,
    145.300261096606,
    145.626631853786,
    145.953002610966,
    146.279373368146,
    146.605744125326,
    146.932114882507,
    147.258485639687,
    147.584856396867,
    147.911227154047,
    148.237597911227,
    148.563968668407,
    148.890339425587,
    149.216710182768,
    149.543080939948,
    149.869451697128,
    150.195822454308,
    150.522193211488,
    150.848563968668,
    151.174934725849,
    151.501305483029,
    151.827676240209,
    152.154046997389,
    152.480417754569,
    152.806788511749,
    153.133159268929,
    153.45953002611,
    153.78590078329,
    154.11227154047,
    154.43864229765,
    154.76501305483,
    155.09138381201,
    155.417754569191,
    155.744125326371,
    156.070496083551,
    156.396866840731,
    156.723237597911,
    157.049608355091,
    157.375979112272,
    157.702349869452,
    158.028720626632,
    158.355091383812,
    158.681462140992,
    159.007832898172,
    159.334203655352,
    159.660574412533,
    159.986945169713,
    160.313315926893,
    160.639686684073,
    160.966057441253,
    161.292428198433,
    161.618798955614,
    161.945169712794,
    162.271540469974,
    162.597911227154,
    162.924281984334,
    163.250652741514,
    163.577023498695,
    163.903394255875,
    164.229765013055,
    164.556135770235,
    164.882506527415,
    165.208877284595,
    165.535248041775,
    165.861618798956,
    166.187989556136,
    166.514360313316,
    166.840731070496,
    167.167101827676,
    167.493472584856,
    167.819843342037,
    168.146214099217,
    168.472584856397,
    168.798955613577,
    169.125326370757,
    169.451697127937,
    169.778067885117,
    170.104438642298,
    170.430809399478,
    170.757180156658,
    171.083550913838,
    171.409921671018,
    171.736292428198,
    172.062663185379,
    172.389033942559,
    172.715404699739,
    173.041775456919,
    173.368146214099,
    173.694516971279,
    174.02088772846,
    174.34725848564,
    174.67362924282,
    175,
    175.32637075718,
    175.65274151436,
    175.97911227154,
    176.305483028721,
    176.631853785901,
    176.958224543081,
    177.284595300261,
    177.610966057441,
    177.937336814621,
    178.263707571802,
    178.590078328982,
    178.916449086162,
    179.242819843342,
    179.569190600522,
    179.895561357702,
    180.221932114883,
    180.548302872063,
    180.874673629243,
    181.201044386423,
    181.527415143603,
    181.853785900783,
    182.180156657963,
    182.506527415144,
    182.832898172324,
    183.159268929504,
    183.485639686684,
    183.812010443864,
    184.138381201044,
    184.464751958225,
    184.791122715405,
    185.117493472585,
    185.443864229765,
    185.770234986945,
    186.096605744125,
    186.422976501305,
    186.749347258486,
    187.075718015666,
    187.402088772846,
    187.728459530026,
    188.054830287206,
    188.381201044386,
    188.707571801567,
    189.033942558747,
    189.360313315927,
    189.686684073107,
    190.013054830287,
    190.339425587467,
    190.665796344648,
    190.992167101828,
    191.318537859008,
    191.644908616188,
    191.971279373368,
    192.297650130548,
    192.624020887728,
    192.950391644909,
    193.276762402089,
    193.603133159269,
    193.929503916449,
    194.255874673629,
    194.582245430809,
    194.90861618799,
    195.23498694517,
    195.56135770235,
    195.88772845953,
    196.21409921671,
    196.54046997389,
    196.866840731071,
    197.193211488251,
    197.519582245431,
    197.845953002611,
    198.172323759791,
    198.498694516971,
    198.825065274151,
    199.151436031332,
    199.477806788512,
    199.804177545692,
    200.130548302872,
    200.456919060052,
    200.783289817232,
    201.109660574413,
    201.436031331593,
    201.762402088773,
    202.088772845953,
    202.415143603133,
    202.741514360313,
    203.067885117493,
    203.394255874674,
    203.720626631854,
    204.046997389034,
    204.373368146214,
    204.699738903394,
    205.026109660574,
    205.352480417755,
    205.678851174935,
    206.005221932115,
    206.331592689295,
    206.657963446475,
    206.984334203655,
    207.310704960836,
    207.637075718016,
    207.963446475196,
    208.289817232376,
    208.616187989556,
    208.942558746736,
    209.268929503916,
    209.595300261097,
    209.921671018277,
    210.248041775457,
    210.574412532637,
    210.900783289817,
    211.227154046997,
    211.553524804178,
    211.879895561358,
    212.206266318538,
    212.532637075718,
    212.859007832898,
    213.185378590078,
    213.511749347259,
    213.838120104439,
    214.164490861619,
    214.490861618799,
    214.817232375979,
    215.143603133159,
    215.469973890339,
    215.79634464752,
    216.1227154047,
    216.44908616188,
    216.77545691906,
    217.10182767624,
    217.42819843342,
    217.754569190601,
    218.080939947781,
    218.407310704961,
    218.733681462141,
    219.060052219321,
    219.386422976501,
    219.712793733681,
    220.039164490862,
    220.365535248042,
    220.691906005222,
    221.018276762402,
    221.344647519582,
    221.671018276762,
    221.997389033943,
    222.323759791123,
    222.650130548303,
    222.976501305483,
    223.302872062663,
    223.629242819843,
    223.955613577024,
    224.281984334204,
    224.608355091384,
    224.934725848564,
    225.261096605744,
    225.587467362924,
    225.913838120104,
    226.240208877285,
    226.566579634465,
    226.892950391645,
    227.219321148825,
    227.545691906005,
    227.872062663185,
    228.198433420366,
    228.524804177546,
    228.851174934726,
    229.177545691906,
    229.503916449086,
    229.830287206266,
    230.156657963446,
    230.483028720627,
    230.809399477807,
    231.135770234987,
    231.462140992167,
    231.788511749347,
    232.114882506527,
    232.441253263708,
    232.767624020888,
    233.093994778068,
    233.420365535248,
    233.746736292428,
    234.073107049608,
    234.399477806789,
    234.725848563969,
    235.052219321149,
    235.378590078329,
    235.704960835509,
    236.031331592689,
    236.357702349869,
    236.68407310705,
    237.01044386423,
    237.33681462141,
    237.66318537859,
    237.98955613577,
    238.31592689295,
    238.642297650131,
    238.968668407311,
    239.295039164491,
    239.621409921671,
    239.947780678851,
    240.274151436031,
    240.600522193212,
    240.926892950392,
    241.253263707572,
    241.579634464752,
    241.906005221932,
    242.232375979112,
    242.558746736292,
    242.885117493473,
    243.211488250653,
    243.537859007833,
    243.864229765013,
    244.190600522193,
    244.516971279373,
    244.843342036554,
    245.169712793734,
    245.496083550914,
    245.822454308094,
    246.148825065274,
    246.475195822454,
    246.801566579634,
    247.127937336815,
    247.454308093995,
    247.780678851175,
    248.107049608355,
    248.433420365535,
    248.759791122715,
    249.086161879896,
    249.412532637076,
    249.738903394256,
    250.065274151436,
    250.391644908616,
    250.718015665796,
    251.044386422977,
    251.370757180157,
    251.697127937337,
    252.023498694517,
    252.349869451697,
    252.676240208877,
    253.002610966057,
    253.328981723238,
    253.655352480418,
    253.981723237598,
    254.308093994778,
    254.634464751958,
    254.960835509138,
    255.287206266319,
    255.613577023499,
    255.939947780679,
    256.266318537859,
    256.592689295039,
    256.919060052219,
    257.2454308094,
    257.57180156658,
    257.89817232376,
    258.22454308094,
    258.55091383812,
    258.8772845953,
    259.20365535248,
    259.530026109661,
    259.856396866841,
    260.182767624021,
    260.509138381201,
    260.835509138381,
    261.161879895561,
    261.488250652741,
    261.814621409922,
    262.140992167102,
    262.467362924282,
    262.793733681462,
    263.120104438642,
    263.446475195822,
    263.772845953003,
    264.099216710183,
    264.425587467363,
    264.751958224543,
    265.078328981723,
    265.404699738903,
    265.731070496084,
    266.057441253264,
    266.383812010444,
    266.710182767624,
    267.036553524804,
    267.362924281984,
    267.689295039165,
    268.015665796345,
    268.342036553525,
    268.668407310705,
    268.994778067885,
    269.321148825065,
    269.647519582245,
    269.973890339426,
    270.300261096606,
    270.626631853786,
    270.953002610966,
    271.279373368146,
    271.605744125326,
    271.932114882507,
    272.258485639687,
    272.584856396867,
    272.911227154047,
    273.237597911227,
    273.563968668407,
    273.890339425588,
    274.216710182768,
    274.543080939948,
    274.869451697128,
    275.195822454308,
    275.522193211488,
    275.848563968668,
    276.174934725849,
    276.501305483029,
    276.827676240209,
    277.154046997389,
    277.480417754569,
    277.806788511749,
    278.133159268929,
    278.45953002611,
    278.78590078329,
    279.11227154047,
    279.43864229765,
    279.76501305483,
    280.09138381201,
    280.417754569191,
    280.744125326371,
    281.070496083551,
    281.396866840731,
    281.723237597911,
    282.049608355091,
    282.375979112272,
    282.702349869452,
    283.028720626632,
    283.355091383812,
    283.681462140992,
    284.007832898172,
    284.334203655352,
    284.660574412533,
    284.986945169713,
    285.313315926893,
    285.639686684073,
    285.966057441253,
    286.292428198433,
    286.618798955614,
    286.945169712794,
    287.271540469974,
    287.597911227154,
    287.924281984334,
    288.250652741514,
    288.577023498695,
    288.903394255875,
    289.229765013055,
    289.556135770235,
    289.882506527415,
    290.208877284595,
    290.535248041776,
    290.861618798956,
    291.187989556136,
    291.514360313316,
    291.840731070496,
    292.167101827676,
    292.493472584856,
    292.819843342037,
    293.146214099217,
    293.472584856397,
    293.798955613577,
    294.125326370757,
    294.451697127937,
    294.778067885117,
    295.104438642298,
    295.430809399478,
    295.757180156658,
    296.083550913838,
    296.409921671018,
    296.736292428198,
    297.062663185379,
    297.389033942559,
    297.715404699739,
    298.041775456919,
    298.368146214099,
    298.694516971279,
    299.02088772846,
    299.34725848564,
    299.67362924282,


};


const uint kTableCount = 18;
const uint16_t kSampleTotalCnt = 100;
const float kTriParamA = -478.0f;
const float kTriParamB = 510100;
const float kTriParamC = -34.0f;

const uint kAngleIndex = 0;
const uint kRealDisIndex = 1;
const uint kPeakIndex = 2;
const uint kSampleDisIndex = 3;

const uint kTofSampleLineCnt = 17;



DynamicCalibration::DynamicCalibration(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::DynamicCalibration)
    ,currentMode(1)
    ,isStartSample(false)
    ,currentSampleCnt(0)
    ,triParamA(0.0f)
    ,triParamB(0.0f)
    ,triParamC(0.0f)
    ,maxPeak(500)
    ,minPeak(0)
    ,offsetAng(0.0)
{
    ui->setupUi(this);
    SetupGui();
    SetupTable();

    std::vector<float> x1,y1,ppp;
    for(uint m=0; m<746; m++) {
        x1.push_back(xxx[m]);
        y1.push_back(yyy[m]);
    }

    //nterpolationLookupTable(x1,y1,ppp);


}

void DynamicCalibration::SetupGui()
{
    ui->widget->setInteractions(QCP::iRangeDrag|QCP::iRangeZoom| QCP::iSelectAxes
                                                  |QCP::iSelectLegend | QCP::iSelectPlottables);

    ui->widget->plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
    ui->widget->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    //@1主视图的轴
    QCPLayoutGrid *subLayout = new QCPLayoutGrid;
    //@3分配视图
    ui->widget->plotLayout()->addElement(0, 0, subLayout);     // 在第一行添加轴矩形

    subRectLeft = new QCPAxisRect(ui->widget, true);   // 不配置轴

    subLayout->addElement(0, 0, subRectLeft);                      // 在第一列添加轴矩形

    QPen *pen = new QPen();
    subGraphRando = ui->widget->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
    subGraphRando->setLineStyle(QCPGraph::lsLine);
    subGraphRando->setScatterStyle(QCPScatterStyle::ssNone);
    pen->setWidthF(2.0);
    pen->setColor(QColor(255,128,0));
    subGraphRando->setPen(*pen);
    subGraphRando->rescaleKeyAxis();

    subGraphRandoPoint = ui->widget->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
    subGraphRandoPoint->setScatterStyle(QCPScatterStyle::ssDisc);
    subGraphRandoPoint->setLineStyle(QCPGraph::lsImpulse);
    pen->setColor(QColor(0,0,0));
    subGraphRandoPoint->setPen(*pen);
    subGraphRandoPoint->rescaleKeyAxis();
    subRectLeft->axis(QCPAxis::atLeft)->ticker()->setTickCount(8); // 设置轴的刻度为一个固定的步进值
    subRectLeft->axis(QCPAxis::atBottom)->ticker()->setTickCount(8);
    subRectLeft->axis(QCPAxis::atLeft)->setTickPen(QPen(QColor(50, 50, 50),2.0));
    subRectLeft->axis(QCPAxis::atBottom)->setTickPen(QPen(QColor(50, 50, 50),2.0));
    //subRectLeft->axis(QCPAxis::atBottom)->setLabel(QString("tof"));
    subRectLeft->axis(QCPAxis::atLeft)->setLabel(QString("distance/mm"));
    subRectLeft->axis(QCPAxis::atBottom)->setTickLabelRotation(0);
    subRectLeft->axis(QCPAxis::atLeft)->setTickLabelRotation(0);
    subRectLeft->axis(QCPAxis::atBottom)->setLabelFont(QFont("仿宋",12,QFont::Bold));
    subRectLeft->axis(QCPAxis::atLeft)->setLabelFont(QFont("仿宋",12,QFont::Bold));
    subRectLeft->axis(QCPAxis::atBottom)->setRange(0,600);
    subRectLeft->axis(QCPAxis::atLeft)->setRange(0,12000);
    subRectLeft->axis(QCPAxis::atLeft)->setUpperEnding(QCPLineEnding::esSpikeArrow);
    subRectLeft->axis(QCPAxis::atBottom)->setUpperEnding(QCPLineEnding::esSpikeArrow);
    subRectLeft->axis(QCPAxis::atLeft)->grid()->setVisible(true);
    subRectLeft->axis(QCPAxis::atBottom)->grid()->setVisible(true);

    ui->widget->show();

    ui->comboBoxMode->addItem("mini");
    ui->comboBoxMode->addItem("tof");
    ui->comboBoxMode->setCurrentIndex(1);


//    Eigen::MatrixXf MatriAA(20,3);
//    Eigen::VectorXf VectorB(20,1);


//    uint nn=0;
//    for(nn=0; nn<20; nn++) {
//        MatriAA(nn,0) = mmm[nn][0];
//        MatriAA(nn,1) = mmm[nn][1];
//        MatriAA(nn,2) = mmm[nn][2];
//        VectorB(nn,0) = bb[nn][0];
//    }

//    Eigen::Vector3f  VectorParam = MatriAA.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(VectorB);


}

void DynamicCalibration::SetupTable()
{
    for(uint16_t m=0; m<kTableCount; m++) { /*先建立表格模式*/
        ui->tableWidget->setItem(m,0,new QTableWidgetItem(""));
        ui->tableWidget->setItem(m,1,new QTableWidgetItem(""));
        ui->tableWidget->setItem(m,2,new QTableWidgetItem(""));
        ui->tableWidget->setItem(m,3,new QTableWidgetItem(""));
        ui->tableWidget->item(m,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        ui->tableWidget->item(m,1)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        ui->tableWidget->item(m,2)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        ui->tableWidget->item(m,3)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
    }


    ui->tableWidget->item(0,0)->setText("angle(°)");
    ui->tableWidget->item(0,1)->setText("real Dis(mm)");
    ui->tableWidget->item(0,2)->setText("peak");
    ui->tableWidget->item(0,3)->setText("spl Dis(mm)");

    ui->tableWidget->show();
}

DynamicCalibration::~DynamicCalibration()
{
    delete ui;
}

void DynamicCalibration::on_comboBoxMode_currentIndexChanged(int index)
{
    static bool skipFirst = true;
    if(skipFirst == false) {
        currentMode = index;
        if(currentMode == 0) {
            //subRectLeft->axis(QCPAxis::atBottom)->setLabel(QString("centroid"));
        }
        else  if(currentMode == 1) {
            //subRectLeft->axis(QCPAxis::atBottom)->setLabel(QString("tof"));
        }
    }
    skipFirst = false;
}

void DynamicCalibration::ReceivePointCloudData(std::vector<ProtocolData> data)
{
    int sampleCnt = sampleAngle.size();
    if(ui->radioButton->isChecked()) {//rolling data
        ProtocolData  tmpData = data.at(0);
        uint16_t  pointNum = tmpData.data.size();
        float angle = 0.0f;
        float deepth=0;
        uint16_t intensity=0;

        float sAngle[kTofSampleLineCnt] = {0};
        float realDis[kTofSampleLineCnt] = {0};
        std::vector<float> angleDeta;
        for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
            realDis[m] = ui->tableWidget->item(m+1,kRealDisIndex)->text().toFloat();
        }

        for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
            sAngle[m] = ui->tableWidget->item(m+1,kAngleIndex)->text().toFloat();
        }

        QVector<QCPGraphData> dataP(kTofSampleLineCnt);

        for(uint i=0; i<kTofSampleLineCnt; i++) {
           dataP[i].value = realDis[i];

        }
        angleDeta.resize(kTofSampleLineCnt);
        for(uint16_t m=0; m<pointNum; m++) {
            angle = tmpData.data.at(m).angle;

            for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
                angleDeta.at(m) = fabs(sAngle[m] - angle);
            }

            deepth = 0;
            if(currentMode == kMiniMode) {
                uint16_t tmpDeepth = tmpData.data.at(m).deepth;
                if(tmpDeepth != 0) {
                    deepth = ((kTriParamB-(kTriParamA)*(kTriParamC))/(tmpDeepth-(kTriParamA))) - (kTriParamC);
                }
                else {
                    deepth = 0;
                }
                intensity = tmpData.data.at(m).indensity;
            }
            else if(currentMode == kTofMode) {
                deepth = tmpData.data.at(m).deepth/100.0;
                intensity = tmpData.data.at(m).indensity;
            }


            for(uint16_t n=0; n<kTofSampleLineCnt; n++) {
                if(angleDeta.at(n) <= 0.5f) {
                    if(fabs(deepth) > 0.001) {
                        ui->tableWidget->item(n+1,kSampleDisIndex)->setText(QString::number(deepth,'f',2));


                        dataP[n].key = deepth;
                        ui->tableWidget->item(n+1,kPeakIndex)->setText(QString::number(intensity,10));
                    }


                }
            }

        }
        if(currentMode == kMiniMode) {
            QVector<QCPGraphData> dataPP(3);
            if(dataP[0].key > 0.001 && dataP[1].key > 0.001 && dataP[1].key > 0.001) {
                dataPP[0].key = dataP[0].key;
                dataPP[0].value = dataP[0].value;

                dataPP[1].key = dataP[1].key;
                dataPP[1].value = dataP[1].value;
                dataPP[2].key = dataP[2].key;
                dataPP[2].value = dataP[2].value;
                subGraphRandoPoint->setLineStyle(QCPGraph::lsNone);
                subGraphRandoPoint->data()->set(dataPP);
            }

        }
        else if(currentMode == kTofMode) {
            subGraphRandoPoint->setLineStyle(QCPGraph::lsImpulse);
            subGraphRandoPoint->data()->set(dataP);
        }
        subGraphRando->data()->clear();
        ui->widget->replot(QCustomPlot::rpQueuedReplot);
    }
    else {//dynamic data

        if(sampleCnt == 0 || isStartSample == false) {
            return;
        }
        //std::vector<ProtocolData>
        ProtocolData  tmpData = data.at(0);
        uint16_t  pointNum = tmpData.data.size();
        float angle = 0.0f;
        float deepth=0;
        uint16_t intensity=0;
        if(currentMode == kMiniMode && sampleDistance.size() == 3) {//trigonometry
            for(uint16_t m=0; m<pointNum; m++) {
                angle = tmpData.data.at(m).angle;
                deepth = tmpData.data.at(m).deepth;
                intensity = tmpData.data.at(m).indensity;

                float deta1 = fabs(sampleAngle.at(0) - angle);
                float deta2 = fabs(sampleAngle.at(1) - angle);
                float deta3 = fabs(sampleAngle.at(2) - angle);
                if(deta1 < 1.0f) {
                    if(tmpData.data.at(m).deepth != 0) {
                        sampleDistance.at(0).push_back(deepth);
                    }
                    if(tmpData.data.at(m).indensity != 0) {
                        sampleIntensity.at(0).push_back(intensity);
                    }
                    ui->tableWidget->item(1,kSampleDisIndex)->setText(QString::number(deepth,'f',2));
                    ui->tableWidget->item(1,kPeakIndex)->setText(QString::number(intensity,10));
                }
                if(deta2 < 1.0f) {
                    if(tmpData.data.at(m).deepth != 0) {
                        sampleDistance.at(1).push_back(deepth);
                    }
                    if(tmpData.data.at(m).indensity != 0) {
                        sampleIntensity.at(1).push_back(intensity);
                    }
                    ui->tableWidget->item(2,kSampleDisIndex)->setText(QString::number(deepth,'f',2));
                    ui->tableWidget->item(2,kPeakIndex)->setText(QString::number(intensity,10));
                }
                if(deta3 < 1.0f) {
                    if(tmpData.data.at(m).deepth != 0) {
                        sampleDistance.at(2).push_back(deepth);
                    }
                    if(tmpData.data.at(m).indensity != 0) {
                        sampleIntensity.at(2).push_back(intensity);
                    }
                    ui->tableWidget->item(3,kSampleDisIndex)->setText(QString::number(deepth,'f',2));
                    ui->tableWidget->item(3,kPeakIndex)->setText(QString::number(intensity,10));
                }
            }
            currentSampleCnt++;
            if(currentSampleCnt >= kSampleTotalCnt) {
                isStartSample = false;
                Eigen::Matrix<double, 3, 2> samMatri , peakMatri;/*centroid distance*/  /*centroid peak*/
                std::vector<float> realDis, sampleDis, samplePeak, retCentroid;
                //for(uint16_t m=0; m<3; m++) {
                    sampleDis.push_back(accumulate(sampleDistance.at(0).begin(),sampleDistance.at(0).end(), 0)/((float)sampleDistance.at(0).size()));
                    samplePeak.push_back(accumulate(sampleIntensity.at(0).begin(),sampleIntensity.at(0).end(), 0)/((float)sampleIntensity.at(0).size()));
                    ui->tableWidget->item(1,kSampleDisIndex)->setText(QString::number(sampleDis.at(0),'f',4));
                    ui->tableWidget->item(1,kPeakIndex)->setText(QString::number(samplePeak.at(0),'f',4));

                    sampleDis.push_back(accumulate(sampleDistance.at(1).begin(),sampleDistance.at(1).end(), 0)/((float)sampleDistance.at(1).size()));
                    samplePeak.push_back(accumulate(sampleIntensity.at(1).begin(),sampleIntensity.at(1).end(), 0)/((float)sampleIntensity.at(1).size()));
                    ui->tableWidget->item(2,kSampleDisIndex)->setText(QString::number(sampleDis.at(1),'f',4));
                    ui->tableWidget->item(2,kPeakIndex)->setText(QString::number(samplePeak.at(1),'f',4));

                    sampleDis.push_back(accumulate(sampleDistance.at(2).begin(),sampleDistance.at(2).end(), 0)/((float)sampleDistance.at(2).size()));
                    samplePeak.push_back(accumulate(sampleIntensity.at(2).begin(),sampleIntensity.at(2).end(), 0)/((float)sampleIntensity.at(2).size()));
                    ui->tableWidget->item(3,kSampleDisIndex)->setText(QString::number(sampleDis.at(2),'f',4));
                    ui->tableWidget->item(3,kPeakIndex)->setText(QString::number(samplePeak.at(2),'f',4));
                //}
                /*calc fit paramters*/
                //1 get real distance
                realDis.push_back(ui->tableWidget->item(1,kRealDisIndex)->text().toFloat());
                realDis.push_back(ui->tableWidget->item(2,kRealDisIndex)->text().toFloat());
                realDis.push_back(ui->tableWidget->item(3,kRealDisIndex)->text().toFloat());

                retCentroid = CalcTriParam(realDis,sampleDis,samplePeak);

                QVector<QCPGraphData> dataP(3);

                for(int i=0; i<3; i++) {
                   dataP[i].key = retCentroid.at(i);
                   dataP[i].value = realDis.at(i);
                }
                subGraphRandoPoint->data()->set(dataP);


                QVector<QCPGraphData> data(13500);
                float key=0;
                for(int n=0; n<13500; n++) {
                    key = n/10.0f;
                    data[n].key = key;
                    data[n].value = (triParamA*key+triParamB)/(key+triParamC);
                }

                subGraphRando->data()->set(data);

                ui->widget->replot(QCustomPlot::rpQueuedReplot);

                QString log;
                log = "current centroid: " + QString::number(retCentroid.at(0),'f',4)+"  "+QString::number(retCentroid.at(1),'f',4)+"  "+QString::number(retCentroid.at(2),'f',4);
                ui->textEditDisLog->append(log);
                log = "tri a b c: " + QString::number(triParamA,'f',4)+"  "+QString::number(triParamB,'f',4)+"  "+QString::number(triParamC,'f',4);
                ui->textEditDisLog->append(log);
                log = "tri p2 p1 p0: " + QString::number(triParamP2,'f',4)+"  "+QString::number(triParamP1,'f',4)+"  "+QString::number(triParamP0,'f',4);
                ui->textEditPeakLog->append(log);



            }
        }
        else if(currentMode == kTofMode && (sampleDistance.size() == kTableCount-1)) {//tof
            float sAngle[kTofSampleLineCnt] = {0};
            //float realDis[kTofSampleLineCnt] = {0};
            std::vector<float> angleDeta,realDis;
            for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
                realDis.push_back(ui->tableWidget->item(m+1,kRealDisIndex)->text().toFloat());
            }

            for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
                sAngle[m] = ui->tableWidget->item(m+1,kAngleIndex)->text().toFloat();
            }

            QVector<QCPGraphData> dataP(kTofSampleLineCnt);

//            for(uint i=0; i<kTofSampleLineCnt; i++) {
//               dataP[i].value = realDis[i];

//            }
            angleDeta.resize(kTofSampleLineCnt);
            for(uint16_t m=0; m<pointNum; m++) {
                angle = tmpData.data.at(m).angle;

                for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
                    angleDeta.at(m) = fabs(sAngle[m] - angle);
                }

                deepth = 0;
                if(currentMode == kTofMode) {
                    float tmpDeepth = tmpData.data.at(m).deepth/100.0;
                    if(fabs(tmpDeepth) > 0.001) {
                        deepth = tmpDeepth;
                    }
                    else {
                        deepth = 0;
                    }
                    intensity = tmpData.data.at(m).indensity;
                }


                for(uint16_t n=0; n<kTofSampleLineCnt; n++) {
                    if(angleDeta.at(n) <= 0.5f) {
                        if(deepth > 0.001) {
                            ui->tableWidget->item(n+1,kSampleDisIndex)->setText(QString::number(deepth,'f',2));
                            ui->tableWidget->item(n+1,kPeakIndex)->setText(QString::number(intensity,10));
                            sampleDistance.at(n).push_back(deepth);
                            sampleIntensity.at(n).push_back(intensity);

                        }
                    }
                }
            }

            currentSampleCnt++;
            if(currentSampleCnt >= kSampleTotalCnt) {
                isStartSample = false;
                //Eigen::Matrix<double, 3, 2> samMatri , peakMatri;/*centroid distance*/  /*centroid peak*/
                std::vector<float> sampleDis, samplePeak, retCentroid;
                for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
                    sampleDis.push_back(accumulate(sampleDistance.at(m).begin(),sampleDistance.at(m).end(), 0.0)/((float)sampleDistance.at(m).size()));
                    samplePeak.push_back(accumulate(sampleIntensity.at(m).begin(),sampleIntensity.at(m).end(), 0.0)/((float)sampleIntensity.at(m).size()));
                    ui->tableWidget->item(m+1,kSampleDisIndex)->setText(QString::number(sampleDis.at(m),'f',4));
                    ui->tableWidget->item(m+1,kPeakIndex)->setText(QString::number(samplePeak.at(m),'f',4));
                }
                /*calc fit paramters*/
                //1 get real distance


                QVector<QCPGraphData> data(kTofSampleLineCnt);
                for(uint n=0; n<kTofSampleLineCnt; n++) {
                    data[n].key = sampleDis.at(n);
                    data[n].value = realDis.at(n);
                }
                subGraphRando->data()->clear();
                subGraphRandoPoint->setLineStyle(QCPGraph::lsImpulse);
                subGraphRandoPoint->data()->set(data);

                //AllLinearFitAndMarkpoint(sampleDis,realDis);

                ui->widget->replot(QCustomPlot::rpQueuedReplot);

                ui->widget->replot(QCustomPlot::rpQueuedReplot);
            }
        }
    }

}

void DynamicCalibration::on_pushButtonDynamicCalibration_clicked()
{
    int sampleCnt=0;
    sampleAngle.clear();
    std::vector<uint8_t> tmpIndex;
    for(uint8_t m=1; m<kTableCount; m++) {
        if(ui->tableWidget->item(m,kAngleIndex)->text() != "" ) {
           sampleCnt++;
           tmpIndex.push_back(m);
           sampleAngle.push_back(ui->tableWidget->item(m,0)->text().toFloat());
        }
    }

    if(sampleCnt == 0) {
        QMessageBox::warning(NULL, "warning", "enter valid sample angle please!");
        return;
    }

    if(currentMode == kMiniMode) {
        if(sampleCnt < 3) {
            QMessageBox::warning(NULL, "warning", "enter first 3 angle value at miniMode please!");
            return;
        }
        for(uint8_t m=0; m<3; m++) {
            if(tmpIndex.at(m) != (m+1)) {
                QMessageBox::warning(NULL, "warning", "enter valid sample angle at miniMode please!");
                return;
            }
        }
        sampleDistance.clear();
        sampleIntensity.clear();
        sampleDistance.resize(3);
        sampleIntensity.resize(3);
    }

    if(currentMode == kTofMode) {
        if(sampleCnt < uint8_t(kTableCount-1)) {
            QMessageBox::warning(NULL, "warning", "enter all angle value at tofMode please!");
            return;
        }
        for(uint8_t m=0; m<(kTableCount-1); m++) {
            if(tmpIndex.at(m) != (m+1)) {
                QMessageBox::warning(NULL, "warning", "enter valid sample angle at tofMode please!");
                return;
            }
        }
        sampleDistance.clear();
        sampleIntensity.clear();
        sampleDistance.resize(kTableCount-1);
        sampleIntensity.resize(kTableCount-1);
    }
    ui->textEditDisLog->clear();
    ui->textEditPeakLog->clear();
    currentSampleCnt = 0;
    isStartSample = true;
}

void DynamicCalibration::on_pushButtonStaticCalibration_clicked()
{
    std::vector<float> realDis, sampleDis, samplePeak,retCentroid;
    maxPeak = 0;
    minPeak = 0;
    if(currentMode == kMiniMode) {//trigonometry
        realDis.push_back(ui->tableWidget->item(1,kRealDisIndex)->text().toFloat());
        realDis.push_back(ui->tableWidget->item(2,kRealDisIndex)->text().toFloat());
        realDis.push_back(ui->tableWidget->item(3,kRealDisIndex)->text().toFloat());

        sampleDis.push_back(ui->tableWidget->item(1,kSampleDisIndex)->text().toFloat());
        sampleDis.push_back(ui->tableWidget->item(2,kSampleDisIndex)->text().toFloat());
        sampleDis.push_back(ui->tableWidget->item(3,kSampleDisIndex)->text().toFloat());

        samplePeak.push_back(ui->tableWidget->item(1,kPeakIndex)->text().toFloat());
        samplePeak.push_back(ui->tableWidget->item(2,kPeakIndex)->text().toFloat());
        samplePeak.push_back(ui->tableWidget->item(3,kPeakIndex)->text().toFloat());

        auto iter = std::find(realDis.begin(), realDis.end(), 0);
        auto iter1 = std::find(sampleDis.begin(), sampleDis.end(), 0);
        auto iter2 = std::find(samplePeak.begin(), samplePeak.end(), 0);
        if (iter != realDis.begin() && iter1 != sampleDis.begin() && iter2 != samplePeak.begin())
        {
            retCentroid = CalcTriParam(realDis,sampleDis,samplePeak);
            ui->textEditDisLog->clear();
            ui->textEditPeakLog->clear();


            QVector<QCPGraphData> dataP(3);

            for(int i=0; i<3; i++) {
               dataP[i].key = retCentroid.at(i);
               dataP[i].value = realDis.at(i);
            }
            subGraphRandoPoint->data()->set(dataP);


            QVector<QCPGraphData> data(13500);
            float key=0;
            for(int n=0; n<13500; n++) {
                key = n/10.0f;
                data[n].key = key;
                data[n].value = (triParamA*key+triParamB)/(key+triParamC);
            }
            subGraphRandoPoint->setLineStyle(QCPGraph::lsNone);
            subGraphRando->data()->set(data);

            ui->widget->replot(QCustomPlot::rpQueuedReplot);

            QString log;
            log = "current centroid: " + QString::number(retCentroid.at(0),'f',4)+"  "+QString::number(retCentroid.at(1),'f',4)+"  "+QString::number(retCentroid.at(2),'f',4);
            ui->textEditDisLog->append(log);
            log = "tri a b c: " + QString::number(triParamA,'f',4)+"  "+QString::number(triParamB,'f',4)+"  "+QString::number(triParamC,'f',4);
            ui->textEditDisLog->append(log);
            log = "tri p2 p1 p0: " + QString::number(triParamP2,'f',4)+"  "+QString::number(triParamP1,'f',4)+"  "+QString::number(triParamP0,'f',4);
            ui->textEditPeakLog->append(log);
        }
        else {
            QMessageBox::warning(NULL, "warning", "have some invalid data");
        }
    }
    else if(currentMode == kTofMode){
        std::vector<float> realDis,sampleDis;
        for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
            realDis.push_back(ui->tableWidget->item(m+1,kRealDisIndex)->text().toFloat());
        }
        for(uint16_t m=0; m<kTofSampleLineCnt; m++) {
            sampleDis.push_back(ui->tableWidget->item(m+1,kSampleDisIndex)->text().toFloat());
        }
        AllLinearFitAndMarkpoint(sampleDis,realDis);
        /*select minValue peak @<1000mm*/
        minPeak = 10000,maxPeak = 0;
        for(uint8_t m=0; m<10; m++) {
            uint peak = (uint)ui->tableWidget->item(m+1,kPeakIndex)->text().toFloat();
            if(peak < minPeak) {
              minPeak  = peak;
            }
            if(peak > maxPeak) {
              maxPeak  = peak;
            }
        }
    }


}

void DynamicCalibration::on_actionTriTransmit_triggered()
{
    uchar str[29] = {0},checksum = 0;
    str[0] = 0xA5;
    str[1] = 0xF1;
    str[2] = triParamA > 0? 1 : 2;
    str[3] = (uint32_t(fabs(triParamA)*10000))>>16;
    str[4] = (uint32_t(fabs(triParamA)*10000))>>8;
    str[5] = (uint32_t(fabs(triParamA)*10000));
    str[6] = triParamB > 0? 1 : 2;
    str[7] = (uint32_t(fabs(triParamB)))>>16;
    str[8] = (uint32_t(fabs(triParamB)))>>8;
    str[9] = (uint32_t(fabs(triParamB)));
    str[10] = triParamC > 0? 1 : 2;
    str[11] = (uint32_t(fabs(triParamC)*1000))>>16;
    str[12] = (uint32_t(fabs(triParamC)*1000))>>8;
    str[13] = (uint32_t(fabs(triParamC)*1000));
    str[14] = 0x00;
    str[15] = 0x01;

    str[16] = triParamP2 > 0? 1 : 2;
    str[17] = (uint32_t(fabs(triParamP2)*1000000))>>16;
    str[18] = (uint32_t(fabs(triParamP2)*1000000))>>8;
    str[19] = (uint32_t(fabs(triParamP2)*1000000));
    str[20] = triParamP1 > 0? 1 : 2;
    str[21] = (uint32_t(fabs(triParamP1*10000)))>>16;
    str[22] = (uint32_t(fabs(triParamP1*10000)))>>8;
    str[23] = (uint32_t(fabs(triParamP1*10000)));
    str[24] = triParamP0 > 0? 1 : 2;
    str[25] = (uint32_t(fabs(triParamP0)*100))>>16;
    str[26] = (uint32_t(fabs(triParamP0)*100))>>8;
    str[27] = (uint32_t(fabs(triParamP0)*100));


    for(uint8_t i=0; i<28; i++)
    {
        checksum ^= str[i];
    }
    str[28] = checksum;
    QByteArray st((char *)str,29);
    emit TransmitSerialCmd(st);
}

void DynamicCalibration::on_actionTriClear_triggered()
{
    uchar str[29] = {0},checksum = 0;
    str[0] = 0xA5;
    str[1] = 0xF1;
    str[2] = 0;
    str[3] = 0;
    str[4] = 0;
    str[5] = 0;
    str[6] = 0;
    str[7] = 0;
    str[8] = 0;
    str[9] = 0;
    str[10] = 0;
    str[11] = 0;
    str[12] = 0;
    str[13] = 0;
    str[14] = 0;
    str[15] = 0;

    str[16] = 0;
    str[17] = 0;
    str[18] = 0;
    str[19] = 0;
    str[20] = 0;
    str[21] = 0;
    str[22] = 0;
    str[23] = 0;
    str[24] = 0;
    str[25] = 0;
    str[26] = 0;
    str[27] = 0;


    for(uint8_t i=0; i<28; i++)
    {
        checksum ^= str[i];
    }
    str[28] = checksum;
    QByteArray st((char *)str,29);
    emit TransmitSerialCmd(st);
}

void DynamicCalibration::on_actionTofTransmit_triggered()
{
    //tofLinearFitParam
    if(tofLinearFitParam.size() == 0 || tofLinearFitMark.size() ==0) {
        QMessageBox::warning(NULL, "warning", "linear Param is empty!");
        return;
    }
    std::vector<float> p;
    p.push_back(0);
    p.push_back(tofLinearFitParam.at(0).at(0));
    p.push_back(tofLinearFitParam.at(0).at(1));
    p.push_back(tofLinearFitMark.at(0).at(0));/*markPoint 600*/

    p.push_back(tofLinearFitParam.at(1).at(0));
    p.push_back(tofLinearFitParam.at(1).at(1));
    p.push_back(tofLinearFitParam.at(1).at(2));
    p.push_back(tofLinearFitMark.at(1).at(0));/*markPoint 1000*/

    p.push_back(0);
    p.push_back(tofLinearFitParam.at(2).at(0));
    p.push_back(tofLinearFitParam.at(2).at(1));
    p.push_back(tofLinearFitMark.at(2).at(0));/*markPoint 6000*/

    p.push_back(0);
    p.push_back(tofLinearFitParam.at(3).at(0));
    p.push_back(tofLinearFitParam.at(3).at(1));
    p.push_back(tofLinearFitMark.at(3).at(0));

    p.push_back(0);
    p.push_back(tofLinearFitParam.at(4).at(0));
    p.push_back(tofLinearFitParam.at(4).at(1));
    p.push_back(tofLinearFitMark.at(4).at(0));

    p.push_back(0);
    p.push_back(tofLinearFitParam.at(5).at(0));
    p.push_back(tofLinearFitParam.at(5).at(1));
    p.push_back(tofLinearFitMark.at(5).at(0));

    p.push_back(0);
    p.push_back(tofLinearFitParam.at(6).at(0));
    p.push_back(tofLinearFitParam.at(6).at(1));
    p.push_back(tofLinearFitMark.at(6).at(0));

    p.push_back(maxPeak);
    p.push_back(minPeak);
    p.push_back(0);
    p.push_back(0);

    /*传输拟合数据*/
    emit TransmitCalibrationData(p);
}

void DynamicCalibration::on_actionTofClear_triggered()
{

}

std::vector<float>  DynamicCalibration::CalcTriParam(std::vector<float> realDis,std::vector<float> sampleDis,std::vector<float> samplePeak)
{
    Eigen::Matrix<double, 3, 2> samMatri , peakMatri;/*centroid distance*/  /*centroid peak*/
    std::vector<float> centroid;

    /*calc fit paramters*/
    //1 get real distance
    samMatri(0,1) = realDis.at(0);
    samMatri(1,1) = realDis.at(1);
    samMatri(2,1) = realDis.at(2);
    //2 push back centroid
    samMatri(0,0) = ((kTriParamB-(kTriParamA)*(kTriParamC))/(sampleDis.at(0)-(kTriParamA))) - (kTriParamC);//(b-ac)/(y-a) -  c
    samMatri(1,0) = ((kTriParamB-(kTriParamA)*(kTriParamC))/(sampleDis.at(1)-(kTriParamA))) - (kTriParamC);
    samMatri(2,0) = ((kTriParamB-(kTriParamA)*(kTriParamC))/(sampleDis.at(2)-(kTriParamA))) - (kTriParamC);
    peakMatri(0,0) = samMatri(0,0);
    peakMatri(1,0) = samMatri(1,0);
    peakMatri(2,0) = samMatri(2,0);
    peakMatri(0,1) = samplePeak.at(0);
    peakMatri(1,1) = samplePeak.at(1);
    peakMatri(2,1) = samplePeak.at(2);

    //3 cal fit parameters
    /*centroid-distance*/
    Eigen::Matrix3d MatriA;//ax + b -cy = x*y
    MatriA <<   samMatri(0,0),1,-samMatri(0,1),
                samMatri(1,0),1,-samMatri(1,1),
                samMatri(2,0),1,-samMatri(2,1);
    float determ = MatriA.determinant();
    if(fabs(determ) < 0.001f ) {
        qDebug()<< "determinant" << determ;
        QMessageBox::warning(NULL, "warning", "centroid-distance determinant is 0!");
        centroid.resize(3);
        return centroid;
    }

    Eigen::Matrix<double, 3, 1> VectorB;
    VectorB<< samMatri(0,0)*samMatri(0,1),samMatri(1,0)*samMatri(1,1),samMatri(2,0)*samMatri(2,1);

    Eigen::Vector3d VectorX,VectorX2;
    VectorX = MatriA.inverse()*VectorB;
    VectorX2 = MatriA.colPivHouseholderQr().solve(VectorB);

    triParamA = VectorX.x();
    triParamB = VectorX.y();
    triParamC = VectorX.z();
    std::cout<< "***centroid-distance***"<<std::endl;
    std::cout<< VectorX <<std::endl;
    std::cout<< VectorX2 <<std::endl;

    centroid.push_back(samMatri(0,0));
    centroid.push_back(samMatri(1,0));
    centroid.push_back(samMatri(2,0));

    /*centroid-peak*/
    MatriA <<   peakMatri(0,0)*peakMatri(0,0),peakMatri(0,0),1,
                peakMatri(1,0)*peakMatri(1,0),peakMatri(1,0),1,
                peakMatri(2,0)*peakMatri(2,0),peakMatri(2,0),1;
    determ = MatriA.determinant();
    if(fabs(determ) < 0.001f ) {
        qDebug()<< "determinant" << determ;
        QMessageBox::warning(NULL, "warning", "centroid-peak determinant is 0!");
        centroid.resize(3);
        return centroid;
    }


    VectorB<< peakMatri(0,1),peakMatri(1,1),peakMatri(2,1);


    VectorX = MatriA.inverse()*VectorB;
    VectorX2 = MatriA.colPivHouseholderQr().solve(VectorB);

    triParamP2 = VectorX.x();
    triParamP1 = VectorX.y();
    triParamP0 = VectorX.z();


    std::cout<< "***centroid-peak***"<<std::endl;
    std::cout<< VectorX <<std::endl;
    std::cout<< VectorX2 <<std::endl;
    return centroid;
}

void DynamicCalibration::on_actionImptParam_triggered()
{
    QString strFile = QFileDialog::getOpenFileName(
                this,
                tr("打开文件"),
                tr(""),
                tr("Text Files(*.ini);;All files(*)")
                );
    if( strFile.isEmpty()) {
        return;
    }
    qDebug()<<strFile;
    QSettings setting(strFile, QSettings::IniFormat);
    //读取ini文件内容
    setting.setIniCodec(QTextCodec::codecForName("UTF-8"));
    float angle_offset = setting.value("P2/OFFSET",1).toFloat();
    ui->tableWidget->item(1,kAngleIndex)->setText(setting.value("P2/3M1",1).toString());
    ui->tableWidget->item(2,kAngleIndex)->setText(setting.value("P2/3M2",2).toString());
    ui->tableWidget->item(3,kAngleIndex)->setText(setting.value("P2/3M3",3).toString());
    ui->tableWidget->item(4,kAngleIndex)->setText(setting.value("P2/3M4",4).toString());
    ui->tableWidget->item(5,kAngleIndex)->setText(setting.value("P2/3M5",5).toString());
    ui->tableWidget->item(6,kAngleIndex)->setText(setting.value("P2/3M6",6).toString());
    ui->tableWidget->item(7,kAngleIndex)->setText(setting.value("P2/3M7",7).toString());
    ui->tableWidget->item(8,kAngleIndex)->setText(setting.value("P2/3M8",8).toString());
    ui->tableWidget->item(9,kAngleIndex)->setText(setting.value("P2/3M9",9).toString());
    ui->tableWidget->item(1,kPeakIndex)->setText(setting.value("P2/W1",1).toString());
    ui->tableWidget->item(2,kPeakIndex)->setText(setting.value("P2/W2",2).toString());
    ui->tableWidget->item(3,kPeakIndex)->setText(setting.value("P2/W3",3).toString());
    ui->tableWidget->item(4,kPeakIndex)->setText(setting.value("P2/W4",4).toString());
    ui->tableWidget->item(5,kPeakIndex)->setText(setting.value("P2/W5",5).toString());
    ui->tableWidget->item(6,kPeakIndex)->setText(setting.value("P2/W6",6).toString());
    ui->tableWidget->item(7,kPeakIndex)->setText(setting.value("P2/W7",7).toString());
    ui->tableWidget->item(8,kPeakIndex)->setText(setting.value("P2/W8",8).toString());
    ui->tableWidget->item(9,kPeakIndex)->setText(setting.value("P2/W9",9).toString());
    ui->tableWidget->item(1,kSampleDisIndex)->setText(setting.value("P2/B1",1).toString());
    ui->tableWidget->item(2,kSampleDisIndex)->setText(setting.value("P2/B2",2).toString());
    ui->tableWidget->item(3,kSampleDisIndex)->setText(setting.value("P2/B3",3).toString());
    ui->tableWidget->item(4,kSampleDisIndex)->setText(setting.value("P2/B4",4).toString());
    ui->tableWidget->item(5,kSampleDisIndex)->setText(setting.value("P2/B5",5).toString());
    ui->tableWidget->item(6,kSampleDisIndex)->setText(setting.value("P2/B6",6).toString());
    ui->tableWidget->item(7,kSampleDisIndex)->setText(setting.value("P2/B7",7).toString());
    ui->tableWidget->item(8,kSampleDisIndex)->setText(setting.value("P2/B8",8).toString());
    ui->tableWidget->item(9,kSampleDisIndex)->setText(setting.value("P2/B9",9).toString());

    ui->tableWidget->item(1,kRealDisIndex)->setText(setting.value("P2/R1",1).toString());
    ui->tableWidget->item(2,kRealDisIndex)->setText(setting.value("P2/R2",2).toString());
    ui->tableWidget->item(3,kRealDisIndex)->setText(setting.value("P2/R3",3).toString());
    ui->tableWidget->item(4,kRealDisIndex)->setText(setting.value("P2/R4",4).toString());
    ui->tableWidget->item(5,kRealDisIndex)->setText(setting.value("P2/R5",5).toString());
    ui->tableWidget->item(6,kRealDisIndex)->setText(setting.value("P2/R6",6).toString());
    ui->tableWidget->item(7,kRealDisIndex)->setText(setting.value("P2/R7",7).toString());
    ui->tableWidget->item(8,kRealDisIndex)->setText(setting.value("P2/R8",8).toString());
    ui->tableWidget->item(9,kRealDisIndex)->setText(setting.value("P2/R9",9).toString());
    ui->tableWidget->item(10,kRealDisIndex)->setText(setting.value("P2/OFFSET",1).toString());



/*
    QString strFile = QFileDialog::getOpenFileName(
                this,
                tr("打开文件"),
                tr(""),
                tr("Text Files(*.cspc);;All files(*)")
                );
    if( strFile.isEmpty()) {
        return;
    }
    QFile fileIn(strFile);
    if( ! fileIn.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, tr("打开文件"), tr("打开文件失败：") + fileIn.errorString());
        return;
    }

    QTextStream tsIn(&fileIn);

    float fParam = 0;
    uint paramCnt = 0;
    float offsetAngle = 0;

    while( ! tsIn.atEnd() )
    {
        if(paramCnt > kTofSampleLineCnt) {
            break;
        }
        tsIn>>fParam;
        if(paramCnt == 0) {
            offsetAngle = fParam;
            offsetAng = offsetAngle;
            paramCnt++;
            continue;
        }
        fParam = fParam + offsetAngle;
        if(fParam >= 360.0) {
            fParam -= 360.0;
        }
        else if(fParam < 0.0) {
            fParam += 360.0;
        }
        ui->tableWidget->item(paramCnt,kAngleIndex)->setText(QString::number(fParam,'f',2));
        tsIn>>fParam;
        ui->tableWidget->item(paramCnt,kRealDisIndex)->setText(QString::number(fParam,'f',2));
        tsIn>>fParam;
        ui->tableWidget->item(paramCnt,kPeakIndex)->setText(QString::number(fParam,'f',2));
        tsIn>>fParam;
        ui->tableWidget->item(paramCnt,kSampleDisIndex)->setText(QString::number(fParam,'f',3));
        paramCnt++;

    }
   fileIn.close();
*/
}

bool DynamicCalibration::LeastSquareMethodLinearFit(std::vector<float> x,std::vector<float> y,std::vector<float> &param)
{
    int xSize = x.size();
    int ySize = y.size();
    if(xSize != ySize) {
         QMessageBox::warning(NULL, "warning", "x y data size not the same");
         return false;
    }

    Eigen::MatrixXf MatriA(xSize,2);//kx + b  = y
    for(int m=0; m<xSize; m++) {
        MatriA(m,0) =   x.at(m);
        MatriA(m,1) =   1;
    }

    Eigen::VectorXf VectorB(ySize,1);//,VectorParam(ySize,1);
    for(int n=0; n<xSize; n++) {
        VectorB(n,0) =   y.at(n);
    }

//    float determ = MatriA.determinant();
//    if(fabs(determ) < 0.001f ) {
//        qDebug()<< "determinant" << determ;
//        QMessageBox::warning(NULL, "warning", "centroid-distance determinant is 0!");
//    }

    Eigen::Vector2f  VectorParam = MatriA.jacobiSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(VectorB);
    //VectorParam = MatriA.colPivHouseholderQr().solve(VectorB);
    std::cout<< "***least square linear fit***"<<std::endl;
    for(int n=0; n<2; n++) {
        param.push_back(VectorParam(n,0));
        std::cout<< VectorParam(n,0) <<std::endl;
    }
    return true;
}

bool DynamicCalibration::LinearFit(std::vector<float> x,std::vector<float> y,std::vector<float> &param)
{
    int xSize = x.size();
    int ySize = y.size();
    if(xSize != ySize) {
         QMessageBox::warning(NULL, "warning", "x y data size not the same");
         return false;
    }

    Eigen::MatrixXf MatriA(xSize,2);//kx + b  = y
    for(int m=0; m<xSize; m++) {
        MatriA(m,0) =   x.at(m);
        MatriA(m,1) =   1;
    }

    Eigen::VectorXf VectorB(ySize,1);//,VectorParam(ySize,1);
    for(int n=0; n<xSize; n++) {
        VectorB(n,0) =   y.at(n);
    }

    float determ = MatriA.determinant();
    if(fabs(determ) < 0.001f ) {
        qDebug()<< "determinant" << determ;
        QMessageBox::warning(NULL, "warning", "centroid-distance determinant is 0!");
    }


    Eigen::Vector2f VectorParam = MatriA.inverse()*VectorB;
    std::cout<< "***linear fit***"<<std::endl;
    for(int n=0; n<2; n++) {
        param.push_back(VectorParam(n,0));
        std::cout<< VectorParam(n,0) <<std::endl;
    }
    return true;
}


bool DynamicCalibration::Polynomial(std::vector<float> x,std::vector<float> y,std::vector<float> &param)
{
    int xSize = x.size();
    int ySize = y.size();
    if(xSize != ySize) {
         QMessageBox::warning(NULL, "warning", "x y data size not the same");
         return false;
    }

    Eigen::MatrixXf MatriA(xSize,3);//x^2 x 1    kx + b  = y
    for(int m=0; m<xSize; m++) {
        MatriA(m,0) =   x.at(m)*x.at(m);
        MatriA(m,1) =   x.at(m);
        MatriA(m,2) =   1;
    }

    Eigen::VectorXf VectorB(ySize,1);//,VectorParam(ySize,1);
    for(int n=0; n<xSize; n++) {
        VectorB(n,0) =   y.at(n);
    }

    float determ = MatriA.determinant();
    if(fabs(determ) < 0.001f ) {
        qDebug()<< "determinant" << determ;
        QMessageBox::warning(NULL, "warning", "centroid-distance determinant is 0!");
    }
    Eigen::Vector3f VectorParam = MatriA.inverse()*VectorB;
    std::cout<< "***polynomial fit***"<<std::endl;
    for(int n=0; n<3; n++) {
        param.push_back(VectorParam(n,0));
        std::cout<< VectorParam(n,0) <<std::endl;
    }
    return true;
}

bool DynamicCalibration::CalcLinearMarkPoint(std::vector<float> param1,std::vector<float> param2,std::vector<float> &coordinate)
{
    if(param1.at(0) == param2.at(0)) {
        QMessageBox::warning(NULL, "warning", "two line k is the same");
        return false;
    }
    double detaY = param2.at(1) - param1.at(1);
    double detaX = param1.at(0) - param2.at(0);
    float x = (detaY)/(detaX);
    float y = param1.at(0)*x + param1.at(1);
    std::cout<< "***markPoint***"<<std::endl;
    std::cout<< x <<std::endl;
    std::cout<< y <<std::endl;
    coordinate.push_back(x);//(b2 - b1)/(k1 -k2)
    coordinate.push_back(y);//y = kx + b
    return true;
}

bool DynamicCalibration::AllLinearFitAndMarkpoint(std::vector<float> dataX,std::vector<float> dataY)
{
#if 1
    if(dataX.size() != dataY.size() || dataX.size() != 17 ||dataY.size() != 17) {
        return false;
    }
    tofLinearFitParam.clear();
    tofLinearFitMark.clear();
    std::vector<float> x,y,par1,par2,mark;
    int m =0;

    /*100 200*/
    x.resize(2);
    y.resize(2);
    for(m=0; m<2; m++) {
        x.at(m) = dataX.at(m);
        y.at(m) = dataY.at(m);
    }
    par1.clear();
    if(!LinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "1st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);

    mark.clear();
    mark.push_back(dataX.at(0));
    mark.push_back(dataY.at(0));
    tofLinearFitMark.push_back(mark);//100

    /*200 300*/

    x.resize(3);
    y.resize(3);
    for(m=0; m<3; m++) {
        x.at(m) = dataX.at(m);
        y.at(m) = dataY.at(m);
    }
    par2.clear();
    if(!Polynomial(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "2st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    //CalcLinearMarkPoint(par1,par2,mark);
    mark.push_back(dataX.at(2));
    mark.push_back(dataY.at(2));
    tofLinearFitMark.push_back(mark);//200


    /*
    x.resize(2);
    y.resize(2);
    for(m=1; m<3; m++) {
        x.at(m-1) = dataX.at(m);
        y.at(m-1) = dataY.at(m);
    }
    par2.clear();
    if(!LinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "2st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//200
    */

    /*300 700*/
    x.resize(2);
    y.resize(2);
    //for(int m=2; m<6; m++) {
        x.at(0) = dataX.at(2);
        y.at(0) = dataY.at(2);
        x.at(1) = dataX.at(6);
        y.at(1) = dataY.at(6);
    //}
    par1.clear();
    if(!LinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "3st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    //CalcLinearMarkPoint(par2,par1,mark);
    mark.push_back(dataX.at(6));
    mark.push_back(dataY.at(6));
    tofLinearFitMark.push_back(mark);//300

    /*700 1000*/
    x.resize(2);
    y.resize(2);
    //for(int m=5; m<10; m++) {
        x.at(0) = dataX.at(6);
        y.at(0) = dataY.at(6);
        x.at(1) = dataX.at(9);
        y.at(1) = dataY.at(9);
    //}
    par2.clear();
    if(!LinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "4st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//600

    /*1000 1400*/
    x.resize(2);
    y.resize(2);
    //for(int m=9; m<12; m++) {
        x.at(0) = dataX.at(9);
        y.at(0) = dataY.at(9);
        x.at(1) = dataX.at(11);
        y.at(1) = dataY.at(11);
    //}
    par1.clear();
    if(!LinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "5st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    CalcLinearMarkPoint(par2,par1,mark);
    tofLinearFitMark.push_back(mark);//1000

    /*1400 2000*/
    x.resize(2);
    y.resize(2);
    //for(int m=11; m<14; m++) {
        x.at(0) = dataX.at(11);
        y.at(0) = dataY.at(11);
        x.at(1) = dataX.at(13);
        y.at(1) = dataY.at(13);
    //}
    par2.clear();
    if(!LinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "6st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//1400

    /*2000 6000*/
    x.resize(2);
    y.resize(2);
    //for(int m=13; m<16; m++) {
        x.at(0) = dataX.at(13);
        y.at(0) = dataY.at(13);
        x.at(1) = dataX.at(15);
        y.at(1) = dataY.at(15);
    //}
    par1.clear();
    if(!LinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "7st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    CalcLinearMarkPoint(par2,par1,mark);
    tofLinearFitMark.push_back(mark);//2000

    ui->textEditDisLog->append("***generate linear parameters***");
    for(uint m=0; m<tofLinearFitParam.size(); m++) {
        ui->textEditDisLog->append(QString::number(tofLinearFitParam.at(m).at(0))+" "\
                                  +QString::number(tofLinearFitParam.at(m).at(1)));

    }


    QVector<QCPGraphData> data(7),dataP(16);
    for(uint n=0; n<7; n++) {
        data[n].key = tofLinearFitMark.at(n).at(0);
        data[n].value = tofLinearFitMark.at(n).at(1);
    }
    for(uint m=0; m<kTofSampleLineCnt-1; m++) {//add first point
        dataP[m].key = dataX.at(m);
        dataP[m].value = dataY.at(m);
    }
    subGraphRando->data()->set(dataP);
    subGraphRandoPoint->data()->set(data);

    ui->widget->replot(QCustomPlot::rpQueuedReplot);
    return true;
#else
    if(dataX.size() != dataY.size() || dataX.size() != 17 ||dataY.size() != 17) {
        return false;
    }
    tofLinearFitParam.clear();
    tofLinearFitMark.clear();
    std::vector<float> x,y,par1,par2,mark;
    int m =0;

    /*100 200*/
    x.resize(2);
    y.resize(2);
    for(m=0; m<2; m++) {
        x.at(m) = dataX.at(m);
        y.at(m) = dataY.at(m);
    }
    par1.clear();
    if(!LinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "1st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);

    mark.clear();
    mark.push_back(dataX.at(0));
    mark.push_back(dataY.at(0));
    tofLinearFitMark.push_back(mark);//100

    /*200 300*/
    x.resize(2);
    y.resize(2);
    for(m=1; m<3; m++) {
        x.at(m-1) = dataX.at(m);
        y.at(m-1) = dataY.at(m);
    }
    par2.clear();
    if(!LinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "2st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//200

    /*300 600*/
    x.resize(4);
    y.resize(4);
    for(int m=2; m<6; m++) {
        x.at(m-2) = dataX.at(m);
        y.at(m-2) = dataY.at(m);
    }
    par1.clear();
    if(!LeastSquareMethodLinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "3st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    CalcLinearMarkPoint(par2,par1,mark);
    tofLinearFitMark.push_back(mark);//300

    /*600 1000*/
    x.resize(5);
    y.resize(5);
    for(int m=5; m<10; m++) {
        x.at(m-5) = dataX.at(m);
        y.at(m-5) = dataY.at(m);
    }
    par2.clear();
    if(!LeastSquareMethodLinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "4st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//600

    /*1000 1400*/
    x.resize(3);
    y.resize(3);
    for(int m=9; m<12; m++) {
        x.at(m-9) = dataX.at(m);
        y.at(m-9) = dataY.at(m);
    }
    par1.clear();
    if(!LeastSquareMethodLinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "5st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    CalcLinearMarkPoint(par2,par1,mark);
    tofLinearFitMark.push_back(mark);//1000

    /*1400 2000*/
    x.resize(3);
    y.resize(3);
    for(int m=11; m<14; m++) {
        x.at(m-11) = dataX.at(m);
        y.at(m-11) = dataY.at(m);
    }
    par2.clear();
    if(!LeastSquareMethodLinearFit(x,y,par2)) {
        QMessageBox::warning(NULL, "warning", "6st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par2);
    mark.clear();
    CalcLinearMarkPoint(par1,par2,mark);
    tofLinearFitMark.push_back(mark);//1400

    /*2000 6000*/
    x.resize(3);
    y.resize(3);
    for(int m=13; m<16; m++) {
        x.at(m-13) = dataX.at(m);
        y.at(m-13) = dataY.at(m);
    }
    par1.clear();
    if(!LeastSquareMethodLinearFit(x,y,par1)) {
        QMessageBox::warning(NULL, "warning", "7st fit err");
        return false;
    }
    tofLinearFitParam.push_back(par1);
    mark.clear();
    CalcLinearMarkPoint(par2,par1,mark);
    tofLinearFitMark.push_back(mark);//2000


    QVector<QCPGraphData> data(7),dataP(16);
    for(uint n=0; n<7; n++) {
        data[n].key = tofLinearFitMark.at(n).at(0);
        data[n].value = tofLinearFitMark.at(n).at(1);
    }
    for(uint m=0; m<kTofSampleLineCnt-1; m++) {//add first point
        dataP[m].key = dataX.at(m);
        dataP[m].value = dataY.at(m);
    }
    subGraphRando->data()->set(dataP);
    subGraphRandoPoint->data()->set(data);

#endif

    ui->widget->replot(QCustomPlot::rpQueuedReplot);
    return true;
}



void DynamicCalibration::on_actionExptParam_triggered()
{
   //获取保存的文件名
   QString strFileSave = QFileDialog::getSaveFileName(
               this,
               tr("保存文件"),
               tr("fitParam"),
               //tr("XLS files(*.xls);;param Files(*.cspc)")
               tr("Text Files(*.cspc)")
               );
   if(strFileSave.isEmpty())
   {
       //文件名是空的
       return;
   }
   //打开输出文件
   QFile fileOut(strFileSave);
   if( ! fileOut.open(QIODevice::WriteOnly))
   {
       QMessageBox::warning(this, tr("保存文件"),
                            tr("打开保存文件失败：") + fileOut.errorString());
       return;
   }
   //定义输出文本流
   QTextStream tsOut( & fileOut);
   //先打印一行表头
   tsOut<< offsetAng <<endl;//
   for(uint m=1; m<kTofSampleLineCnt+1; m++) {
       tsOut<< ui->tableWidget->item(m,kAngleIndex)->text()<<" "<< \
               ui->tableWidget->item(m,kRealDisIndex)->text()<<" "<< \
               ui->tableWidget->item(m,kPeakIndex)->text()<<" "<< \
               ui->tableWidget->item(m,kSampleDisIndex)->text()<<" "<<endl;

   }
   fileOut.close();
   QMessageBox::warning(this, tr("save param"),
                        tr("save finished!"));

}


bool DynamicCalibration::nterpolationLookupTable(std::vector<float> x,std::vector<float> y,std::vector<float> table)
{
    uint32_t size = x.size();
    //找到最接近0.05倍数的索引值
    float startIndex = ((uint32_t)(x.at(0)/0.05) + 1)*0.05;
    float endIndex = startIndex + 250/15.6;
    std::vector<float> tempX,tempY,par;


    for(uint32_t m=0; m<size-4; m=m+4) {

        //取点拟合
        float sP = x.at(m);
        tempX.push_back(x.at(m));
        tempY.push_back(y.at(m));

        while(1) {
            if(m >= size-1) {
                return false;
            }
            if(fabs(sP - x.at(m+3)) < 0.00001) {
                m++;
            }
            else {
                break;
            }
        }

        tempX.push_back(x.at(m+3));
        tempY.push_back(y.at(m+3));
        par.clear();
        if(!LinearFit(tempX,tempY,par)) {
            QMessageBox::warning(NULL, "warning", "3st fit err");
            return false;
        }


        //0.05分辨率获取函数值
        float value= 0;
        while(1) {

            value = par.at(0)*startIndex +  par.at(1);
            table.push_back(value);
            startIndex += 0.05;
            if(startIndex > tempX.at(1)) {
                break;
            }
        }
        tempX.clear();
        tempY.clear();
        //填充数据 + 起始与结束拐点
        if(startIndex > endIndex) {
            break;
        }
    }
    return 1;
}


void DynamicCalibration::on_actioncompensationAngle_triggered()
{
    std::vector<float> samplePoint;

    samplePoint.push_back(ui->tableWidget->item(1,kAngleIndex)->text().toFloat());
    samplePoint.push_back(ui->tableWidget->item(2,kAngleIndex)->text().toFloat());
    samplePoint.push_back(ui->tableWidget->item(3,kAngleIndex)->text().toFloat());
    samplePoint.push_back(ui->tableWidget->item(4,kAngleIndex)->text().toFloat());
    emit TransmitCompensation(true,samplePoint);
}



void DynamicCalibration::on_actionSamplePoint_triggered()
{
    std::vector<float> hrPoint,wtPoint,bkPoint;
    std::vector<int> dis;
    angle_offset = ui->tableWidget->item(10,kRealDisIndex)->text().toFloat();
    if(ui->tableWidget->item(1,kAngleIndex)->text() != ""
       && ui->tableWidget->item(2,kAngleIndex)->text() != "") {
        for(uint m=0; m<9; m++) {
           hrPoint.push_back(ui->tableWidget->item(m+1,kAngleIndex)->text().toFloat()-angle_offset);
           wtPoint.push_back(ui->tableWidget->item(m+1,kPeakIndex)->text().toFloat()-angle_offset);
           bkPoint.push_back(ui->tableWidget->item(m+1,kSampleDisIndex)->text().toFloat()-angle_offset);
           dis.push_back(ui->tableWidget->item(m+1,kRealDisIndex)->text().toFloat());
           qDebug() <<hrPoint[m]<<" "<<wtPoint[m]<<" "<<bkPoint[m]<<" "<<dis[m];

        }
    }
    else {
        QMessageBox::warning(NULL, "warning", "请输入相应的角度值");
    }
    emit TransmitSamplePoint(hrPoint,wtPoint,bkPoint,dis,1);
}

void DynamicCalibration::on_actionCompareData_triggered()
{
    std::vector<float> hrPoint,wtPoint,bkPoint;
    std::vector<int> dis;
    angle_offset = ui->tableWidget->item(10,kRealDisIndex)->text().toFloat();
    if(ui->tableWidget->item(1,kAngleIndex)->text() != ""
       && ui->tableWidget->item(2,kAngleIndex)->text() != "") {
        for(uint m=0; m<9; m++) {
           hrPoint.push_back(ui->tableWidget->item(m+1,kAngleIndex)->text().toFloat()-angle_offset);
           wtPoint.push_back(ui->tableWidget->item(m+1,kPeakIndex)->text().toFloat()-angle_offset);
           bkPoint.push_back(ui->tableWidget->item(m+1,kSampleDisIndex)->text().toFloat()-angle_offset);
           dis.push_back(ui->tableWidget->item(m+1,kRealDisIndex)->text().toFloat());
           qDebug() <<hrPoint[m]<<" "<<wtPoint[m]<<" "<<bkPoint[m]<<" "<<dis[m];         
        }
    }
    else {
        QMessageBox::warning(NULL, "warning", "请输入相应的角度值");
    }
    emit TransmitSamplePoint(hrPoint,wtPoint,bkPoint,dis,2);
}
