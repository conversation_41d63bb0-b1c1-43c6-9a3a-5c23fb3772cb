<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImportPointCloud</class>
 <widget class="QWidget" name="ImportPointCloud">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>462</width>
    <height>199</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>importPointCloud</string>
  </property>
  <property name="windowIcon">
   <iconset resource="logo2.qrc">
    <normaloff>:/new/prefix1/icon/logo/download2.png</normaloff>:/new/prefix1/icon/logo/download2.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout" stretch="1,2,1,1">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QPushButton" name="pushButtonImportPointCloud">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="autoFillBackground">
          <bool>false</bool>
         </property>
         <property name="text">
          <string>import</string>
         </property>
         <property name="flat">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="radioButtonFileData">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="layoutDirection">
          <enum>Qt::LeftToRight</enum>
         </property>
         <property name="text">
          <string>fileData</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEditFilePath">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QPushButton" name="pushButtonStart">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Start</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonStop">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Stop</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,5,1">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>0hz</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSlider" name="horizontalSliderSetSpeed">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimum">
          <number>0</number>
         </property>
         <property name="maximum">
          <number>20</number>
         </property>
         <property name="pageStep">
          <number>0</number>
         </property>
         <property name="value">
          <number>10</number>
         </property>
         <property name="tracking">
          <bool>false</bool>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="tickInterval">
          <number>0</number>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>20hz</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QProgressBar" name="progressBar">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="value">
        <number>0</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="logo2.qrc"/>
 </resources>
 <connections/>
</ui>
