#include "histogram_chart.h"
#include "ui_histogramchart.h"
#include <QSettings>

const uint8_t convolutionParam[16]= {2,3,4,5,7,8,10,11,12,13,14,15,14,13,10,5};
//const uint8_t convolutionParam1[16]= {2,3,4,5,7,8,9,10,12,14,15,10,7,4,3,2};
const uint8_t convolutionParam1[16]= {2,3,4,7,10,15,14,12,10,9,8,7,5,4,3,2};
const uint8_t convolutionParam2[8]= {2,4,8,16,16,8,4,2};

HistogramChart::HistogramChart(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::histogramChart),
    convolutionType(0),
    is_start_save_data_(false)
{
    ui->setupUi(this);
    ui->widgetHistog->setInteractions(QCP::iRangeDrag|QCP::iRangeZoom| QCP::iSelectAxes
                                                  |QCP::iSelectLegend | QCP::iSelectPlottables);

    //keyAxis = ui->widgetHistog->xAxis;
    //valueAxis = ui->widgetHistog->yAxis;


    ui->widgetHistog->addGraph();
    ui->widgetHistog->graph(0)->setPen(QPen(QColor(50,50,50),1.2)); // line color blue for first graph
    ui->widgetHistog->graph(0)->setLineStyle(QCPGraph::lsLine);
    ui->widgetHistog->xAxis->setTicks(true);
    ui->widgetHistog->yAxis->setTicks(true);;
    ui->widgetHistog->xAxis->setUpperEnding(QCPLineEnding::esSpikeArrow);
    ui->widgetHistog->yAxis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    ui->widgetHistog->xAxis->setSubTicks(true);
    ui->widgetHistog->xAxis->setRange(0, 383);
    ui->widgetHistog->yAxis->setSubTicks(true);
    ui->widgetHistog->yAxis->setRange(0, 2000);
    ui->widgetHistog->yAxis->ticker()->setTickCount(8);


    ui->widgetHistog->addGraph();
    ui->widgetHistog->graph(1)->setPen(QPen(QColor(255,50,50),1.2)); // line color blue for first graph
    ui->widgetHistog->graph(1)->setLineStyle(QCPGraph::lsLine);
    //ui->widgetHistog->graph(1)->setScatterStyle(QCPScatterStyle::ssDot);


    ui->widgetHistog->axisRect()->setAutoMargins(QCP::msBottom );
    ui->widgetHistog->axisRect()->setMargins(QMargins(50,10,10,0));//分别为左 上 右 底 边距



    //ui->widgetHistog->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssDisc, 2));
    //ui->widgetHistog->setBackground(QBrush(QColor(44, 62, 80)));
    //柱状图
    //fossil = new QCPBars(keyAxis, valueAxis);  // 使用xAxis作为柱状图的key轴，yAxis作为value轴
    //fossil->setAntialiased(false); // 为了更好的边框效果，关闭抗齿锯
    //fossil->setName("TDC直方图"); // 设置柱状图的名字，可在图例中显示
    //fossil->setPen(QPen(QColor(255, 0, 0).lighter(130))); // 设置柱状图的边框颜色
    //fossil->setBrush(QColor(0, 168, 140));  // 设置柱状图的画刷颜色

    //为柱状图设置一个文字类型的key轴，ticks决定了轴的范围，而labels决定了轴的刻度文字的显示
//    QVector<double> tickValue;
//    QVector<double> tick;
//    for(uint16_t m=0; m<384; m++) {
//        tick.append(m);
//        tickValue.append(0);
//    }

//    //keyAxis->setLabel("TDC");        // 设置为文字轴
//    keyAxis->setTickLabelRotation(0);     // 轴刻度文字旋转60度
//    keyAxis->setSubTicks(true);           // 不显示子刻度
//   // keyAxis->setTickLength(0,383);          // 轴内外刻度的长度分别是0,4,也就是轴内的刻度线不显示
//    keyAxis->setRange(0, 383);               // 设置范围
//    keyAxis->setUpperEnding(QCPLineEnding::esSpikeArrow);//显示末尾箭头

//    valueAxis->setRange(0, 393);
//    //valueAxis->setLabel("count");
//    valueAxis->setUpperEnding(QCPLineEnding::esSpikeArrow);


//    fossil->setData(tick, tickValue);


    //文本标签
    wavePacketText = new QCPItemText(ui->widgetHistog);
    wavePacketText->setPositionAlignment(Qt::AlignTop|Qt::AlignRight);//文字布局：顶、左对齐
    wavePacketText->position->setType(QCPItemPosition::ptAxisRectRatio);//位置类型（当前轴范围的比例为单位/实际坐标为单位）
    wavePacketText->position->setCoords(0.95, 0.01); //把文字框放在X轴的中间，Y轴的最顶部
    wavePacketText->setFont(QFont("仿宋",10,QFont::Bold)); //字体大小
    wavePacketText->setColor(QColor(0,0,0));
    wavePacketText->setPadding(QMargins(2,2,2,2));//文字距离边框几个像素
    wavePacketText->setText("tof:   peak: ");

    ui->widgetHistog->show();


    //创建QSettings对象并指定ini文件路径并将格式设置为ini
    QSettings setting("settings.ini", QSettings::IniFormat);
    //读取ini文件内容
    QString  q_enable = setting.value("ENABLE/convolution_enable").toString();
    bool ok = false;
    uint8_t enable = q_enable.toInt(&ok,16);
    St_convolution_settings_.kernel_enable = enable==1 ? true : false;


    QString  q_raw_enable = setting.value("ENABLE/convolution_raw_enable").toString();
    ok = false;
    uint8_t raw_enable = q_raw_enable.toInt(&ok,16);
    St_convolution_settings_.raw_enable = raw_enable==1 ? true : false;



    QString  q_derivative = setting.value("ENABLE/convolution_derivative_enable").toString();
    ok = false;
    uint8_t derivatice_enable = q_derivative.toInt(&ok,16);
    St_convolution_settings_.derivative_enable = derivatice_enable==1 ? true : false;




    QString  q_size = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_size").toString();
    ok = false;
    uint8_t param_size = q_size.toInt(&ok,16);
    St_convolution_settings_.parameters_size = param_size;

    QString str,param;
    for(uint n=0; n<St_convolution_settings_.parameters_size; n++) {
        str = "CONVOLUTION_PARAMETERS/convolution_parameters_"+QString::number(n,10);
        param = setting.value(str).toString();
        ok = false;
        uint8_t param_tmp = param.toInt(&ok,16);
        St_convolution_settings_.parameters[n] = param_tmp;
    }
    /*
    QString  q_param0 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_0").toString();
    ok = false;
    uint8_t param0 = q_param0.toInt(&ok,16);
    St_convolution_settings_.parameters[0] = param0;

    QString  q_param1 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_1").toString();
    ok = false;
    uint8_t param1 = q_param1.toInt(&ok,16);
    St_convolution_settings_.parameters[1] = param1;

    QString  q_param2 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_2").toString();
    ok = false;
    uint8_t param2 = q_param2.toInt(&ok,16);
    St_convolution_settings_.parameters[2] = param2;

    QString  q_param3 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_3").toString();
    ok = false;
    uint8_t param3 = q_param3.toInt(&ok,16);
    St_convolution_settings_.parameters[3] = param3;

    QString  q_param4 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_4").toString();
    ok = false;
    uint8_t param4 = q_param4.toInt(&ok,16);
    St_convolution_settings_.parameters[4] = param4;

    QString  q_param5 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_5").toString();
    ok = false;
    uint8_t param5 = q_param5.toInt(&ok,16);
    St_convolution_settings_.parameters[5] = param5;

    QString  q_param6 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_6").toString();
    ok = false;
    uint8_t param6 = q_param6.toInt(&ok,16);
    St_convolution_settings_.parameters[6] = param6;

    QString  q_param7 = setting.value("CONVOLUTION_PARAMETERS/convolution_parameters_7").toString();
    ok = false;
    uint8_t param7 = q_param7.toInt(&ok,16);
    St_convolution_settings_.parameters[7] = param7;
    */

    QString  q_threshold = setting.value("THRESHOLD_DUTY/convolution_threshold").toString();
    ok = false;
    uint8_t threshold = q_threshold.toInt(&ok,16);
    St_convolution_settings_.threshold_duty = (float)threshold/100.0;

    QString  q_output= setting.value("OUTPUT_ITEMS/convolution_output").toString();
    ok = false;
    uint8_t output = q_output.toInt(&ok,16);
    St_convolution_settings_.output_enable = output==1 ? true : false;


    QString  q_centroid = setting.value("OUTPUT_ITEMS/convolution_centroid_enable").toString();
    ok = false;
    uint8_t centroid = q_centroid.toInt(&ok,16);
    St_convolution_settings_.output_centroid_enable = centroid==1 ? true : false;

    QString  q_intensity = setting.value("OUTPUT_ITEMS/convolution_intensity_enable").toString();
    ok = false;
    uint8_t intensity = q_intensity.toInt(&ok,16);
    St_convolution_settings_.output_intensity_enable = intensity==1 ? true : false;

    QString  q_integration = setting.value("OUTPUT_ITEMS/convolution_pulse_integration_enable").toString();
    ok = false;
    uint8_t integration = q_integration.toInt(&ok,16);
    St_convolution_settings_.output_pulse_integration_enable = integration;

    QString  q_pulse_width = setting.value("OUTPUT_ITEMS/convolution_pulse_width_enable").toString();
    ok = false;
    uint8_t pulse_width = q_pulse_width.toInt(&ok,16);
    St_convolution_settings_.output_pulse_width_enable = pulse_width;


    if(St_convolution_settings_.output_enable == true) {

        QDateTime current_time = QDateTime::currentDateTime();
        QString data = current_time.toString("yyyy-MM-dd-hh-mm-ss-");

        convolution_file.setFileName(data+"convolution.csv");

        if (!convolution_file.open(QFile::WriteOnly | QFile::Text))		//检测文件是否打开
        {
            QMessageBox::information(this, "Error Message", "Please Select a csv File!");
            return;
        }
    }


}

HistogramChart::~HistogramChart()
{
    delete ui;
    delete fossil;
    delete keyAxis;
    delete valueAxis;
}

void HistogramChart::receiveHistType(uint8_t type)
{
   if(type == 1) {
       is_start_save_data_ = true;
   }
   else if(type == 2) {
       is_start_save_data_ = false;
   }
   else {
       convolution_file.close();
       is_start_save_data_ = false;
   }
   qDebug()<<"hist TransmitHistType: "<<type;
}

void HistogramChart::receiveHistogramData(QByteArray rec)
{
    uint8_t *buff = (uint8_t*)rec.data();
    uint16_t size = rec.size();
    memcpy(histogramBuff,buff,size);

    uint16_t max = 0, max1 = 0, index = 0,index1 = 0;
    QVector<double> ticks;
    size = size/2;
    QVector<double> x(size),y(size);
    QVector<double> x1(size),y1(size);
    QVector<double> x2(size),y2(size);

    for(uint16_t n=0; n<size; n++) {
        y[n] = histogramBuff[n];
        if(y[n] > max) {
            max = y[n];
            index = n;
        }
        x[n] = n;

        if(is_start_save_data_ == true) {
            QTextStream out(&convolution_file);					//分行写入文件

            out << y[n] << ",";
        }

    }

    if(is_start_save_data_ == true) {
        QTextStream out(&convolution_file);
        out << endl;
    }
    max1 = max;
    index1 = index;

    uint32_t  noise_base = 0;
    ui->widgetHistog->graph(0)->setData(x,y);
    /**************************/
    float  before_peak_sum = 0;

    int before_index = 0;
    int before_max = -1e5;
    int before_max_pri = -1e5;
    int before_max_pri1 = -1e5;
    int before_max_pri2 = -1e5;
    int before_max_pri3 = -1e5;
    int before_max_pri4 = -1e5;

    for(uint16_t n=0; n<index-10; n++) {
        //before_peak_sum += histogramBuff[n];
        //before_peak_sum /= (index-2-150);

        if(histogramBuff[n] >= before_max) {
            before_index = n;
            before_max_pri4 = before_max_pri3;
            before_max_pri3 = before_max_pri2;
            before_max_pri2 = before_max_pri1;
            before_max_pri1 = before_max_pri;
            before_max_pri = before_max;
            before_max = histogramBuff[n];
        }
    }

    uint before_convolution = 0;
    uint before_sum = 0;
    for(int m=before_index-8; m<before_index+8; m++) {
        before_convolution += histogramBuff[m]*St_convolution_settings_.parameters[m-(before_index-8)];
    }
    before_sum = 184*(before_max_pri4+before_max_pri3+before_max_pri2+before_max_pri1+before_max)/5;




    /**************************/


    /*int32_t  convolution_derivative_max_value = 0;
    int32_t  convolution_derivative_min_value = 0;
    uint16_t convolution_derivative_max_index = 0;
    uint16_t convolution_derivative_min_index = 0;
    int32_t  convolution_derivative_min_index_last = 0;
    int32_t  convolution_derivative_tmp_value = 0;

    bool		 is_derivative_positive = false;
    bool		 is_derivative_negative = false;
    uint16_t convolution_der_pos_cnt = 0;
    uint16_t convolution_der_neg_cnt = 0;
    int con_derivative_coeff_pos = 14;
    int con_derivative_coeff_neg = -10;
    double y1_maxxx = 0;
    double y1_minnn = 1000;*/


    float convolution_max_rate = 0;
    if(St_convolution_settings_.kernel_enable == true) {
        max = 0;

        for(uint16_t n=20; n<size-St_convolution_settings_.parameters_size; n++) {

            double convolutionSum=0;
            if(n< size-St_convolution_settings_.parameters_size) {
                for(uint m=0; m<St_convolution_settings_.parameters_size; m++) {
                    convolutionSum += histogramBuff[n+m]*St_convolution_settings_.parameters[m];
                }

            }
            else {
                convolutionSum = noise_base;
            }

            if(convolutionSum > max) {
                max = convolutionSum;
                index = n;
            }

            y1[n] = convolutionSum;

            x1[n] = n;
            if(n == 20) {
                noise_base = convolutionSum;
            }
        }
        for(uint16_t n=0; n<20; n++) {
            y1[n] = noise_base;
            x1[n] = n;
        }
        for(uint16_t n=size-St_convolution_settings_.parameters_size; n<size; n++) {
            y1[n] = noise_base;
            x1[n] = n;

        }

        convolution_max_rate = 0.95f*max/(max1 == 0 ? 1 : max1);
        for(uint n=0; n<size; n++) {
            x2[n] = n;
            y2[n] = y1[n]/(fabs(convolution_max_rate) < 0.0001 ? 1 : convolution_max_rate);
            //qDebug() << y2[n];
        }

    }

    ui->widgetHistog->graph(1)->setData(x2,y2);


    wavePacketText->setText("tof: " + QString::number(index1,10) \
                            + "  peak: " + QString::number(max1,10)\
                            + "  tof: " + QString::number(index,10) \
                            + "  peak: " + QString::number(max,10)\
                            + "  sum: " + QString::number(before_convolution,10)\
                           );//
   // qDebug()<<before_max<<" "<<max1 <<" "<< max <<" "<<before_index<<" "<<before_sum<<" "<<(max+before_convolution)<<" "<<(before_sum+max);
    ui->widgetHistog->yAxis->setRange(-2, max1*1.2);
    ui->widgetHistog->replot(QCustomPlot::rpQueuedReplot);//刷新图表

    return;
#if 0
    else {


        if((histogramBuff[600] > 400 && histogramBuff[1100] > 400)) {
            con_derivative_coeff_pos = 11;
            con_derivative_coeff_neg = -9;
        }
        else if(histogramBuff[20] > 300 ) {
            con_derivative_coeff_pos = 13;
            con_derivative_coeff_neg = -10;
        }
        else if(histogramBuff[20] > 200 || (histogramBuff[20] < 180 && histogramBuff[size-10] > 200)) {
            con_derivative_coeff_pos = 15;
            con_derivative_coeff_neg = -10;
        }




        for(int i=100; i<size-1; i=i+2) {
            convolution_derivative_tmp_value = histogramBuff[i] - histogramBuff[i-1];

            if(convolution_derivative_tmp_value > convolution_derivative_max_value) {
                if(convolution_derivative_tmp_value > con_derivative_coeff_pos && convolution_der_pos_cnt == convolution_der_neg_cnt) {         
                    qDebug()<<"max: "<<i<<" "<<convolution_derivative_tmp_value <<" "<<convolution_derivative_max_value;
                    convolution_der_pos_cnt++;
                }
                convolution_derivative_max_value = convolution_derivative_tmp_value;
                convolution_derivative_max_index = i-4;
            }

            if(convolution_derivative_tmp_value < convolution_derivative_min_value) {
                if(convolution_derivative_tmp_value < con_derivative_coeff_neg && i > convolution_derivative_min_index+5
                        && convolution_der_pos_cnt > convolution_der_neg_cnt) { //&& convolution_der_pos_cnt > convolution_der_neg_cnt) {
                    qDebug()<<"min: "<<i<<" "<<convolution_derivative_tmp_value <<" "<<convolution_derivative_min_value;
                    convolution_der_neg_cnt++;
                    convolution_derivative_min_value = convolution_derivative_tmp_value;
                    convolution_derivative_min_index_last = convolution_derivative_min_index;
                    convolution_derivative_min_index = i+10;
                    con_derivative_coeff_neg = -18;
                }


            }

            if(histogramBuff[i] > max) {
                max = histogramBuff[i];
                index = i;
            }

        }
        if(convolution_der_neg_cnt == 2 && convolution_der_pos_cnt == 1) {
           convolution_derivative_min_index = convolution_derivative_min_index_last;
           index = (convolution_derivative_max_index+convolution_derivative_min_index)>>1;
           max = histogramBuff[index];
        }


            if(con_derivative_coeff_neg == -18) {
                convolution_derivative_min_index -= 10;
            }
            float		 convolution_der_width = 0;
            float		 convolution_der_middle = 0;

            convolution_der_middle = (convolution_derivative_min_index+convolution_derivative_max_index)>>1;
            convolution_der_width = 0.00004959f*convolution_der_middle*convolution_der_middle+0.1875f*convolution_der_middle-29.42f;


            if(convolution_der_neg_cnt == 2 && convolution_der_pos_cnt == 1) {
                convolution_derivative_min_index = convolution_derivative_min_index_last;
                //convolution_max_index = convolution_der_middle;//(convolution_derivative_max_index+convolution_derivative_min_index)>>1;
              //convolution_max = *(Pointer_buf+convolution_max_index);
             }




            if(convolution_derivative_max_index < index && index < convolution_derivative_min_index
                && convolution_derivative_max_index > 100 && convolution_derivative_min_index < 1320 \
                && max > histogramBuff[convolution_derivative_max_index] + 2 \
              && max > histogramBuff[convolution_derivative_min_index] + 2 \
                && (convolution_derivative_min_index-convolution_derivative_max_index) < 1.5f*convolution_der_width \
                && convolution_der_pos_cnt <= 2 && convolution_der_neg_cnt <= 2) {
                qDebug()<<"ok";
            }







        y1[0] = histogramBuff[1];
        x1[0] = 0;

        if(St_convolution_settings_.derivative_enable == true) {
            for(int i=10; i<size-2; i=i+2) {
                y1[i-1] = histogramBuff[i] - histogramBuff[i-1];
                x1[i-1] = i;
                if(y1[i-1] > y1_maxxx) {
                    y1_maxxx = y1[i-1];
                }

                if(y1[i-1] < y1_minnn) {
                    y1_minnn = y1[i-1];
                }
            }
        }
        else {
            for(int i=0; i<size-3; i++) {
                y1[i] = histogramBuff[i]+histogramBuff[i+1]+histogramBuff[i+2]+histogramBuff[i+3];
                x1[i] = i;
                if(y1[i] > y1_maxxx) {
                    y1_maxxx = y1[i];
                }

                if(y1[i] < y1_minnn) {
                    y1_minnn = y1[i];
                }
            }
            max1 = y1_maxxx;
            y1_minnn = 100;
        }






        if(histogramBuff[convolution_derivative_min_index + 10] > histogramBuff[convolution_derivative_min_index]) {
            convolution_derivative_min_index += 10;
            if(histogramBuff[convolution_derivative_min_index + 20] > histogramBuff[convolution_derivative_min_index]) {
                convolution_derivative_min_index += 20;
                if(histogramBuff[convolution_derivative_min_index + 30] > histogramBuff[convolution_derivative_min_index]) {
                    convolution_derivative_min_index += 30;
                }
            }

        }


        uint base_noise_value = histogramBuff[convolution_derivative_max_index]<histogramBuff[convolution_derivative_min_index]
                               ? histogramBuff[convolution_derivative_max_index]:histogramBuff[convolution_derivative_min_index];



        uint base_threshold = (max + base_noise_value)/2;

        uint32_t integration_peak_pixel = 0;
        uint32_t integration_peak = 0;
        uint16_t max_index = 0;
        uint16_t min_index = 0;




        for(uint16_t i=index; i>=convolution_derivative_max_index-8; i--) {

            if(histogramBuff[i] < base_threshold) {
                max_index = i;
                break;
            }

            integration_peak_pixel += histogramBuff[i]*i;
            integration_peak += histogramBuff[i];
        }
        for(uint16_t i=index+1; i<=convolution_derivative_min_index+8; i++) {

            if(histogramBuff[i] < base_threshold) {
                min_index = i;
                break;
            }

            integration_peak_pixel += histogramBuff[i]*i;
            integration_peak += histogramBuff[i];
        }

        float centroid_real = 0;
        if(integration_peak != 0) {
            centroid_real = (float)(integration_peak_pixel)/integration_peak;
        }


        /*****************/
        uint32_t integration_peak_pixel1 = 0;
        uint32_t integration_peak1 = 0;
        for(uint16_t i=index; i>=convolution_derivative_max_index-8; i--) {

            if(histogramBuff[i] < base_threshold) {
                max_index = i;
                break;
            }

            integration_peak_pixel1 += (histogramBuff[i]-base_threshold)*i;
            integration_peak1 += (histogramBuff[i]-base_threshold);
        }
        for(uint16_t i=index+1; i<=convolution_derivative_min_index+8; i++) {

            if(histogramBuff[i] < base_threshold) {
                min_index = i;
                break;
            }

            integration_peak_pixel1 += (histogramBuff[i]-base_threshold)*i;
            integration_peak1 += (histogramBuff[i]-base_threshold);
        }
        float centroid2 = 0;
        if(integration_peak != 0) {
            centroid2 = (float)(integration_peak_pixel1)/integration_peak1;
        }

        /*****************/
        qDebug()<<"centroid: "<<centroid_real<<" "<<integration_peak_pixel<<" "<<integration_peak
                <<" "<< centroid2<<" "<<integration_peak_pixel1<<" "<<integration_peak1;



        ui->widgetHistog->graph(1)->setData(x1,y1);
        ui->widgetHistog->yAxis->setRange(y1_minnn*1.1, max*1.1);
        qDebug()<<con_derivative_coeff_pos<<" "<<con_derivative_coeff_neg<<" "
               <<convolution_der_pos_cnt<<" "<<convolution_der_neg_cnt<<" "
               <<convolution_derivative_max_index<<" "<<convolution_derivative_min_index<<" "
               <<y1_maxxx<<" "
               <<y1_minnn<<" "
               <<centroid_real <<" "<<index<<" "<<max<<" "<<base_threshold
               <<max_index <<" "<<min_index;
    }


#endif

    //uint16_t lower = 0,upper = size-1;
    uint32_t halfPeak = (max-noise_base)*St_convolution_settings_.threshold_duty+noise_base;
    //qDebug() << halfPeak;
    uint64_t peakSum = 0,indexSum = 0;
    float centroidValue = 0;
    uint16_t pulse_width = 0;
    if(St_convolution_settings_.kernel_enable == true) {
        if(y1.size() == size) {

            for(uint16_t i=index; i>5; i--) {
                if(y1[i] >= halfPeak) {
                    peakSum += y1[i]*i;
                    indexSum += y1[i];
                    pulse_width++;
                }
                else {
                    if(y1[i-5] > halfPeak) {
                        peakSum += y1[i]*i;
                        indexSum += y1[i];
                    }
                    else {
                        break;
                    }


                }
            }

            for(uint16_t i=index; i<size-5; i++) {
                if(y1[i] >= halfPeak) {
                    peakSum += y1[i]*i;
                    indexSum += y1[i];
                    pulse_width++;
                }
                else {
                    if(y1[i+5] > halfPeak) {
                        peakSum += y1[i]*i;
                        indexSum += y1[i];
                    }

                    break;
                }
            }

            centroidValue = (float)peakSum/indexSum;
            St_convolution_settings_.intensity = max;
            St_convolution_settings_.intensity_integration = peakSum;
            St_convolution_settings_.pulse_width = pulse_width;
            St_convolution_settings_.centroid = centroidValue;

            if(is_start_save_data_ == true) {
                QTextStream out(&convolution_file);					//分行写入文件

                out << St_convolution_settings_.centroid << "," << St_convolution_settings_.intensity << ","  \
                    << St_convolution_settings_.intensity_integration <<"," << St_convolution_settings_.pulse_width << endl;
            }


        }
    }
    else {
        if(y.size() == size) {

            for(uint16_t i=index; i>5; i--) {
                if(y[i] >= halfPeak) {
                    peakSum += y[i]*i;
                    indexSum += y[i];
                    pulse_width++;
                }
                else {
                    if(y[i-5] > halfPeak) {
                        peakSum += y[i]*i;
                        indexSum += y[i];
                    }
                    else {
                        break;
                    }


                }
            }

            for(uint16_t i=index; i<size-5; i++) {
                if(y[i] >= halfPeak) {
                    peakSum += y[i]*i;
                    indexSum += y[i];
                    pulse_width++;
                }
                else {
                    if(y[i+5] > halfPeak) {
                        peakSum += y[i]*i;
                        indexSum += y[i];
                    }

                    break;
                }
            }

            centroidValue = (float)peakSum/indexSum;
            St_convolution_settings_.intensity = max;
            St_convolution_settings_.intensity_integration = peakSum;
            St_convolution_settings_.pulse_width = pulse_width;
            St_convolution_settings_.centroid = centroidValue;

            if(is_start_save_data_ == true) {
                QTextStream out(&convolution_file);					//分行写入文件

                out << St_convolution_settings_.centroid << "," << St_convolution_settings_.intensity << ","  \
                    << St_convolution_settings_.intensity_integration <<"," << St_convolution_settings_.pulse_width << endl;
            }


        }



    }

    //ui->widgetHistog->yAxis->setRange(0, 800);
    if(St_convolution_settings_.raw_enable == true) {
        ui->widgetHistog->graph(0)->setData(x,y);//(ticks, fossilData);
    }
    if(St_convolution_settings_.kernel_enable == true) {


        if(St_convolution_settings_.derivative_enable == true) {
            QVector<double> y2(size);
            QVector<double> y3(size);
            int y1_size = y1.size()-1;
            uint32_t y2_max = 0,y2_max_index = 0;
            int32_t  y2_mix = 10000000,y2_min_index = 0;
            float y2_x1=0,y2_y1=0,y2_x2=0,y2_y2=0;
            for(int m=0; m<y1_size; m++) {
                y2[m] = y1[m+1] - y1[m];

                if(y2[m] > y2_max) {
                    y2_max = y2[m];
                    y2_max_index = m;
                }

                if(y2[m] < y2_mix) {
                    y2_mix = y2[m];
                    y2_min_index = m;
                }
            }
            bool is_max_ok = false,is_min_ok = false;


            /*if(y2[y2_max_index + 1] > 0) {
                y2_x1 = y2_max_index + 0.5f;
                y2_y1 = (y2_max + y2[y2_max_index + 1])/2;
                is_max_ok = true;
            }

            if(y2[y2_min_index - 1] < 0) {
                y2_x2 = y2_min_index - 0.5f;
                y2_y2 = (y2_mix + y2[y2_min_index - 1])/2;
                is_min_ok = true;
            }*/


            for(uint16_t i=y2_max_index-4; i<y2_min_index+4; i++) {
                peakSum += y1[i]*i;
                indexSum += y1[i];
            }
            //qDebug() << "1: "<< y2_max_index-2 << " " << y2_min_index+2;
            //centroidValue = (float)peakSum/indexSum;
           if(y2[y2_max_index + 1] > 0) {
                y2_x1 = y2_max_index;
                y2_y1 = (y2[y2_max_index - 1] + y2_max + y2[y2_max_index + 1])/3;
                is_max_ok = true;
            }

            if(y2[y2_min_index - 1] < 0) {
                y2_x2 = y2_min_index;
                y2_y2 = (y2[y2_min_index + 1] + y2_mix + y2[y2_min_index - 1])/3;
                is_min_ok = true;
            }



            if(is_max_ok == true && is_min_ok == true) {
                qDebug() << "1: "<< y2_max_index << " " << y2_min_index << " " <<centroidValue;
                /*uint32_t y3_max = 0,y3_max_index = 0;
                int32_t  y3_mix = 10000000,y3_min_index = 0;
                float y3_x1=0,y3_y1=0,y3_x2=0,y3_y2=0;

                bool is_lock = false;
                for(int m=0; m<size; m++) {
                    y3[m] = y2[m+1] - y2[m];

                    if(y3[m] > y2_max && is_lock == false) {
                        y3_max = y3[m];
                        y3_max_index = m;

                    }

                    if(y3[m] < y3_mix) {
                        y3_mix = y3[m];
                        y3_min_index = m;
                        is_lock = true;
                        qDebug()<< "true";

                    }
                }*/

                //St_convolution_settings_.centroid = y3_x1 - y3_y1*(y3_x2 - y3_x1)/(y3_y2 - y3_y1);
                centroidValue = y2_x1 - y2_y1*(y2_x2 - y2_x1)/(y2_y2 - y2_y1);
                float centroidValue_integration = (float)peakSum/indexSum;
                qDebug() << "1: "<< y2_max_index << " " << y2_min_index << " " <<centroidValue << " "<<centroidValue_integration;
                //qDebug()<< "true";
            }
            else {
                 qDebug() << "2: "<< y2_max_index;
                St_convolution_settings_.centroid = y2_max_index \
                        - y2_max*(y2_min_index - y2_max_index)/(y2_mix - y2_max);
                //qDebug()<< "false";
            }





            ui->widgetHistog->graph(1)->setData(x1,y2);
            ui->widgetHistog->yAxis->setRange(y2_mix*1.1, y2_max*1.1);
        }
        else {
            ui->widgetHistog->graph(1)->setData(x2,y2);
            //ui->widgetHistog->yAxis->setRange(noise_base*0.9, max*1.2);
            ui->widgetHistog->yAxis->setRange(0, max*1.2/convolution_max_rate);
        }




    }
    else {
        ui->widgetHistog->yAxis->setRange(0, max1*1.1);
    }
    emit sendHistDis(centroidValue);
    wavePacketText->setText("tof: " + QString::number(index,10) \
                            + "  peak: " + QString::number(max,10)\
                            + "  centroid: " + QString::number(centroidValue,'f',3)
                            + "  integration: " + QString::number(St_convolution_settings_.intensity_integration,10)
                            + "  pw: "+ QString::number(pulse_width,10));//
    ui->widgetHistog->replot(QCustomPlot::rpQueuedReplot);//刷新图表
}
