<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>histogramChart</class>
 <widget class="QWidget" name="histogramChart">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1045</width>
    <height>650</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>histogram</string>
  </property>
  <property name="windowIcon">
   <iconset resource="logo2.qrc">
    <normaloff>:/new/prefix1/icon/logo/histogram.png</normaloff>:/new/prefix1/icon/logo/histogram.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QCustomPlot" name="widgetHistog" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="logo2.qrc"/>
 </resources>
 <connections/>
</ui>
