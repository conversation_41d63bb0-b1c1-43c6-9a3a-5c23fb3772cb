#include "greymap_chart.h"
#include "ui_greymapchart.h"

GreymapChart::Greymap<PERSON>hart(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::GreymapChart),
    lastSpadCount(18*4),
    lastSpadRow(3),
    lastSpadColumn(6)
{
    ui->setupUi(this);

    ui->tableWidget->setRowCount(lastSpadRow);
    ui->tableWidget->setColumnCount(lastSpadColumn);
    //ui->tableWidget->verticalHeader()->setVisible(false); //隐藏列表头
    //ui->tableWidget->horizontalHeader()->setVisible(false); //隐藏行表头

    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);    //x先自适应宽度
    ui->tableWidget->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch);    //x先自适应宽度

    ui->tableWidget->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(0, 102, 116);}");
    ui->tableWidget->verticalHeader()->setStyleSheet("QHeaderView::section{background:rgb(0, 102, 116);}");
    for(uint16_t m=0; m<lastSpadColumn; m++) { /*先建立表格模式*/
        ui->tableWidget->setItem(0,m,new QTableWidgetItem(""));
        ui->tableWidget->setItem(1,m,new QTableWidgetItem(""));
        ui->tableWidget->setItem(2,m,new QTableWidgetItem(""));
    }
    ui->tableWidget->show();
    ui->tableWidget->setEnabled(false);
}

GreymapChart::~GreymapChart()
{
    delete ui;
}

void GreymapChart::receiveGreymapData(QByteArray rec)
{
    /*判断是否需要重置表格*/
    uint32_t size = rec.size();
    if(lastSpadCount != size) {
        for(int row = 0;row < ui->tableWidget->rowCount();row++) {/*删除行*/
            ui->tableWidget->removeRow(0);
        }
        for(int Column = 0;Column < ui->tableWidget->columnCount();Column++) {/*删除列*/
            ui->tableWidget->removeColumn(0);
        }

        if(size == 16*10*4) {
            uint16_t columnCnt = size/40;
            lastSpadRow = 10;
            lastSpadColumn = columnCnt;
            ui->tableWidget->setRowCount(lastSpadRow);
            ui->tableWidget->setColumnCount(lastSpadColumn);
            for(uint16_t m=0; m<10; m++) { /*先建立表格模式*/
                for(uint16_t n=0; n<columnCnt; n++) {
                    ui->tableWidget->setItem(m,n,new QTableWidgetItem(""));
                }
            }

        }
        else if(size == 3*6*4) {
            uint16_t columnCnt = size/12;
            lastSpadRow = 3;
            lastSpadColumn = columnCnt;
            ui->tableWidget->setRowCount(lastSpadRow);
            ui->tableWidget->setColumnCount(lastSpadColumn);
            for(uint16_t m=0; m<3; m++) { /*先建立表格模式*/
                for(uint16_t n=0; n<columnCnt; n++) {
                    ui->tableWidget->setItem(m,n,new QTableWidgetItem(""));
                }
            }
        }
        else if(size == 5*5*4) {
            uint16_t columnCnt = size/20;
            lastSpadRow = 5;
            lastSpadColumn = columnCnt;
            ui->tableWidget->setRowCount(lastSpadRow);
            ui->tableWidget->setColumnCount(lastSpadColumn);
            for(uint16_t m=0; m<5; m++) { /*先建立表格模式*/
                for(uint16_t n=0; n<columnCnt; n++) {
                    ui->tableWidget->setItem(m,n,new QTableWidgetItem(""));
                }
            }
        }
        else if(size == 2*4*4) {
            uint16_t columnCnt = size/8;
            lastSpadRow = 2;
            lastSpadColumn = columnCnt;
            ui->tableWidget->setRowCount(lastSpadRow);
            ui->tableWidget->setColumnCount(lastSpadColumn);
            for(uint16_t m=0; m<2; m++) { /*先建立表格模式*/
                for(uint16_t n=0; n<columnCnt; n++) {
                    ui->tableWidget->setItem(m,n,new QTableWidgetItem(""));
                }
            }
        }
        lastSpadCount = lastSpadRow*lastSpadColumn*4;
        ui->tableWidget->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(230,230,250);color: black;}");
        ui->tableWidget->verticalHeader()->setStyleSheet("QHeaderView::section{background:rgb(230,230,250);color: black;}");
        ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);//设置整行选中
        ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectColumns);//设置整行选中

    }

    /*获取数据*/
    uint32_t *buff = new uint32_t[size/4];
    uint8_t *dataPtr = (uint8_t*)rec.data();
    memcpy(buff,dataPtr,size);

    /*遍历最大值*/
    uint32_t max = 0 ,index = 0;
    for(uint16_t m=0; m<size/4; m++) {
        if(buff[m] > max) {
            max = buff[m];
            index = m;
        }
    }
    Q_UNUSED(index)

    if(lastSpadRow <= 2) {/*芯视界 3*6*/
        for(uint16_t m=0; m<lastSpadColumn; m++) { /*先建立表格模式*/  /*6*/
            for(uint16_t n=0; n<lastSpadRow; n++) { /*3*/
                uint32_t value = buff[m*lastSpadRow + n];
                ui->tableWidget->item(n,m)->setText(QString::number(value,10));
                if(max == 0) {
                    max = 1;
                }
                uint8_t col = 255-255*value/max;

                ui->tableWidget->item(n,m)->setBackground(QBrush(QColor(col,col,col)));
                ui->tableWidget->item(n,m)->setForeground(QBrush(QColor(0,255,0)));
                if(lastSpadRow > 5) {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 16));
                }
                else {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 26));
                }

                ui->tableWidget->item(n,m)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
            }
        }
    }

    if(lastSpadRow <= 3) {/*芯视界 3*6*/
        for(uint16_t m=0; m<lastSpadColumn; m++) { /*先建立表格模式*/  /*6*/
            for(uint16_t n=0; n<lastSpadRow; n++) { /*3*/
                uint32_t value = buff[m*lastSpadRow + n];
                ui->tableWidget->item(n,m)->setText(QString::number(value,10));
                if(max == 0) {
                    max = 1;
                }
                uint8_t col = 255-255*value/max;

                ui->tableWidget->item(n,m)->setBackground(QBrush(QColor(col,col,col)));
                ui->tableWidget->item(n,m)->setForeground(QBrush(QColor(0,255,0)));
                if(lastSpadRow > 5) {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 16));
                }
                else {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 26));
                }

                ui->tableWidget->item(n,m)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
            }
        }
    }
    if(lastSpadRow <= 5) {/*芯视界4302 3*6*/
        for(uint16_t m=0; m<lastSpadColumn; m++) { /*先建立表格模式*/  /*5*/
            for(uint16_t n=0; n<lastSpadRow; n++) { /*5*/
                uint32_t value = buff[m*lastSpadRow + n];
                ui->tableWidget->item(n,m)->setText(QString::number(value,10));
                if(max == 0) {
                    max = 1;
                }
                uint8_t col = 255-255*value/max;

                ui->tableWidget->item(n,m)->setBackground(QBrush(QColor(col,col,col)));
                ui->tableWidget->item(n,m)->setForeground(QBrush(QColor(0,255,0)));
                if(lastSpadRow > 5) {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 16));
                }
                else {
                    ui->tableWidget->item(n,m)->setFont(QFont("song", 26));
                }

                ui->tableWidget->item(n,m)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
            }
        }
    }
    else if(lastSpadRow <= 10) {/*灵明10*16*/
            for(uint16_t m=0; m<lastSpadRow; m++) { /*先建立表格模式*/  /*3*/
                for(uint16_t n=0; n<lastSpadColumn; n++) { /*6*/
                    uint32_t value = buff[m*n + n];
                    ui->tableWidget->item(m,n)->setText(QString::number(value,10));
                    if(max == 0) {
                        max = 1;
                    }
                    uint8_t col = 255-255*value/max;
                    //qDebug() << "col" << col << " " << value << " "<< max;
                    ui->tableWidget->item(m,n)->setBackground(QBrush(QColor(col,col,col)));
                    ui->tableWidget->item(m,n)->setForeground(QBrush(QColor(0,255,0)));
                    if(lastSpadRow > 5) {
                        ui->tableWidget->item(m,n)->setFont(QFont("song", 16));
                    }
                    else {
                        ui->tableWidget->item(m,n)->setFont(QFont("song", 26));
                    }

                    ui->tableWidget->item(m,n)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
                }
            }
    }

    delete []buff;
}


