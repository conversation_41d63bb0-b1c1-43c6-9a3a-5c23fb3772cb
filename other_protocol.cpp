#include "other_protocol.h"
#include <QThread>
#include <QFile>

namespace  otherdevice{

    ProcessProtocol::ProcessProtocol() : isQuitThread(false)
        , protocolTyp(0)
        ,ver(0)
        ,voltage(0)
        ,health(0)
        ,checkSumCnt(0)
        ,isRecordDataToDisk(false)
    {

    }
    ProcessProtocol::~ProcessProtocol()
    {

    }

    void ProcessProtocol::QuitThread(bool isClose)
    {
        isQuitThread = isClose;
        qDebug()<<thName<<": Other QuitThread id"<< QThread::currentThreadId();
    }

    void ProcessProtocol::ParseProtocol()
    {
        //qDebug()<<thName<<": ParseProtocol id"<< QThread::currentThreadId();
        int parseDataStatus = kParseNone;

        /*粘包*/
        ringBuff += GetSerialPtr()->readAll();
        /*获取长度*/
        int length = ringBuff.length();

        if(isRecordDataToDisk == true) {
            QFile file("RecordData.txt");
            file.open(QIODevice::WriteOnly | QIODevice::Append);
            QTextStream text_stream(&file);
            text_stream << ringBuff.toHex(' ') << "\r\n";
            file.flush();
            file.close();
        }
        /*转化为uint8_t 数组*/
        uint8_t *data = (uint8_t*)ringBuff.data();
        /*解析数据*/
        for(int n=0; n<length; n++) {
            /*剩余长度*/
            int leftDataNum = length - n;
            /*获取头标识*/
            if(data[n] == kFrameHead1) {
                if(leftDataNum < 6) {/*小于获取长度信息的索引值 继续接收数据*/
                    return;
                }
                parseDataStatus = kParseData1;
            }
            else if(data[n] == kFrameHead2) {
                parseDataStatus = kParseData2;
            }
            else if(data[n] == kFrameHead3) {
                if(leftDataNum < 4) {/*小于获取长度信息的索引值 继续接收数据*/
                    return;
                }
                parseDataStatus = kParseData3;
            }
            else {
                parseDataStatus = kParseNone;
            }

            /*判断数据长度与解析数据*/
            switch(parseDataStatus) {
                case kParseData1 :
                    //qDebug() << "entering parse 1...";
                    //header(1) + cmd(1) + id(1) + checkXor(1) + byteNumber(2) + data(n)
                    //header(1) + 0x5a(1) + num(2) + sum(2) + type(1) + data(n)
                    //header(1) + 0x5a(1) + 0x04(1) + 0x00(1) + 保留(2) + 0x06(1) + xor(1) + MSB(1) + LSB(1) + MSB(1)
                    if(data[n+1] == 0x5A && data[n+6] == 0x01) {/*获取雷达信息*/
                        int dataNum = data[n+3]<<8 | data[n+2];
                        int frameNum = 7 + dataNum;
                        if(leftDataNum < frameNum) {/*长度不够获取起始包*/
                            qDebug() << "lidar information return!";
                            return;
                        }

                        uint16_t checkSum = 0,check = data[n+5]<<8 | data[n+4];
                        for(int m=0; m<frameNum; m++) {
                            if(m == 4 || m == 5) {
                                continue;
                            }
                            checkSum += data[n+m];
                        }
                        if(checkSum == check) {
                            /*get lidar device info*/
                            //qDebug() << "current version: " << data[n+26];
                            ver = data[n+26];
                            ringBuff.remove(0,n+frameNum);
                            qDebug() << "lidar information!";
                        }
                        else {
                            ringBuff.remove(0,n+7);
                            qDebug() << "lidar information check err!";
                        }


                    }
                    else if(data[n+1] == 0x5A && data[n+2] == 0x04) {
                        if(leftDataNum < 11) {/*长度不够获取起始包*/
                            qDebug() << "lidar health code return!";
                            return;
                        }
                        uint16_t tmpVoltage = 0;
                        if(((data[n+4]<<8) + data[n+5]) == 0) {
                            tmpVoltage = 0;
                        }
                        else {
                            tmpVoltage = ~(((data[n+4]<<8) + data[n+5]) + 122);
                        }

                        voltage = tmpVoltage;
                        QByteArray volCode;

                        health = data[n+9] | (data[n+10]<<8);
                        volCode.append(data[n+10]);
                        volCode.append(data[n+9]);

                        ringBuff.remove(0,n+11);
                        qWarning()<<"";
                        qWarning() << "lidar health code! " << volCode.toHex();
                        qWarning()<<"";
                    }
                    else if((data[n+1]&0x02) == 0x02 && (data[n+1]&0x01) == 0x00 && (data[n+1]&0xC0) == 0x00) {/*获取不同的应答信息  根据cmd和id区分...*/
                        int dataNum = data[n+5]<<8 | data[n+4];
                        int frameNum = 6 + dataNum*2;
                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 2048+6+10) {
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar interaction callback return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }

                        uint8_t checkSum = 0,check = data[n+3];
                        for(int m=0; m<frameNum; m++) {
                            if(m != 3) {
                                checkSum ^= data[n+m];
                            }
                        }
                        if(checkSum == check) {
                            /*get lidar device info*/
                            if((data[n+1]&0x22) == 0x22) {
                                QByteArray str;
                                if(data[n+2] == 0xAC) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    transmitHistogramData(str);
                                }
                                else if(data[n+2] == 0xAD) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    qDebug() << "rec greymap size : " << str.size();
                                    transmitGreymapData(str);
                                }
                                else if(data[n+2] == 0xAE) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    transmitCalibrationData(str);
                                }
                            }
                            else {
                                QByteArray str;
                                //str.push_back(data[n+0]);
                                //str.push_back(data[n+1]);/*cmd*/
                                //str.push_back(data[n+2]);/*id*/
                                str.append((char*)&data[n],dataNum*2+6);/*data*/                               
                                FeedbackInfo(str);

                            }

                            //transmitGreymapData(str);
                            //transmitCalibrationData(str);


                            ringBuff.remove(0,n+frameNum);

                            //qDebug() << "lidar interaction callback!";
                        }
                        else {
                            checkSumCnt++;
                            qDebug() << "n: " << n << " "<< ringBuff.toHex() ;
                            qDebug() << "lidar interaction callback check err!";
                            ringBuff.remove(0,n+6);
                        }

                    }
                    else {

                        qDebug() << "n: " << n << " "<< ringBuff.toHex() ;
                        ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/
                        qDebug() << "remove header";
                    }
                    n = -1;
                    length = ringBuff.length();
                    //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/

                    break;
                case kParseData2 :

                    break;
                case kParseData3 :
                    if(data[n+1] == 0x55 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
                        int dataNum = data[n+3];
                        int frameNum = 10 + dataNum*3;

                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 1024) {
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar scan return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }
                        else {
                            uint16_t check = data[n+9]<<8 | data[n+8];
                            uint8_t checkSumH = 0,checkSumL = 0;
                            checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
                            checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
                            for(int m=0; m<dataNum*3; m=m+3) {
                                /*进行异或校验提取*/
                                checkSumL ^= data[n+10+m]^data[n+10+m+1];
                                checkSumH ^= data[n+10+m+2];
                            }
                            if((checkSumH<<8|checkSumL) == check) {
                                /*get lidar pointcloud info*/
                                float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
                                float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
                                double incAngle = 0;
                                bool isParse = true;
                               // qDebug()<< " "<< startAngle << " " << endAngle;

                                //2.0 计算角度
                                if(dataNum != 0)
                                {
                                    //qDebug() << startAngle << " " << endAngle;
                                    if(startAngle > endAngle)
                                    {
                                        incAngle = (360 - startAngle + endAngle)/(dataNum-1);
                                    }
                                    else
                                    {
                                        incAngle = (endAngle - startAngle)/((dataNum-1) == 0 ? 1:(dataNum-1));
                                    }

                                    if(endAngle < startAngle && endAngle > 26) {
                                           isParse = false;
                                           qDebug() << "nn: " << n << " "<< ringBuff.toHex() ;
                                    }
                                }
                                if(isParse == true) {
                                    if((data[n+2]&0x01) == 0x01) {/*起始包*/
                                        //2.1 计算距离
                                        double spd = (data[n+2]>>1)/10.0;
                                        for(int m=0; m<dataNum; m++)
                                        {
                                            quint16 down= (data[n+10+3*m+1]>>2)&0x3f;
                                            uint16_t up = 0x00ff&data[n+10+3*m+2];//左移高位会自动补1
                                            up = up<<6;

                                            float tmpdepth = up | down;

                                            //tmpdepth = sqrt(tmpdepth*tmpdepth - 15.6*64.0);
                                            //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                            float tmpAngle = startAngle ;

                                            uint tmpIndensity = ((data[n+10+3*m+1]&0x03)<<6) | (((data[n+10+3*m])>>2)&0x3f);
                                            tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                            PolarData dat;
                                            dat.angle = tmpAngle;
                                            dat.deepth = tmpdepth;
                                            dat.indensity = tmpIndensity;
                                            protocolData.speed = spd;
                                            protocolData.version = ver;
                                            protocolData.mcuVoltage = voltage;
                                            protocolData.healthCode = health;
                                            protocolData.data.push_back(dat);
                                            vprotocolData.push_back(protocolData);

                                            //qDebug() << "total size: " <<protocolData.data.size();
                                            emit transmitPointCloudData(vprotocolData);
                                            //emit ProcessSendToMainPointCloud(vprotocolData);
                                            protocolData.data.clear();
                                            protocolData.speed = 0;

                                            vprotocolData.clear();
                                            //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
                                        }
                                    }
                                    else {/*点云包*/
                                    //2.1 计算距离
                                    for(int m=0; m<dataNum; m++)
                                    {
                                        quint16 down= (data[n+3*m+1+10]>>2)&0x3f;
                                        uint16_t up = 0x00ff&data[n+3*m+2+10];//左移高位会自动补1
                                        up = up<<6;

                                        float tmpdepth = up | down;
                                        //tmpdepth = sqrt((4*tmpdepth*tmpdepth)/(64.0) - 15.6*64.0);
                                        //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                        float tmpAngle = startAngle + m*incAngle ;

                                        uint tmpIndensity = ((data[n+3*m+1+10]&0x03)<<6) | (((data[n+3*m+10])>>2)&0x3f);
                                        tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                        PolarData dat;
                                        dat.angle = tmpAngle;
                                        dat.deepth = tmpdepth;
                                        dat.indensity = tmpIndensity;
                                        protocolData.data.push_back(dat);
                                       // qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
                                    }
                                }
                                }
                                //qDebug() << "finished!";
                                ringBuff.remove(0,n+frameNum);
                            }
                            else {
                                 qDebug() << "n: " << n << " "<< ringBuff.toHex() ;
                                 qDebug() << "lidar scan check err!";
                                 ringBuff.remove(0,n+10);
                            }
                        }
                    }
                    else if(data[n+1] == 0x66 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
                        int dataNum = data[n+3];
                        int frameNum = 10 + dataNum*4;

                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 1024) {
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar calibration return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }
                        else {
                            uint16_t check = data[n+9]<<8 | data[n+8];
                            uint8_t checkSumH = 0,checkSumL = 0;
                            checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
                            checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
                            for(int m=0; m<dataNum*4; m=m+4) {
                                /*进行异或校验提取*/
                                checkSumL ^= data[n+10+m]^data[n+10+m+1];
                                checkSumH ^= data[n+10+m+2]^data[n+10+m+3];
                            }
                            if((checkSumH<<8|checkSumL) == check) {
                                /*get lidar pointcloud info*/
                                float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
                                float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
                                //qDebug()<< "s: "<< startAngle << "e: " << endAngle;
                                double incAngle = 0;
                                bool isParse = true;
                                //2.0 计算角度
                                if(dataNum != 0)
                                {
                                    if(startAngle > endAngle)
                                    {
                                        incAngle = (360 - startAngle + endAngle)/(dataNum-1);
                                    }
                                    else
                                    {
                                        incAngle = (endAngle - startAngle)/(dataNum-1);
                                    }
                                    if(endAngle < startAngle && endAngle > 26) {
                                        isParse = false;
                                        qDebug() << "nn: " << n << " "<< ringBuff.toHex() ;
                                    }
                                }
                                if(isParse == true) {
                                    if((data[n+2]&0x01) == 0x01) {/*起始包*/
                                        //2.1 计算距离
                                        double spd = (data[n+2]>>1)/10.0;;
                                        for(int m=0; m<dataNum; m++)
                                        {
                                            uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

                                            float tmpdepth = up/100.0*15.55;
                                            //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);

                                            float tmpAngle = startAngle ;

                                            uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
                                            tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                            PolarData dat;
                                            dat.angle = tmpAngle;
                                            dat.deepth = tmpdepth;
                                            dat.indensity = tmpIndensity;
                                            protocolData.speed = spd;
                                            protocolData.version = ver;
                                            protocolData.mcuVoltage = voltage;
                                            protocolData.healthCode = health;
                                            protocolData.data.push_back(dat);
                                            vprotocolData.push_back(protocolData);
                                            emit transmitPointCloudData(vprotocolData);
                                            //emit ProcessSendToMainPointCloud(vprotocolData);
                                            protocolData.data.clear();
                                            protocolData.speed = 0;

                                            vprotocolData.clear();
                                            //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
                                        }
                                    }
                                    else {/*点云包*/
                                    //2.1 计算距离
                                    for(int m=0; m<dataNum; m++)
                                    {
                                        uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

                                        float tmpdepth = up/100.0*15.55;
                                        //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                        //tmpdepth = sqrt(tmpdepth*tmpdepth - 64.0);
                                        float tmpAngle = startAngle + m*incAngle ;

                                        uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
                                        tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                        PolarData dat;
                                        dat.angle = tmpAngle;
                                        dat.deepth = tmpdepth;
                                        dat.indensity = tmpIndensity;
                                        protocolData.data.push_back(dat);
                                        //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
                                    }
                                }
                                }
                                ringBuff.remove(0,n+frameNum);
                            }
                            else {
                                 qDebug() << "lidar calibration check err!";
                                 ringBuff.remove(0,n+10);
                            }
                        }
                    }
                    else {
                        qDebug() << "n: " << n << " "<< ringBuff.toHex() ;
                        qDebug() << "lidar calibration no header!";
                        ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/

                    }
                    n = -1;
                    length = ringBuff.length();
                    //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/
                    break;
                default:
                    break;
            }

        }


    }
    void ProcessProtocol::TransmitData(QByteArray str)//通过槽函数处理的才是多线程中的函数
    {
        qDebug()<<thName<<": Other TransmitData id"<< QThread::currentThreadId();
        Q_UNUSED(str)
        if(GetSerialPtr()->isOpen())
        {
            GetSerialPtr()->write(str);
        }
    }
    void ProcessProtocol::SetProtocolType(int protocolType)
    {
        qDebug()<<thName<<": Other SetProtocolType id"<< QThread::currentThreadId() << " "<<protocolType ;
        protocolTyp = protocolType;
    }
    void ProcessProtocol::RecordData(bool isOk)
    {
        isRecordDataToDisk = isOk;
        QFile file("RecordData.txt");
        if(file.open(QIODevice::WriteOnly | QIODevice::Append)) {
            QTextStream text_stream(&file);
            QString currentDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh-mm-ss");
            text_stream << currentDate << " "<< isRecordDataToDisk <<"\r\n";
            file.flush();
            file.close();
        }
}
}
