<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PointCloudsChart</class>
 <widget class="QMainWindow" name="PointCloudsChart">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>969</width>
    <height>750</height>
   </rect>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="windowTitle">
   <string>PointCloud</string>
  </property>
  <property name="windowIcon">
   <iconset resource="logo2.qrc">
    <normaloff>:/new/prefix1/icon/logo/sacn.png</normaloff>:/new/prefix1/icon/logo/sacn.png</iconset>
  </property>
  <property name="layoutDirection">
   <enum>Qt::RightToLeft</enum>
  </property>
  <property name="autoFillBackground">
   <bool>true</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 12pt &quot;Agency FB&quot;;
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    border: 2px solid gray;
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QCustomPlot" name="customPlot" native="true">
      <property name="autoFillBackground">
       <bool>true</bool>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="toolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="font">
    <font>
     <family>Agency FB</family>
     <pointsize>12</pointsize>
     <weight>9</weight>
     <italic>false</italic>
     <bold>false</bold>
     <underline>false</underline>
     <stylestrategy>PreferDefault</stylestrategy>
     <kerning>true</kerning>
    </font>
   </property>
   <property name="tabletTracking">
    <bool>false</bool>
   </property>
   <property name="acceptDrops">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="allowedAreas">
    <set>Qt::AllToolBarAreas</set>
   </property>
   <property name="iconSize">
    <size>
     <width>50</width>
     <height>50</height>
    </size>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <property name="floatable">
    <bool>true</bool>
   </property>
   <attribute name="toolBarArea">
    <enum>LeftToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionStandBy"/>
   <addaction name="actionRunning"/>
   <addaction name="separator"/>
   <addaction name="actionScreenShot"/>
   <addaction name="actionSaveAs"/>
   <addaction name="separator"/>
   <addaction name="actionRecord"/>
   <addaction name="actionImportCloud"/>
   <addaction name="actionRecordCalibration"/>
   <addaction name="actionPointcloudQuality"/>
  </widget>
  <action name="actionStandBy">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo.qrc">
     <normaloff>:/new/prefix1/icon/stop2.png</normaloff>:/new/prefix1/icon/stop2.png</iconset>
   </property>
   <property name="text">
    <string>StandBy</string>
   </property>
   <property name="toolTip">
    <string>停止雷达</string>
   </property>
  </action>
  <action name="actionRunning">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo.qrc">
     <normaloff>:/new/prefix1/icon/running2.png</normaloff>:/new/prefix1/icon/running2.png</iconset>
   </property>
   <property name="text">
    <string>TurnOn</string>
   </property>
   <property name="toolTip">
    <string>运行雷达</string>
   </property>
  </action>
  <action name="actionScreenShot">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/shot.png</normaloff>:/new/prefix1/icon/logo/shot.png</iconset>
   </property>
   <property name="text">
    <string>Scrshot</string>
   </property>
   <property name="toolTip">
    <string>截图</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/download2.png</normaloff>:/new/prefix1/icon/logo/download2.png</iconset>
   </property>
   <property name="text">
    <string>SaveAs</string>
   </property>
   <property name="toolTip">
    <string>截图保存</string>
   </property>
  </action>
  <action name="actionRecord">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/record.png</normaloff>:/new/prefix1/icon/logo/record.png</iconset>
   </property>
   <property name="text">
    <string>Record</string>
   </property>
   <property name="toolTip">
    <string>记录数据</string>
   </property>
  </action>
  <action name="actionImportCloud">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/import.png</normaloff>:/new/prefix1/icon/logo/import.png</iconset>
   </property>
   <property name="text">
    <string>ImtPtCld</string>
   </property>
   <property name="toolTip">
    <string>导入数据</string>
   </property>
  </action>
  <action name="actionRecordCalibration">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/fit2.png</normaloff>:/new/prefix1/icon/logo/fit2.png</iconset>
   </property>
   <property name="text">
    <string>Calibra</string>
   </property>
   <property name="toolTip">
    <string>计算校正参数</string>
   </property>
  </action>
  <action name="actionRecord_4">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset>
     <normaloff>:/icon/logo.ico</normaloff>:/icon/logo.ico</iconset>
   </property>
   <property name="text">
    <string>Record</string>
   </property>
   <property name="toolTip">
    <string>Record</string>
   </property>
  </action>
  <action name="actionPointcloudQuality">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/calibration3.png</normaloff>:/new/prefix1/icon/logo/calibration3.png</iconset>
   </property>
   <property name="text">
    <string>ImageQuality</string>
   </property>
   <property name="toolTip">
    <string>计算点云质量</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="logo2.qrc"/>
  <include location="logo.qrc"/>
 </resources>
 <connections/>
</ui>
