#include "task_base.h"

//void task1_process(Task_base& object)
//{

//}

//void task2_process(Task_base& object)
//{

//}

//void task3_process(Task_base& object)
//{

//}

//void task4_process(Task_base& object)
//{

//}


//Task_base::Task_base(QObject *parent) : QObject(parent)
//{
//    currentIndex = 0;
//    timer = new QTimer(this);
//    timer->setSingleShot(true);
//    connect(timer, &QTimer::timeout, this, &Task_base::handle_timeout);
//}

//void Task_base::execute_current_task() {
//    if (currentIndex >= tasks.size()) {
//        // 所有任务执行完成
//        qDebug() << "所有任务执行完成";
//        return;
//    }

//    auto current_task = tasks.at(currentIndex);
//    current_task();

//    // 启动超时定时器，设置超时时间为1秒
//    timer->start(1000);
//}

//void Task_base::handle_timeout() {
//    // 超时处理
//    qDebug() << "任务超时";

//    // 继续执行下一个任务
//    currentIndex++;
//    execute_current_task();
//}
