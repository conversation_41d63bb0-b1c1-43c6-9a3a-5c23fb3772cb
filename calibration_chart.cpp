#include "calibration_chart.h"
#include "ui_calibrationchart.h"
#include "dtof_calibration.h"
#include <Eigen/Dense>
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Cholesky>

#define SELECT_MAX(x,y) x>y?x:y
#define SELECT_MIN(a,b) a<b?a:b



const uint kTableCount = 15;

//int multiCalibrationProcess(std::vector<std::vector<std::vector<float>>> dataHr,std::vector<std::vector<std::vector<float>>> dataWt,std::vector<std::vector<std::vector<float>>> dataBk,std::vector<float> realDis,uint8_t type);


CalibrationChart::CalibrationChart(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CalibrationChart),
    isStartSample(false),
    sampleCnt(0),
    sampleIndex(1),
    isStartRecordFile(false),
    sequenceCnt(0),
    isSequence(false),
    tempOffset(100.0),
    totalRows(0),
    trackCnt(0),
    isExecuteStep(false),
    isReached(false),
    isSaveTrackFile(false)
{
    ui->setupUi(this);
    ui->widgetCalibration->setInteractions(QCP::iRangeDrag|QCP::iRangeZoom| QCP::iSelectAxes
                                                  |QCP::iSelectLegend | QCP::iSelectPlottables);

    ui->widgetCalibration->plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
    ui->widgetCalibration->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    //@1主视图的轴
    QCPLayoutGrid *subLayout = new QCPLayoutGrid;
    //@3分配视图
    ui->widgetCalibration->plotLayout()->addElement(0, 0, subLayout);     // 在第一行添加轴矩形

    subRectLeft = new QCPAxisRect(ui->widgetCalibration, true);   // 不配置轴
    //subRectRight = new QCPAxisRect(&customPlot, true);

    //@3.1定义文本数据
//    wavePacketText = new QCPItemText(ui->widgetCalibration);
//    wavePacketText->setPositionAlignment(Qt::AlignTop|Qt::AlignLeft);//文字布局：顶、左对齐
//    wavePacketText->position->setType(QCPItemPosition::ptAxisRectRatio);//位置类型（当前轴范围的比例为单位/实际坐标为单位）
//    wavePacketText->position->setCoords(0.01, 0.03); //把文字框放在X轴的中间，Y轴的最顶部
//    wavePacketText->setFont(QFont("仿宋",16,QFont::Bold)); //字体大小
//    wavePacketText->setColor(QColor(255,128,0));
//    wavePacketText->setPadding(QMargins(2,2,2,2));//文字距离边框几个像素


    subLayout->addElement(0, 0, subRectLeft);                      // 在第一列添加轴矩形

    QPen *pen = new QPen();

    //温度
    subGraphRando = ui->widgetCalibration->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
    subGraphRando->setLineStyle(QCPGraph::lsLine);
    //subGraphRando->setScatterStyle(QCPScatterStyle::ssPlus);
    pen->setWidthF(1.0);
    pen->setColor(QColor(255,128,0));
    subGraphRando->setPen(*pen);
    subGraphRando->rescaleKeyAxis();


    subGraphRandoPoint = ui->widgetCalibration->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
    //subGraphRandoPoint->setScatterStyle(QCPScatterStyle::ssCircle);
    subGraphRandoPoint->setLineStyle(QCPGraph::lsLine);
    pen->setColor(QColor(0,0,0));
    pen->setWidthF(1.0);
    subGraphRandoPoint->setPen(*pen);
    subGraphRandoPoint->rescaleKeyAxis();

    subRectLeft->axis(QCPAxis::atLeft)->ticker()->setTickCount(8); // 设置轴的刻度为一个固定的步进值
    subRectLeft->axis(QCPAxis::atBottom)->ticker()->setTickCount(8);
    subRectLeft->axis(QCPAxis::atLeft)->setTickPen(QPen(QColor(50, 50, 50),2.0));
    subRectLeft->axis(QCPAxis::atBottom)->setTickPen(QPen(QColor(50, 50, 50),2.0));
    subRectLeft->axis(QCPAxis::atBottom)->setLabel(QString("times"));
    subRectLeft->axis(QCPAxis::atLeft)->setLabel(QString("value"));
    subRectLeft->axis(QCPAxis::atBottom)->setTickLabelRotation(0);
    subRectLeft->axis(QCPAxis::atLeft)->setTickLabelRotation(0);
    subRectLeft->axis(QCPAxis::atBottom)->setLabelFont(QFont("仿宋",12,QFont::Bold));
    subRectLeft->axis(QCPAxis::atLeft)->setLabelFont(QFont("仿宋",12,QFont::Bold));
    //subRectLeft->axis(QCPAxis::atBottom)->setLabelColor(QColor(0,0,255));
    //subRectLeft->axis(QCPAxis::atLeft)->setLabelColor(QColor(255,0,0));
    subRectLeft->axis(QCPAxis::atBottom)->setRange(0,600);
    subRectLeft->axis(QCPAxis::atLeft)->setRange(0,100);
    //subRectLeft->axis(QCPAxis::atLeft)->setBasePen(QPen(Qt::red,4));
    //subRectLeft->axis(QCPAxis::atBottom)->setBasePen(QPen(Qt::blue,4));
    subRectLeft->axis(QCPAxis::atLeft)->setUpperEnding(QCPLineEnding::esSpikeArrow);
    subRectLeft->axis(QCPAxis::atBottom)->setUpperEnding(QCPLineEnding::esSpikeArrow);
    //subRectLeft->setBackground(QBrush(QColor(44, 62, 80)));//0, 102, 116
    //subRectLeft->axis(QCPAxis::atLeft)->grid()->setPen(QPen(QColor(40,175,100), 0.1, Qt::PenStyle::DashLine));
    subRectLeft->axis(QCPAxis::atLeft)->grid()->setVisible(true);
    //subRectLeft->axis(QCPAxis::atBottom)->grid()->setPen(QPen(QColor(40,175,100), 0.1, Qt::PenStyle::DashLine));
    subRectLeft->axis(QCPAxis::atBottom)->grid()->setVisible(true);


    ui->widgetCalibration->show();


    pf = new  Polynomial();

    //ui->horizontalSlider->setMaximum(100);
    //startTimer(2);
    sampleAver.resize(8);
    sampleAverPeak.resize(8);

    /**************table view****************/
    curItemModel = new QStandardItemModel();
     /* 设置列数 */
    curItemModel->setColumnCount(1);



    /* 设置行数 */
    //curItemModel->setRowCount(2);
    //curItemModel->setHeaderData(0, Qt::Vertical, "transmit");
    //curItemModel->setHeaderData(1, Qt::Vertical, "reception");

    ui->tableView->setModel(curItemModel);
    /* 设置列宽在可视界面自适应宽度 */
    ui->tableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    ui->tableView->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(192, 192, 192);}");

    /* 行颜色交替显示 */
    ui->tableView->setAlternatingRowColors(true);
    /* 不允许在图形界面修改内容 */
    ui->tableView->setEditTriggers(QAbstractItemView::SelectedClicked);


    //必须先对tableview配置如下功能
    ui->tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableView->setContextMenuPolicy(Qt::CustomContextMenu);

    /* 显示表 */
    ui->tableView->show();

    //菜单等事件
    RightClickMenu = new QMenu();               //右键点击菜单
    deleteAction = new QAction("删除",this);               //单行删除事件
    deleteMultiAction = new QAction("删除全部",this);       //多行删除
    RightClickMenu->addAction(deleteAction);    //将action添加到菜单内
    RightClickMenu->addAction(deleteMultiAction);

    //信号槽连接
    connect(ui->tableView,&QTableView::customContextMenuRequested,this,&CalibrationChart::onGetMousePos);
    connect(RightClickMenu,&QMenu::triggered,this,&CalibrationChart::onMenuAction);

    DefaultTaskList();
    ui->exeRun->setStyleSheet("background-color: rgb(255, 165, 95);");

    m_timerId = startTimer(8);





}

CalibrationChart::~CalibrationChart()
{
    delete ui;
    //delete subGraphRando;
    //delete subGraphRandoPoint;
    //delete wavePacketText;
    //delete subRectLeft;
    delete pf;

}

void CalibrationChart::DefaultTaskList()
{
    trackCmdList.allStepsNum = 0;
    trackCmdList.currentStep = 0;
    trackCmdList.settingDistance.clear();
    trackCmdList.gettingDistance.clear();


        lastOffset = ui->offset->text().toUInt();
        curItemModel->insertRow(0);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(50,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        uint settingDis = 50 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);


        curItemModel->insertRow(1);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(90,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 90 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);


        curItemModel->insertRow(2);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(180,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 180 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);


        curItemModel->insertRow(3);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(250,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 250 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);



        curItemModel->insertRow(4);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(300,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 300 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);



        curItemModel->insertRow(5);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(500,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 500 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis );
        trackCmdList.gettingDistance.push_back(0);


        curItemModel->insertRow(6);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(1000,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 1000 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis );
        trackCmdList.gettingDistance.push_back(0);



        curItemModel->insertRow(7);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(3000,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 3000 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis );
        trackCmdList.gettingDistance.push_back(0);


        curItemModel->insertRow(8);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(6000,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 6000 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis );
        trackCmdList.gettingDistance.push_back(0);


        /*curItemModel->insertRow(9);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(6000,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis = 6000 - ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis );
        trackCmdList.gettingDistance.push_back(0);*/
}

void CalibrationChart::timerEvent(QTimerEvent *event)
{
    Q_UNUSED(event)

    if(isExecuteStep == true) {
        if(trackCmdList.allStepsNum > 0 ) {
            if(isReached == true) {
                isSaveTrackFile = true;
                if(trackCnt >= 500) {
                    isSaveTrackFile = false;
                    trackCnt = 0;
                    isReached = false;

                    trackCmdList.currentStep++;
                    if(trackCmdList.currentStep == trackCmdList.allStepsNum) {
                        isExecuteStep = false;
                        trackFile.close();
                        qDebug() << "track finished!";
                        ui->exeRun->setStyleSheet("background-color: rgb(55, 125, 34);");
                        ui->exeRun->setText("Run");
                        return;
                    }
                    SendTrackCmd(trackCmdList.settingDistance.at(trackCmdList.currentStep));
                    qDebug() << "send: "<<trackCmdList.settingDistance.at(trackCmdList.currentStep)+ui->offset->text().toUInt() << " mm";
                }
            }
        }

    }
}

void CalibrationChart::FeedbackInfoLidar(QByteArray fdb)
{
    uint8_t *data = (uint8_t *)fdb.data();
    uint goal = 0;
    static float set_value = 0;
    /*将反馈的参数填写到参数框中*/
    if(fdb.size() < 3) {
        return;
    }
    else if(data[2] == 0xAA) {

        QString st;
        if(fdb.size() == (1*4 + 6)) {
            float temperature;
            memcpy(&temperature,&data[6],4);
//            set_value = temperature/18.0;
//            qDebug()<<set_value;
            tempOffset = ui->exeNum->text().toFloat();
            temperature += tempOffset;

            subGraphRando->addData(subGraphRando->dataCount(),temperature);
            subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRando->dataCount()*1.2);
            ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
        }
    }
//    else if(data[2] == 0xF1) {
//        QString st;

//        if(fdb.size() == (1*4 + 6)) {
//            float temperature;
//            memcpy(&temperature,&data[6],4);
//            tempOffset = ui->exeNum->text().toFloat();
//            temperature += tempOffset;

//            subGraphRando->addData(subGraphRando->dataCount(),set_value);
//            subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRando->dataCount()*1.2);

//            subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),temperature);
//            subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
//            ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);




//        }
//    }
    else if(data[2] == 0x20) {
        isReached = true;
        goal = data[6] | data[7]<<8  ;      
        qDebug() << "track arrive at goal: " << goal+50 << "mm  "<< fdb.toHex() ;
    }

}

void CalibrationChart::ReceiveLidarInfo(std::vector<std::vector<float>> data)
{
        //qDebug() << "getting...";
        uint size = data[0].size();
        if(ui->lineEdit_FILE_NAME->text() == "series") {
            for(uint m=0; m<size; m++) {
                subGraphRando->addData(subGraphRando->dataCount(),data[1][m]);

            }
        }

        if(ui->lineEdit_FILE_NAME->text() == "delta") {
            for(uint m=0; m<size; m++) {
                subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),data[0][m]);
            }
        }

        subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
        ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);

}

void CalibrationChart::receiveCalibrationData(QByteArray rec)
{
    if(rec.size() == 46 && rec.at(0) == 0x5A) {
        uint8_t *data = (uint8_t*)rec.data();
        float disVal=0;
        uint8_t confindenceVal=0;
        memcpy(&disVal,&data[39],4);
        confindenceVal  = data[43];
        if(disVal > 1) {
            ui->textEdit_TOF_PEAK->append("current Dis:  " + QString::number(disVal,'f',2)+"  mm  ,"\
                                          "confidence: "+QString::number(confindenceVal,10));
            if(ui->lineEdit_FILE_NAME->text() == "dis") {
                   subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),disVal);

                   subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
                   ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
               }
        }





        return;
    }

    if(rec.size() == 2) {
        uint8_t *data = (uint8_t*)rec.data();
        float disVal=0;
        //qDebug()<< data[0] << " " << data[1];
        int16_t realDis = (int16_t)(data[0] | data[1]<<8);
        disVal = realDis;
        ui->textEdit_TOF_PEAK->append("current Dis:  " + QString::number(disVal,'f',2)+"  mm  ,");
        if(ui->lineEdit_FILE_NAME->text() == "dis") {
               subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),disVal);

               subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
               ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
           }





        return;
    }

    if(rec.size() == 4) {
        uint8_t *data = (uint8_t*)rec.data();
        float disVal=0;
        //qDebug()<< data[0] << " " << data[1];
        uint16_t realDis = (uint16_t)(data[0] | data[1]<<8);
        uint16_t realPeak = (uint16_t)(data[2] | data[3]<<8);
        disVal = realDis;
        ui->textEdit_TOF_PEAK->append("current Dis:  " + QString::number(disVal,'f',2)+"  mm  ,"\
                                      "current Peak:  " + QString::number(realPeak,10) );
        if(ui->lineEdit_FILE_NAME->text() == "dis") {
               subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),disVal);

               subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
               ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
           }





        return;
    }

    //qDebug() << "rec: " << rec;
    uint8_t *data = (uint8_t*)rec.data();
    uint32_t tdc = data[3]<<24 | data[2]<<16 | data[1]<<8 | data[0];
    uint32_t peak = data[7]<<24 | data[6]<<16 | data[5]<<8 | data[4];
    uint32_t noise = 0,tdc2 = 0,peak2 = 0,noise2 = 0,refTof = 0;
    float value2 = 0,value3 = 0,fNoise = 0,fNoise2 = 0;

    if(rec.size() == 28) {
        noise = data[11]<<24 | data[10]<<16 | data[9]<<8 | data[8];
        memcpy(&fNoise,&noise,4);
        tdc2 = data[15]<<24 | data[14]<<16 | data[13]<<8 | data[12];
        memcpy(&value2,&tdc2,4);
        peak2 = data[19]<<24 | data[18]<<16 | data[17]<<8 | data[16];
        noise2 = data[23]<<24 | data[22]<<16 | data[21]<<8 | data[20];
        memcpy(&fNoise2,&noise2,4);
        refTof = data[27]<<24 | data[26]<<16 | data[25]<<8 | data[24];
        memcpy(&value3,&refTof,4);
    }

    float value = 0;
    memcpy(&value,&tdc,4);
    value *= 15.55f;
    refTof /= 64;
    refTof *= 15.55f;

    QDateTime curDateTime=QDateTime::currentDateTime();


    tofMeadm.push_back(value);
    PeakMeadm.push_back(peak);
    if(tofMeadm.size() >= 1000) {
        double mean = std::accumulate(tofMeadm.begin(), tofMeadm.end(), 0.0);
        double peakMean = std::accumulate(PeakMeadm.begin(), PeakMeadm.end(), 0.0);

        mean = mean/1000.0;
        peakMean = peakMean/1000.0;

        std::vector<float>::iterator k = tofMeadm.begin();
        tofMeadm.erase(k);

        std::vector<float>::iterator m = PeakMeadm.begin();
        PeakMeadm.erase(m);
    }

    if(ui->textEdit_TOF_PEAK->toPlainText().length() > 1024000) {
        ui->textEdit_TOF_PEAK->clear();
    }

    if(1) {
    ui->textEdit_TOF_PEAK->append(QString::number(value,'f',3)+"  "+QString::number(peak,10)+"  "+QString::number(fNoise,'f',2) + "    " +
                                    QString::number(value2,'f',3)+"  "+QString::number(peak2,10)+"  "+QString::number(fNoise2,'f',2) + "    " +
                                    QString::number(value3,'f',2)) ;
    }

   if(ui->lineEdit_FILE_NAME->text() == "tof") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),value);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }
   else if(ui->lineEdit_FILE_NAME->text() == "peak") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),peak);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }
   else if(ui->lineEdit_FILE_NAME->text() == "peak2") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),peak2);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }
   else if(ui->lineEdit_FILE_NAME->text() == "ref") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),value3);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }
   else if(ui->lineEdit_FILE_NAME->text() == "noise2") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),fNoise2);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }
   else if(ui->lineEdit_FILE_NAME->text() == "deta") {
       subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),value-value3);

       subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
       ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
   }

   if(isStartRecordFile == true) {
       QTextStream out(&file);					//分行写入文件

       if(rec.size() == 28) {
           out << value << " " << peak << " " << fNoise <<" " \
               << value2 <<" " << peak2 <<" " << fNoise2 << " " << value3<< endl;

       }
       else {
           out << value << " " << peak <<endl;
       }
   }

   if(isSaveTrackFile == true && trackCnt <= 499) {
       QTextStream fout(&trackFile);					//分行写入文件
       if(trackCnt == 0){
        QString currentDis = QString::number(trackCmdList.settingDistance.at(trackCmdList.currentStep)+ui->offset->text().toUInt(),10);
        fout << currentDis << " "<< currentDis<< " "<< currentDis<< " "
             << currentDis << " "<< currentDis<< " "<< currentDis<< " "<< currentDis<<endl;
       }
       else if(rec.size() == 28) {
           fout << value << " " << peak << " " << fNoise <<" " \
               << value2 <<" " << peak2 <<" " << fNoise2 << " " << value3<< endl;
       }
       else {
           fout << value << " " << peak <<endl;
       }
       trackCnt++;
   }

    /*解析数据*/
    if(isStartSample == true) {
        sampleCnt++;
        sampleTdc.push_back(value);
        samplePeak.push_back(peak);
    //    ui->progressBar->setValue(sampleCnt);
        if(sampleCnt == 1000) {
            float value=0;
            for(uint16_t n=0; n<sampleTdc.size(); n++) {
                value += sampleTdc.at(n);
            }
            value = value/sampleTdc.size();
            sampleAver.replace(sampleIndex,value);

            float valueP=0;
            for(uint16_t n=0; n<samplePeak.size(); n++) {
                valueP += samplePeak.at(n);
            }
            valueP = valueP/samplePeak.size();
            sampleAverPeak.replace(sampleIndex,valueP);

            /*显示值到表格中*/

            if(sampleIndex < 8) {
       //         ui->tableWidget->item(sampleIndex + 1,1)->setText(QString::number(value,'f',3));
       //         ui->tableWidget->item(sampleIndex + 1,2)->setText(QString::number(valueP,'f',3));
       //         ui->spinBox_SAMPLE_INDEX->setValue(sampleIndex+1);
            }
            //qDebug() << " sampleCnt " << sampleCnt;
            sampleCnt = 0;
            isStartSample = false;
        }
    }
}

void CalibrationChart::receiveHistDis(float dis)
{
   subGraphRandoPoint->addData(subGraphRandoPoint->dataCount(),dis);

   subRectLeft->axis(QCPAxis::atBottom)->setRange(0,subGraphRandoPoint->dataCount()*1.2);
   ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
}


void CalibrationChart::on_pushButton_SAVE_LOG_clicked()
{
    //将文本框数据取出并按行排列
    QString filepath = QFileDialog::getSaveFileName(this, tr("保存文件"),QString(), tr("Text files (*.txt);;HTML-Files (*.txt);;"));

    //QFile file(filepath);//文件命名
    file.setFileName(filepath);

    if (!file.open(QFile::WriteOnly | QFile::Text))		//检测文件是否打开
    {
        QMessageBox::information(this, "Error Message", "Please Select a Text File!");
        return;
    }
    //QTextStream out(&file);					//分行写入文件
    //out << ui->textEdit_TOF_PEAK->toPlainText();
    ui->pushButton_SAVE_LOG->setText("SaveLog...");
    isStartRecordFile = true;

}

void CalibrationChart::on_pushButton_CLEAR_LOG_clicked()
{
    ui->textEdit_TOF_PEAK->clear();
    isStartRecordFile = false;
    file.close();
    ui->pushButton_SAVE_LOG->setText("SaveLog");
    tofMeadm.clear();
    PeakMeadm.clear();
    sequenceCnt = 0;
    sequenceData.clear();
    subGraphRandoPoint->data()->set(sequenceData);

    subGraphRando->data()->set(sequenceData);
    ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);

}

void CalibrationChart::on_stopCMD_clicked()
{
    QByteArray data;
    uint8_t noneData = 0;
    data.push_back(0xA5);
    data.push_back(0x05);
    data.push_back(0x23);
    data.push_back(noneData);//XOR
    data.push_back(0x01);
    data.push_back(noneData);
    data.push_back(noneData);
    data.push_back(noneData);
    uint8_t checkNum=0;
    for(uint8_t m=0; m<8; m++) {
        checkNum ^= data.at(m);
    }
    data[3] = checkNum;
    emit TransmitCmd(data);
    trackFile.close();
    isExecuteStep = false;
    isReached = false;
    trackCmdList.currentStep = 0;
    trackCmdList.allStepsNum = 0;
    trackCmdList.settingDistance.clear();
    trackCmdList.gettingDistance.clear();

    DefaultTaskList();
    ui->exeRun->setStyleSheet("background-color: rgb(55, 125, 34);");
    ui->exeRun->setText("Run");
}

void CalibrationChart::on_resetCMD_clicked()
{
    QByteArray data;
    uint8_t noneData = 0;
    data.push_back(0xA5);
    data.push_back(0x05);
    data.push_back(0x22);
    data.push_back(noneData);//XOR
    data.push_back(0x01);
    data.push_back(noneData);
    data.push_back(noneData);
    data.push_back(noneData);
    uint8_t checkNum=0;
    for(uint8_t m=0; m<8; m++) {
        checkNum ^= data.at(m);
    }
    data[3] = checkNum;
    emit TransmitCmd(data);
}

void CalibrationChart::on_settingSpeed_returnPressed()
{
    QByteArray data;
    uint8_t noneData = 0;
    data.push_back(0xA5);
    data.push_back(0x05);
    data.push_back(0x21);
    data.push_back(noneData);//XOR
    data.push_back(0x01);
    data.push_back(noneData);

    uint16_t dis = ui->settingSpeed->text().toUInt();
    data.push_back(uint8_t(dis));
    data.push_back((dis>>8));

    uint8_t checkNum=0;
    for(uint8_t m=0; m<8; m++) {
        checkNum ^= data.at(m);
    }
    data[3] = checkNum;
    emit TransmitCmd(data);
    //isReached = true;
}

void CalibrationChart::on_RunDis_clicked()
{
    QByteArray data;
    uint8_t noneData = 0;
    data.push_back(0xA5);
    data.push_back(0x05);
    data.push_back(0x20);
    data.push_back(noneData);//XOR
    data.push_back(0x01);
    data.push_back(noneData);

    uint16_t dis = ui->settingDistance->text().toUInt();
    data.push_back(uint8_t(dis));
    data.push_back((dis>>8));

    uint8_t checkNum=0;
    for(uint8_t m=0; m<8; m++) {
        checkNum ^= data.at(m);
    }
    data[3] = checkNum;
    emit TransmitCmd(data);
}

void CalibrationChart::SendTrackCmd(uint16_t dat)
{
    QByteArray data;
    uint8_t noneData = 0;
    data.push_back(0xA5);
    data.push_back(0x05);
    data.push_back(0x20);
    data.push_back(noneData);//XOR
    data.push_back(0x01);
    data.push_back(noneData);

    data.push_back(uint8_t(dat));
    data.push_back((dat>>8));

    uint8_t checkNum=0;
    for(uint8_t m=0; m<8; m++) {
        checkNum ^= data.at(m);
    }
    data[3] = checkNum;
    emit TransmitCmd(data);
}

void CalibrationChart::on_addSamplePoint_clicked()
{
    uint32_t  settingDis = ui->samplePointDis->text().toUInt();
    if(settingDis >= 0 && settingDis <= 65535) {
        curItemModel->insertRow(totalRows);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(settingDis,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);
    }

}

void CalibrationChart::onGetMousePos(QPoint pos)
{
    QModelIndex index = ui->tableView->indexAt(pos);    //找到tableview当前位置信息
    seletMouseRow = index.row();    //获取到了当前右键所选的行数

    if(index.isValid())        //如果行数有效，则显示菜单
    {
        RightClickMenu->exec(QCursor::pos());
    }

}

void CalibrationChart::onMenuAction(QAction *act)
{
    //删除本行数据
    if(act->text() == "删除") {   //看选中了删除这个菜单

        //弹出提示框，看是否删除数据
        QMessageBox message(QMessageBox::NoIcon, "提示",
                                   "是否删除本行数据?",
        QMessageBox::Yes | QMessageBox::No, NULL);

        //如确认删除
        if(message.exec() == QMessageBox::Yes) {
            curItemModel->removeRow(seletMouseRow);  //删除掉了表格信息
            trackCmdList.settingDistance.remove(seletMouseRow);
            trackCmdList.gettingDistance.remove(seletMouseRow);
            trackCmdList.allStepsNum = trackCmdList.settingDistance.size();
            totalRows -= 1;
        }
    }
    else if(act->text() == "删除全部") {
        //弹出提示框，看是否删除数据
        QMessageBox message(QMessageBox::NoIcon, "提示",
                                   "是否删除全部数据?",
        QMessageBox::Yes | QMessageBox::No, NULL);

        //如确认删除
        if(message.exec() == QMessageBox::Yes) {

            //curItemModel->removeColumns(0,totalRows);

            for (int i = curItemModel->rowCount() - 1; i >= 0; --i)
            {
                curItemModel->removeRow(i);
            }
            trackCmdList.settingDistance.clear();
            trackCmdList.gettingDistance.clear();
            trackCmdList.allStepsNum = trackCmdList.settingDistance.size();

            totalRows = 0 ;
        }
    }
}

void CalibrationChart::on_exeRun_clicked()
{
    if(ui->saveFileName->text() == "") {
        QMessageBox message(QMessageBox::NoIcon, "提示",
                                   "输入文件名",
        QMessageBox::Yes | QMessageBox::No, NULL);
        message.exec();
        return;
    }
    if(trackCmdList.allStepsNum == 0) {
        QMessageBox message(QMessageBox::NoIcon, "提示",
                                   "没有执行指令",
        QMessageBox::Yes | QMessageBox::No, NULL);
        message.exec();
        return;
    }


    isExecuteStep = true;
    isReached = false;
    trackCmdList.currentStep = 0;
    SendTrackCmd(trackCmdList.settingDistance.at(0));
    ui->exeRun->setStyleSheet("background-color: rgb(255, 165, 95);");
    ui->exeRun->setText("Running...");
    qDebug() << "send: "<<trackCmdList.settingDistance.at(0) + ui->offset->text().toUInt()<< " mm";

    //QFile file(filepath);//文件命名
    //QDateTime curDateTime=QDateTime::currentDateTime();//= curDateTime.toString("yyyy-MM-dd-hh:mm:ss")+
    //QString currentDate_time = QDateTime::currentDateTime().toString("hh");

    QString fileName = ui->saveFileName->text()+".cspc";

    trackFile.setFileName(fileName);

    /*QFileInfo fileInfo(fileName);
    if(fileInfo.isFile()) {
        QMessageBox::information(this, "Error Message", "需要重新命名文件");
        return;
    }*/

    if (!trackFile.open(QFile::Append | QFile::WriteOnly| QFile::Text)) {		//检测文件是否打开

        QMessageBox::information(this, "Error Message", "Please Select a Text File!");

        return;
    }
    trackCnt = 0;

}

void CalibrationChart::on_samplePointDis_returnPressed()
{
    uint32_t  settingDis = ui->samplePointDis->text().toUInt();
    if(settingDis >= 0 && settingDis <= 6150 && ui->offset->text() != "") {

        curItemModel->insertRow(totalRows);
        curItemModel->setData(curItemModel->index(totalRows,0),QString::number(settingDis,10));
        curItemModel->item(totalRows,0)->setTextAlignment(Qt::AlignHCenter|Qt::AlignVCenter);
        totalRows++;
        trackCmdList.allStepsNum = totalRows;
        trackCmdList.currentStep = 0;
        settingDis -= ui->offset->text().toUInt();
        trackCmdList.settingDistance.push_back(settingDis);
        trackCmdList.gettingDistance.push_back(0);
    }
    else {
        QMessageBox message(QMessageBox::NoIcon, "提示",
                                   "enter data error or no offset!",
        QMessageBox::Yes | QMessageBox::No, NULL);
        message.exec();
    }
}

void CalibrationChart::on_offset_returnPressed()
{
    uint settingCnt = trackCmdList.settingDistance.size();
    for(uint m=0; m<settingCnt; m++){
        trackCmdList.settingDistance[m] += lastOffset;
        trackCmdList.settingDistance[m] -= ui->offset->text().toUInt();
    }
    lastOffset = ui->offset->text().toUInt();
}




void CalibrationChart::on_exeNum_returnPressed()
{
    calibrationParam.clear();
    QString strFile = QFileDialog::getOpenFileName(
                this,
                tr("打开文件"),
                tr(""),
                tr("Text Files(*.cspc);;All files(*)")
                );
    if( strFile.isEmpty()) {
        return;
    }
    QFile fileIn(strFile);
    if( ! fileIn.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, tr("打开文件"), tr("打开文件失败：") + fileIn.errorString());
        return;
    }

    QTextStream tsIn(&fileIn);



    /*test multiCalibration*/
    std::vector<std::vector<std::vector<float>>> dataHr,dataWt,dataBk;//10*7*500
    std::vector<float> dis;
    dataHr.resize(9);
    dataWt.resize(9);
    dataBk.resize(9);

    for(uint m=0; m<9; m++) {
        dataHr[m].resize(7);
        dataWt[m].resize(7);
        dataBk[m].resize(7);

    }

    dis.resize(9);
    dis[0] = 50;
    dis[1] = 90;
    dis[2] = 180;
    dis[3] = 250;
    dis[4] = 300;
    dis[5] = 500;
    dis[6] = 1000;
    dis[7] = 3000;
    dis[8] = 6000;
    //dis[9] = 6000;


    float tof1=0,peak1=0,noise1=0,ts=0,peak2=0,noise2=0,ref=0;
    uint paramCnt = 0;

    while(!tsIn.atEnd()) {
        tsIn>>tof1;
        tsIn>>peak1;
        tsIn>>noise1;
        tsIn>>ts;
        tsIn>>peak2;
        tsIn>>noise2;
        tsIn>>ref;
        paramCnt++;
        if(paramCnt <= 4500) {
            if(paramCnt <= 500) {
                dataHr[0][0].push_back(tof1);
                dataHr[0][1].push_back(peak1);
                dataHr[0][2].push_back(noise1);
                dataHr[0][3].push_back(ts);
                dataHr[0][4].push_back(peak2);
                dataHr[0][5].push_back(noise2);
                dataHr[0][6].push_back(ref);
            }
            else if(paramCnt <= 1000) {
                dataHr[1][0].push_back(tof1);
                dataHr[1][1].push_back(peak1);
                dataHr[1][2].push_back(noise1);
                dataHr[1][3].push_back(ts);
                dataHr[1][4].push_back(peak2);
                dataHr[1][5].push_back(noise2);
                dataHr[1][6].push_back(ref);
            }
            else if(paramCnt <= 1500) {
                dataHr[2][0].push_back(tof1);
                dataHr[2][1].push_back(peak1);
                dataHr[2][2].push_back(noise1);
                dataHr[2][3].push_back(ts);
                dataHr[2][4].push_back(peak2);
                dataHr[2][5].push_back(noise2);
                dataHr[2][6].push_back(ref);
            }
            else if(paramCnt <= 2000) {
                dataHr[3][0].push_back(tof1);
                dataHr[3][1].push_back(peak1);
                dataHr[3][2].push_back(noise1);
                dataHr[3][3].push_back(ts);
                dataHr[3][4].push_back(peak2);
                dataHr[3][5].push_back(noise2);
                dataHr[3][6].push_back(ref);
            }
            else if(paramCnt <= 2500) {
                dataHr[4][0].push_back(tof1);
                dataHr[4][1].push_back(peak1);
                dataHr[4][2].push_back(noise1);
                dataHr[4][3].push_back(ts);
                dataHr[4][4].push_back(peak2);
                dataHr[4][5].push_back(noise2);
                dataHr[4][6].push_back(ref);
            }
            else if(paramCnt <= 3000) {
                dataHr[5][0].push_back(tof1);
                dataHr[5][1].push_back(peak1);
                dataHr[5][2].push_back(noise1);
                dataHr[5][3].push_back(ts);
                dataHr[5][4].push_back(peak2);
                dataHr[5][5].push_back(noise2);
                dataHr[5][6].push_back(ref);
            }
            else if(paramCnt <= 3500) {
                dataHr[6][0].push_back(tof1);
                dataHr[6][1].push_back(peak1);
                dataHr[6][2].push_back(noise1);
                dataHr[6][3].push_back(ts);
                dataHr[6][4].push_back(peak2);
                dataHr[6][5].push_back(noise2);
                dataHr[6][6].push_back(ref);
            }
            else if(paramCnt <= 4000) {
                dataHr[7][0].push_back(tof1);
                dataHr[7][1].push_back(peak1);
                dataHr[7][2].push_back(noise1);
                dataHr[7][3].push_back(ts);
                dataHr[7][4].push_back(peak2);
                dataHr[7][5].push_back(noise2);
                dataHr[7][6].push_back(ref);
            }
            else if(paramCnt <= 4500) {
                dataHr[8][0].push_back(tof1);
                dataHr[8][1].push_back(peak1);
                dataHr[8][2].push_back(noise1);
                dataHr[8][3].push_back(ts);
                dataHr[8][4].push_back(peak2);
                dataHr[8][5].push_back(noise2);
                dataHr[8][6].push_back(ref);
            }
            /*else if(paramCnt <= 5000) {
                dataHr[9][0].push_back(tof1);
                dataHr[9][1].push_back(peak1);
                dataHr[9][2].push_back(noise1);
                dataHr[9][3].push_back(ts);
                dataHr[9][4].push_back(peak2);
                dataHr[9][5].push_back(noise2);
                dataHr[9][6].push_back(ref);
            }*/
        }
        else if(paramCnt <= 9000) {
            if(paramCnt <= 4500+500) {
                dataWt[0][0].push_back(tof1);
                dataWt[0][1].push_back(peak1);
                dataWt[0][2].push_back(noise1);
                dataWt[0][3].push_back(ts);
                dataWt[0][4].push_back(peak2);
                dataWt[0][5].push_back(noise2);
                dataWt[0][6].push_back(ref);
            }
            else if(paramCnt <= 4500+1000) {
                dataWt[1][0].push_back(tof1);
                dataWt[1][1].push_back(peak1);
                dataWt[1][2].push_back(noise1);
                dataWt[1][3].push_back(ts);
                dataWt[1][4].push_back(peak2);
                dataWt[1][5].push_back(noise2);
                dataWt[1][6].push_back(ref);
            }
            else if(paramCnt <= 4500+1500) {
                dataWt[2][0].push_back(tof1);
                dataWt[2][1].push_back(peak1);
                dataWt[2][2].push_back(noise1);
                dataWt[2][3].push_back(ts);
                dataWt[2][4].push_back(peak2);
                dataWt[2][5].push_back(noise2);
                dataWt[2][6].push_back(ref);
            }
            else if(paramCnt <= 4500+2000) {
                dataWt[3][0].push_back(tof1);
                dataWt[3][1].push_back(peak1);
                dataWt[3][2].push_back(noise1);
                dataWt[3][3].push_back(ts);
                dataWt[3][4].push_back(peak2);
                dataWt[3][5].push_back(noise2);
                dataWt[3][6].push_back(ref);
            }
            else if(paramCnt <= 4500+2500) {
                dataWt[4][0].push_back(tof1);
                dataWt[4][1].push_back(peak1);
                dataWt[4][2].push_back(noise1);
                dataWt[4][3].push_back(ts);
                dataWt[4][4].push_back(peak2);
                dataWt[4][5].push_back(noise2);
                dataWt[4][6].push_back(ref);
            }
            else if(paramCnt <= 4500+3000) {
                dataWt[5][0].push_back(tof1);
                dataWt[5][1].push_back(peak1);
                dataWt[5][2].push_back(noise1);
                dataWt[5][3].push_back(ts);
                dataWt[5][4].push_back(peak2);
                dataWt[5][5].push_back(noise2);
                dataWt[5][6].push_back(ref);
            }
            else if(paramCnt <= 4500+3500) {
                dataWt[6][0].push_back(tof1);
                dataWt[6][1].push_back(peak1);
                dataWt[6][2].push_back(noise1);
                dataWt[6][3].push_back(ts);
                dataWt[6][4].push_back(peak2);
                dataWt[6][5].push_back(noise2);
                dataWt[6][6].push_back(ref);
            }
            else if(paramCnt <= 4500+4000) {
                dataWt[7][0].push_back(tof1);
                dataWt[7][1].push_back(peak1);
                dataWt[7][2].push_back(noise1);
                dataWt[7][3].push_back(ts);
                dataWt[7][4].push_back(peak2);
                dataWt[7][5].push_back(noise2);
                dataWt[7][6].push_back(ref);
            }
            else if(paramCnt <= 4500+4500) {
                dataWt[8][0].push_back(tof1);
                dataWt[8][1].push_back(peak1);
                dataWt[8][2].push_back(noise1);
                dataWt[8][3].push_back(ts);
                dataWt[8][4].push_back(peak2);
                dataWt[8][5].push_back(noise2);
                dataWt[8][6].push_back(ref);
            }
            /*else if(paramCnt <= 5000+5000) {
                dataWt[9][0].push_back(tof1);
                dataWt[9][1].push_back(peak1);
                dataWt[9][2].push_back(noise1);
                dataWt[9][3].push_back(ts);
                dataWt[9][4].push_back(peak2);
                dataWt[9][5].push_back(noise2);
                dataWt[9][6].push_back(ref);
            }*/
        }
        else if(paramCnt <= 13500) {
            if(paramCnt <= 9000+500) {
                dataBk[0][0].push_back(tof1);
                dataBk[0][1].push_back(peak1);
                dataBk[0][2].push_back(noise1);
                dataBk[0][3].push_back(ts);
                dataBk[0][4].push_back(peak2);
                dataBk[0][5].push_back(noise2);
                dataBk[0][6].push_back(ref);
            }
            else if(paramCnt <= 9000+1000) {
                dataBk[1][0].push_back(tof1);
                dataBk[1][1].push_back(peak1);
                dataBk[1][2].push_back(noise1);
                dataBk[1][3].push_back(ts);
                dataBk[1][4].push_back(peak2);
                dataBk[1][5].push_back(noise2);
                dataBk[1][6].push_back(ref);
            }
            else if(paramCnt <= 9000+1500) {
                dataBk[2][0].push_back(tof1);
                dataBk[2][1].push_back(peak1);
                dataBk[2][2].push_back(noise1);
                dataBk[2][3].push_back(ts);
                dataBk[2][4].push_back(peak2);
                dataBk[2][5].push_back(noise2);
                dataBk[2][6].push_back(ref);
            }
            else if(paramCnt <= 9000+2000) {
                dataBk[3][0].push_back(tof1);
                dataBk[3][1].push_back(peak1);
                dataBk[3][2].push_back(noise1);
                dataBk[3][3].push_back(ts);
                dataBk[3][4].push_back(peak2);
                dataBk[3][5].push_back(noise2);
                dataBk[3][6].push_back(ref);
            }
            else if(paramCnt <= 9000+2500) {
                dataBk[4][0].push_back(tof1);
                dataBk[4][1].push_back(peak1);
                dataBk[4][2].push_back(noise1);
                dataBk[4][3].push_back(ts);
                dataBk[4][4].push_back(peak2);
                dataBk[4][5].push_back(noise2);
                dataBk[4][6].push_back(ref);
            }
            else if(paramCnt <= 9000+3000) {
                dataBk[5][0].push_back(tof1);
                dataBk[5][1].push_back(peak1);
                dataBk[5][2].push_back(noise1);
                dataBk[5][3].push_back(ts);
                dataBk[5][4].push_back(peak2);
                dataBk[5][5].push_back(noise2);
                dataBk[5][6].push_back(ref);
            }
            else if(paramCnt <= 9000+3500) {
                dataBk[6][0].push_back(tof1);
                dataBk[6][1].push_back(peak1);
                dataBk[6][2].push_back(noise1);
                dataBk[6][3].push_back(ts);
                dataBk[6][4].push_back(peak2);
                dataBk[6][5].push_back(noise2);
                dataBk[6][6].push_back(ref);
            }
            else if(paramCnt <= 9000+4000) {
                dataBk[7][0].push_back(tof1);
                dataBk[7][1].push_back(peak1);
                dataBk[7][2].push_back(noise1);
                dataBk[7][3].push_back(ts);
                dataBk[7][4].push_back(peak2);
                dataBk[7][5].push_back(noise2);
                dataBk[7][6].push_back(ref);
            }
            else if(paramCnt <= 9000+4500) {
                dataBk[8][0].push_back(tof1);
                dataBk[8][1].push_back(peak1);
                dataBk[8][2].push_back(noise1);
                dataBk[8][3].push_back(ts);
                dataBk[8][4].push_back(peak2);
                dataBk[8][5].push_back(noise2);
                dataBk[8][6].push_back(ref);
            }
            /*else if(paramCnt <= 9000+5000) {
                dataBk[9][0].push_back(tof1);
                dataBk[9][1].push_back(peak1);
                dataBk[9][2].push_back(noise1);
                dataBk[9][3].push_back(ts);
                dataBk[9][4].push_back(peak2);
                dataBk[9][5].push_back(noise2);
                dataBk[9][6].push_back(ref);
            }*/
        }

    }
   fileIn.close();
   if(paramCnt != 13500+1) {
       QMessageBox::warning(this, tr("文件大小"), tr("文件大小不匹配："));
       return;
   }


   if(multiCalibrationProcess(dataHr,dataWt,dataBk,dis,0,&calibrationParam) == -2) {
       QMessageBox::warning(this, tr("peak error"), tr("装调不合格"));
       return;
   }
   emit TransmitCalibrationData(calibrationParam);
}

void CalibrationChart::on_saveFileName_returnPressed()
{
    isReached = true;
    std::cout<<"execute manual in placed!"<<std::endl;
}

void CalibrationChart::on_settingDistance_returnPressed()
{
    QByteArray data;
    uint8_t noneData = 0x00;
    data.push_back(0x5A);
    data.push_back(0x34);
    data.push_back(0x01);
    data.push_back(0X02);//XOR
    data.push_back(noneData);
    data.push_back(noneData);
    data.push_back(0X01);
    data.push_back(0XC7);
    qDebug()<<"5300 sennd";

    emit TransmitTestCmd(data);
}

void CalibrationChart::on_lineEdit_FILE_NAME_returnPressed()
{
    if(ui->lineEdit_FILE_NAME->text() == "delta") {
        subRectLeft->axis(QCPAxis::atLeft)->setRange(0,2);
    }
    else if(ui->lineEdit_FILE_NAME->text() == "series") {
        subRectLeft->axis(QCPAxis::atLeft)->setRange(0,360);
    }
    ui->widgetCalibration->replot(QCustomPlot::rpQueuedReplot);
}
