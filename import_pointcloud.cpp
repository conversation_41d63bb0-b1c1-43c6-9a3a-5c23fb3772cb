#include "import_pointcloud.h"
#include "ui_importpointcloud.h"
#include <QFile>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QDebug>
#include <QTime>

ImportPointCloud::ImportPointCloud(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ImportPointCloud),
    isStartEmitData(false),
    timeInterval(100),
    curDataCnt(0),
    totalDataCnt(0)
{
    ui->setupUi(this);
    time = new QTimer(this);
    connect(time,SIGNAL(timeout()),this,SLOT(TimeOut()));                         //定时发送函数
    time->start(timeInterval);
}

ImportPointCloud::~ImportPointCloud()
{
    delete ui;
}

void ImportPointCloud::on_pushButtonImportPointCloud_clicked()
{
    QString strFile = QFileDialog::getOpenFileName(
                this,
                tr("打开文件"),
                tr(""),
                tr("Text Files(*.cspc);;All files(*)")
                );
    if( strFile.isEmpty()) {
        return;
    }
    QFile fileIn(strFile);
    if( ! fileIn.open(QIODevice::ReadOnly)) {
        QMessageBox::warning(this, tr("打开文件"), tr("打开文件失败：") + fileIn.errorString());
        return;
    }
    ui->lineEditFilePath->setText(strFile);

    uint32_t fileSize = fileIn.size();
    qDebug() << "file size: "<<fileSize;

    QTextStream tsIn(&fileIn);
    QString strCurName;

    uint size=0,tmpValue=0;
    totalDataCnt = 0;
    float spd=0;
    replayData.clear();//lot of data which is per circle
    ProtocolData tmpPtDaPer;//data of per circl with all infomation
    PolarData tmpScan;//data of per circl
    uint16_t HrFlag=0;

    while( ! tsIn.atEnd() )
    {
        tsIn>>strCurName;
        if(strCurName.startsWith('#'))
        {
            tsIn>>size;
            tsIn>>spd;
            tmpPtDaPer.speed = spd;
            tsIn>>tmpValue;
            tmpPtDaPer.version = tmpValue;
            tsIn>>tmpValue;
            tmpPtDaPer.healthCode = tmpValue;
            tsIn>>tmpValue;
            tmpPtDaPer.mcuVoltage = tmpValue;

            for(uint i=0; i<size; i++)
            {
                tsIn>>tmpScan.angle;
                tsIn>>tmpScan.deepth;
                tsIn>>tmpScan.indensity;
                tsIn>>HrFlag;
                tmpScan.HrDenote = HrFlag;
                tmpPtDaPer.data.push_back(tmpScan);
//                qDebug()<<tmpScan.angle<<" "<<tmpScan.deepth;
            }
            replayData.push_back(tmpPtDaPer);
            tmpPtDaPer.data.clear();

        }

    }
    totalDataCnt = replayData.size();
    ui->lineEditFilePath->setText(strFile + " " + QString::number(totalDataCnt,10));
    fileIn.close();
    if(totalDataCnt > 0) {
        //isStartEmitData = true;
        curDataCnt = 0;
        ui->progressBar->setValue(0);
    }
}


void ImportPointCloud::TimeOut()
{
    static QTime lastTimeMs1;
    if(isStartEmitData == true) {
        if(curDataCnt < totalDataCnt) {
            std::vector<ProtocolData> tmpData;
            tmpData.push_back(replayData.at(curDataCnt));
            ui->progressBar->setValue(int(100*(curDataCnt+1)/totalDataCnt));
            emit transmitPointCloudData(tmpData);
            curDataCnt++;
        }
        else {
            curDataCnt = false;
            qDebug() << "running is finished";
        }
    }
    /*QTime startTime = QTime::currentTime();
    qDebug() << "interval time... " << lastTimeMs1.msecsTo(startTime);
    lastTimeMs1 = startTime;*/
}


void ImportPointCloud::on_pushButtonStart_clicked()
{
    isStartEmitData = true;
}

void ImportPointCloud::on_pushButtonStop_clicked()
{
    isStartEmitData = false;//transmitShowFileDataCmd
}

void ImportPointCloud::on_radioButtonFileData_clicked()
{
    bool isTrue = ui->radioButtonFileData->isChecked();
    isStartEmitData = false;
    emit transmitShowFileDataCmd(isTrue);
}

void ImportPointCloud::on_horizontalSliderSetSpeed_sliderMoved(int position)
{
    int sliderVal = position;

    if(sliderVal == 0) {
       time->stop();
       timeInterval = 0;
    }
    else {
       timeInterval = 1000/sliderVal;
       time->start(timeInterval);
    }
    qDebug() << "setting interval: " <<position<< " "<<timeInterval;

}
