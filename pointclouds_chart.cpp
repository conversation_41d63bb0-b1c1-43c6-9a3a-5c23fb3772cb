#include "pointclouds_chart.h"
#include "ui_pointcloudschart.h"
#include <Eigen/Cholesky>
#include <Eigen/Core>
#include <Eigen/Dense>
#include <QSettings>
#include <QStringList>


using namespace std;
bool isSamplePeak2 = false;

///
/// \brief sleep_msec
/// \param msec
/// \note 这种方法不会阻塞当前线程,尤其适合Qt单线程带UI程序
void sleep_msec(int msec) {
    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while (QTime::currentTime() < dieTime) {
        //强制进入当前线程的事件循环,这样可以把堵塞的事件都处理掉,从而避免程序卡死。
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
        //如果去掉QCoreApplication::processEvents; 可以延时,但会阻塞线程
    }
}

PointCloudsChart::PointCloudsChart(QWidget *parent)
    : QMainWindow(parent),
      ui(new Ui::PointCloudsChart),
      isStartRecord(false),
      isShowFileData(false),
      isRecordPoint(false),
      recordFrameCnt(0),
      curStatisticsCnt(0),
      widgetRate(1.0),
      currentRaduis(kMaxRadius),
      pickPointAngle(0.0),
      isExeCompensation(false),
      isExeSample(false),
      isExeCompare(false),
      isExeBaseAngle(false),
      samplePt(0.0),
      degree_factor(1.0) {
    ui->setupUi(this);
    customPlot = ui->customPlot;
    GuiSetup();
#if (USING_CUSTOM_STYLE == 1)
    // ui->actionRecord->setEnabled(false);
    ui->actionImportCloud->setEnabled(false);
    ui->actionRecordCalibration->setEnabled(false);
#endif
}

void PointCloudsChart::GuiSetup() {
    // 1
    customPlot->setLocale(QLocale(
        QLocale::English, QLocale::UnitedKingdom));  //句点作为十进制分隔符，逗号作为千分隔符 period as decimal separator and comma as thousand separator
    customPlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables);
    customPlot->axisRect()->setRangeZoomFactor(0.5);

    // 2 create graph and assign data to it:
    customPlot->addGraph();
    customPlot->graph(0)->setPen(QPen(Qt::white));  // line color blue for first graph
    customPlot->graph(0)->setLineStyle(QCPGraph::lsNone);
    customPlot->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssDisc, 1.9));
    customPlot->setBackground(QBrush(QColor(44, 62, 80)));

    customPlot->addGraph();
    customPlot->graph(1)->setPen(QPen(Qt::red));  // line color blue for first graph
    customPlot->graph(1)->setLineStyle(QCPGraph::lsNone);
    customPlot->graph(1)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssDisc, 1.9));


    customPlot->xAxis->setTicks(false);
    customPlot->yAxis->setTicks(false);
    customPlot->xAxis->setVisible(false);
    customPlot->yAxis->setVisible(false);
    // 3 set margins to zero
    customPlot->axisRect()->setAutoMargins(QCP::msNone);
    customPlot->axisRect()->setMargins(QMargins(0, 0, 0, 0));

    // 4 set axes ranges, so we see all data:
    /*double whRate = 1.0;
    if(whRate < 1.0) {//w > h
      customPlot->xAxis->setRange(-kMaxRadius/whRate, kMaxRadius/whRate);
      customPlot->yAxis->setRange(-kMaxRadius, kMaxRadius);
    }
    else{//w <= h
      customPlot->xAxis->setRange(-kMaxRadius, kMaxRadius);
      customPlot->yAxis->setRange(-kMaxRadius*whRate, kMaxRadius*whRate);
    }*/

    // 5 radius angle labels
    ellipseFactor = {
        {0, 1},
        {1, 0.8},
        {2, 0.6},
        {3, 0.4},
        {4, 0.2},
    };
    for (uint8_t m = 0; m < kEllipseCnt; m++) {
        QCPItemEllipse *m_ellipse = new QCPItemEllipse(customPlot);
        m_ellipse->topLeft->setCoords(kMaxRadius * ellipseFactor.at(m), -kMaxRadius * ellipseFactor.at(m));
        m_ellipse->bottomRight->setCoords(-kMaxRadius * ellipseFactor.at(m), kMaxRadius * ellipseFactor.at(m));
        m_ellipse->setPen(QPen(QColor(40, 175, 100)));
        qcpItemEll.push_back(m_ellipse);

        QCPItemText *label = new QCPItemText(customPlot);
        label->setPositionAlignment(Qt::AlignBottom | Qt::AlignLeft);
        label->position->setType(QCPItemPosition::ptPlotCoords);
        label->position->setCoords(kMaxRadius * ellipseFactor.at(m) + 1, -0.1);
        label->setRotation(-20);
        label->setText(QString::asprintf("%d", int(kMaxRadius * ellipseFactor.at(m))));
        label->setFont(QFont(font().family(), 8));
        label->setColor(QColor(255, 128, 0));
        qcpText.push_back(label);
    }

    QCPItemLine *arrowH = new QCPItemLine(customPlot);
    arrowH->setPen(QPen(QColor(40, 175, 100)));
    arrowH->start->setCoords(-kMaxRadius * 1.03, 0);
    arrowH->end->setCoords(kMaxRadius * 1.05, 0);
    arrowH->setHead(QCPLineEnding::esSpikeArrow);
    qcpLine.push_back(arrowH);

    QCPItemLine *arrowV = new QCPItemLine(customPlot);
    arrowV->setPen(QPen(QColor(40, 175, 100)));  // 255,255,0
    arrowV->start->setCoords(0, kMaxRadius * 1.03);
    arrowV->end->setCoords(0, -(kMaxRadius * 1.03));
    arrowV->setHead(QCPLineEnding::esNone);
    qcpLine.push_back(arrowV);

    pickLine = new QCPItemLine(customPlot);
    pickLine->setPen(QPen(QColor(255, 128, 0, 150)));  // 255,255,0
    pickLine->start->setCoords(0, 0);
    pickLine->end->setCoords(0, 0);
    pickLine->setHead(QCPLineEnding::esNone);


    // 6 lidar infomation  text
    version = new QCPItemText(customPlot);
    version->setPositionAlignment(Qt::AlignVCenter);
    version->position->setType(QCPItemPosition::ptAxisRectRatio);
    version->position->setCoords(0.82, 0.9);
    version->setText(QString::asprintf("version: %d", 1));
    version->setFont(QFont("仿宋", 10, QFont::Bold));
    version->setColor(QColor(255, 128, 0));

    speed = new QCPItemText(customPlot);
    speed->setPositionAlignment(Qt::AlignVCenter);
    speed->position->setType(QCPItemPosition::ptAxisRectRatio);
    speed->position->setCoords(0.82, 0.05);
    speed->setText(QString::asprintf("speed: %.1f Hz", 10.0));
    speed->setFont(QFont("仿宋", 10, QFont::Bold));
    speed->setColor(QColor(255, 128, 0));

    pick = new QCPItemText(customPlot);
    pick->setPositionAlignment(Qt::AlignVCenter);
    pick->position->setType(QCPItemPosition::ptAxisRectRatio);
    pick->position->setCoords(0.005, 0.05);
    pick->setText(QString::asprintf("curNum:%d curAngle:%.2f curDis:%d curIntensity:%d", 0, 0.0, 0, 0));
    pick->setFont(QFont("仿宋", 10, QFont::Bold));
    pick->setColor(QColor(255, 128, 0));

    mean = new QCPItemText(customPlot);
    mean->setPositionAlignment(Qt::AlignVCenter);
    mean->position->setType(QCPItemPosition::ptAxisRectRatio);
    mean->position->setCoords(0.005, 0.1);
    mean->setText(QString::asprintf("maxDis:%d minDis:%d meanDis:%d std:%.2f", 0, 0, 0, 0.0f));
    mean->setFont(QFont("仿宋", 10, QFont::Bold));
    mean->setColor(QColor(255, 128, 0));

    others = new QCPItemText(customPlot);
    others->setPositionAlignment(Qt::AlignVCenter);
    others->position->setType(QCPItemPosition::ptAxisRectRatio);
    others->position->setCoords(0.005, 0.15);
    others->setText(QString::asprintf("DDS - vol:%.2f code:%d H", 3.3f, 0));
    others->setFont(QFont("仿宋", 10, QFont::Bold));
    others->setColor(QColor(255, 128, 0));

    qcpLidarInfoText = {
        {"version", version},
        {"speed", speed},
        {"pick", pick},
        {"mean", mean},
        {"others", others},
    };

    connect(customPlot->yAxis, SIGNAL(rangeChanged(QCPRange)), this, SLOT(SetRange(QCPRange)));
    connect(customPlot, &QCustomPlot::mousePress, this, &PointCloudsChart::PickPointCallBack);

    customPlot->show();

    QSettings setting("settings.ini", QSettings::IniFormat);
    //读取ini文件内容
    QString q_enable          = setting.value("ENABLE/integration_enable").toString();
    bool    ok                = false;
    uint8_t enable            = q_enable.toInt(&ok, 16);
    is_integration_pointcloud = enable == 1 ? true : false;


    m_timerId = startTimer(8);
}

PointCloudsChart::~PointCloudsChart() {
    delete ui;
    if (customPlot != nullptr) {
        delete customPlot;
    }
    if (saveAsCapturePtr != nullptr) {
        delete saveAsCapturePtr;
    }
    if (importPointCloudPtr != nullptr) {
        delete importPointCloudPtr;
    }
    if (recordPointCloud != nullptr) {
        delete recordPointCloud;
    }
    if (dynamicCalibration != nullptr) {
        delete dynamicCalibration;
    }
    if (pointCloudQualityPtr != nullptr) {
        delete pointCloudQualityPtr;
    }
    qcpItemEll.clear();
    qcpText.clear();
    qcpLine.clear();
    delete pickLine;
    delete version;
    delete speed;
    delete pick;
}

void PointCloudsChart::timerEvent(QTimerEvent *event) {
    Q_UNUSED(event)
}


void PointCloudsChart::resizeEvent(QResizeEvent *event) {
    if (event->type() == QEvent::Resize) {
        static bool isFirstResize = true;
        int         w = 0, h = 0;
        if (isFirstResize == true) {
            isFirstResize = false;
            w             = customPlot->window()->width() - kActionHeight;
            h             = customPlot->window()->height();
        } else {
            w = customPlot->width();
            h = customPlot->height();
        }
        double whRate = double(h) / (w);
        widgetRate    = whRate;

        if (whRate < 1.0) {  // w > h
            customPlot->xAxis->setRange(-currentRaduis / whRate, currentRaduis / whRate);
            customPlot->yAxis->setRange(-currentRaduis, currentRaduis);
        } else {  // w <= h
            customPlot->xAxis->setRange(-currentRaduis, currentRaduis);
            customPlot->yAxis->setRange(-currentRaduis * whRate, currentRaduis * whRate);
        }
        customPlot->replot();
    }
}

void PointCloudsChart::SetAdminFuction(bool isEnable) {
    if (isEnable == true) {
        ui->actionRecord->setEnabled(true);
        ui->actionImportCloud->setEnabled(true);
        ui->actionRecordCalibration->setEnabled(true);
    }
}

void PointCloudsChart::SetRange(QCPRange range) {
    static double yLower = -kMaxRadius, yUpper = 0, xLower = -kMaxRadius / widgetRate, xUpper = 0;
    int           rangeSize = (int)range.size();
    // qDebug() << rangeSize << range.lower << " " <<range.upper;
    if (abs(rangeSize - 64000) < 10) {
        int w = customPlot->width();
        int h = customPlot->height();

        double whRate = double(h) / (w);


        if (whRate < 1.0) {  // w > h
            xUpper = xLower + 32000 / whRate;
            yUpper = yLower + 32000;
            customPlot->xAxis->setRange(xLower, xUpper);
            customPlot->yAxis->setRange(yLower, yUpper);
        } else {  // w <= h
            yUpper = yLower + 32000 * whRate;
            xUpper = xLower + 32000;
            customPlot->xAxis->setRange(xLower, xUpper);
            customPlot->yAxis->setRange(yLower, yUpper);
        }
        currentRaduis = 16000;
    } else if (abs(rangeSize - 250) < 10) {
        int w = customPlot->width();
        int h = customPlot->height();

        double whRate = double(h) / (w);

        if (whRate < 1.0) {  // w > h
            xUpper = xLower + 500 / whRate;
            yUpper = yLower + 500;
            customPlot->xAxis->setRange(xLower, xUpper);
            customPlot->yAxis->setRange(yLower, yUpper);
        } else {  // w <= h
            yUpper = yLower + 500 * whRate;
            xUpper = xLower + 500;
            customPlot->xAxis->setRange(xLower, xUpper);
            customPlot->yAxis->setRange(yLower, yUpper);
        }
        currentRaduis = 250;
    } else {
        yLower = range.lower;
        xLower = customPlot->xAxis->range().lower;

        int    w        = customPlot->width();
        int    h        = customPlot->height();
        double whRate   = double(h) / (w);
        int    diameter = 0;
        if (whRate < 1.0) {  // w > h
            diameter = customPlot->yAxis->range().size();
        } else {  // w <= h
            diameter = customPlot->xAxis->range().size();
        }

        uint16_t raduis = 0;
        if (abs(diameter - kRange0) < 100) {  // radius = 16000
            raduis = kRange1;
        } else if (abs(diameter - kRange1) < 100) {  // radius = 8000
            raduis = kRange2;
        } else if (abs(diameter - kRange2) < 100) {  // radius = 4000
            raduis = kRange3;
        } else if (abs(diameter - kRange3) < 100) {  // radius = 2000
            raduis = kRange4;
        } else if (abs(diameter - kRange4) < 100) {  // radius = 1000
            raduis = kRange5;
        } else if (abs(diameter - kRange5) < 20) {  // radius = 500
            raduis = kRange6;
        } else if (abs(diameter - kRange6) < 20) {  // radius = 250
            raduis = kRange7;
        }

        for (uint8_t m = 0; m < kEllipseCnt; m++) {

            if (qcpItemEll.size() == kEllipseCnt && qcpText.size() == kEllipseCnt) {
                qcpItemEll.at(m)->topLeft->setCoords(raduis * ellipseFactor.at(m), -raduis * ellipseFactor.at(m));
                qcpItemEll.at(m)->bottomRight->setCoords(-raduis * ellipseFactor.at(m), raduis * ellipseFactor.at(m));
                qcpItemEll.at(m)->setPen(QPen(QColor(40, 175, 100)));

                qcpText.at(m)->setPositionAlignment(Qt::AlignBottom | Qt::AlignLeft);
                qcpText.at(m)->position->setType(QCPItemPosition::ptPlotCoords);
                qcpText.at(m)->position->setCoords(raduis * ellipseFactor.at(m) + 1, -0.1);
                qcpText.at(m)->setRotation(-20);
                qcpText.at(m)->setText(QString::asprintf("%d", int(raduis * ellipseFactor.at(m))));
                qcpText.at(m)->setFont(QFont(font().family(), 8));
                qcpText.at(m)->setColor(QColor(255, 128, 0));
            }
            currentRaduis = raduis;
        }

        if (qcpLine.size() == 2) {
            qcpLine.at(0)->setPen(QPen(QColor(40, 175, 100)));
            qcpLine.at(0)->start->setCoords(-raduis * 1.03, 0);
            qcpLine.at(0)->end->setCoords(raduis * 1.05, 0);
            qcpLine.at(0)->setHead(QCPLineEnding::esSpikeArrow);


            qcpLine.at(1)->setPen(QPen(QColor(40, 175, 100)));
            qcpLine.at(1)->start->setCoords(0, raduis * 1.03);
            qcpLine.at(1)->end->setCoords(0, -(raduis * 1.03));
            qcpLine.at(1)->setHead(QCPLineEnding::esNone);
        }
    }
}

void PointCloudsChart::PickPointCallBack(QMouseEvent *event) {
    static QTime lastTimeMs;
    QTime        startTime = QTime::currentTime();
    int64_t      detaTime  = lastTimeMs.msecsTo(startTime);
    lastTimeMs             = startTime;
    double x               = customPlot->xAxis->pixelToCoord(event->pos().x());
    double y               = customPlot->yAxis->pixelToCoord(event->pos().y());

    pickPointAngle = -qRadiansToDegrees(qAtan2(y, x));
    if (pickPointAngle > 360.0) {
        pickPointAngle -= 360.0;
    } else if (pickPointAngle < 0.0) {
        pickPointAngle += 360.0;
    }
    curStatisticsCnt = 0;
    if (detaTime < 500) {

    } else {
        if (isShowFileData == false) {
            ReceivePointCloudData(lastData);
        } else {
            ReceiveFileData(lastData);
        }
    }
}
#include "sys/time.h"

int PointCloudsChart::samsung_calc_bound(std::vector<float> in_angle, std::vector<float> in_dis, std::vector<float> in_intensity, std::vector<float> *param) {
    if (in_angle.size() != in_dis.size() || in_dis.size() != in_intensity.size()) {
        return -1;
    }
    uint buff_index_offset = 0;
    for (uint n = 0; n < in_angle.size(); n++) {
        if (in_angle[n] > 39 && in_angle[n] < 41) {
            buff_index_offset = n;
        }
    }
    std::vector<float> angle, dis, intensity;
    for (uint nn = buff_index_offset; nn < in_angle.size() + buff_index_offset; nn++) {
        uint index = nn;
        if (index > in_angle.size() - 1) {
            index = index - in_angle.size();
        }
        angle.push_back(in_angle[index]);
        dis.push_back(in_dis[index]);
        intensity.push_back(in_intensity[index]);
    }


    std::vector<float> data_diference_value;
    const int          bound_threshold   = 180;
    const int          initial_threshold = 40;
    std::vector<float> bound_index;
    std::vector<float> bound_value;
    int                is_neg = 0;
    int                is_pos = 0;
    std::vector<float> initial_index;
    std::vector<float> initial_value;
    int                is_initial_neg = 0;
    int                is_initial_pos = 0;
    int                diff_value     = 0;

    for (uint m = 0; m < dis.size(); m += 2) {
        if (m + 2 > (dis.size() - 1)) {
            if (m + 2 == dis.size()) {  // m begin from 0
                diff_value = dis[0] - dis[m];
            } else if (m + 2 == dis.size() + 1) {
                diff_value = dis[1] - dis[m];
            }
            //           else if (m+2 == dis.size()+1) {
            //                diff_value = dis[1] - dis[m];
            //            }

            // check bound
            if (diff_value < -bound_threshold && is_neg == 0) {
                bound_index.push_back(m);
                is_neg = 1;
                is_pos = 0;
            } else if (diff_value > bound_threshold && is_pos == 0) {
                bound_index.push_back(m + 2);
                is_neg = 0;
                is_pos = 1;
            }
            data_diference_value.push_back(diff_value);

            int bound_size = bound_index.size();
            if (bound_size != 12) {
                // disp("search bound is error!");
                return -2;
            } else {
                int angle_size       = angle.size();
                int ring_angle_index = 1;
                if (bound_index[11] > (angle_size - 1)) {
                    ring_angle_index = bound_index[11] - angle_size;  // begin from 0
                } else {
                    ring_angle_index = bound_index[11];
                }
                bound_value.push_back(angle[bound_index[0]]);
                bound_value.push_back(angle[bound_index[3]]);
                bound_value.push_back(angle[bound_index[4]]);
                bound_value.push_back(angle[bound_index[7]]);
                bound_value.push_back(angle[bound_index[8]]);
                bound_value.push_back(angle[ring_angle_index]);

                float delta_angle = bound_value[1] - bound_value[0];
                delta_angle       = delta_angle < 0 ? delta_angle + 360 : delta_angle;

                float delta_angle2 = bound_value[3] - bound_value[2];
                delta_angle2       = delta_angle2 < 0 ? delta_angle2 + 360 : delta_angle2;

                float delta_angle3 = bound_value[5] - bound_value[4];
                delta_angle3       = delta_angle3 < 0 ? delta_angle3 + 360 : delta_angle3;

                if (delta_angle > 30 || delta_angle2 > 30 || delta_angle3 > 30) {
                    // disp("bound is too large!");
                    return -3;
                } else {
                    /*qDebug() <<bound_value[0]<< " "<<bound_value[1]<<" " <<
                               bound_value[2]<< " "<<bound_value[3]<<" " <<
                               bound_value[4]<< " "<<bound_value[5];*/
                    param->push_back(bound_value[0]);
                    param->push_back(bound_value[1]);
                    param->push_back(bound_value[2]);
                    param->push_back(bound_value[3]);
                    param->push_back(bound_value[4]);
                    param->push_back(bound_value[5]);
                }
            }
            //
            int initial_size = initial_index.size();

            if (initial_size != 2) {
                // disp("initial angle is error!");
                return -4;
            } else {
                float init_angle1 = angle[initial_index[0]];
                float init_angle2 = angle[initial_index[1]];
                if ((init_angle2 > 180) && (init_angle1 < 180) && ((init_angle2 + init_angle1) / 2.0 > 170) && ((init_angle2 + init_angle1) / 2.0 < 190)) {

                    // initial_value.push_back( (init_angle2 + init_angle1)/2.0);
                    // qDebug() << (init_angle2 + init_angle1)/2.0;
                    qDebug() << bound_value[0] << " " << bound_value[1] << " " << bound_value[2] << " " << bound_value[3] << " " << bound_value[4] << " "
                             << bound_value[5] << " " << (init_angle2 + init_angle1) / 2.0;
                    param->push_back((init_angle2 + init_angle1) / 2.0);

                } else {
                    // disp("initial angle range is error!")
                    return -5;
                }
            }


            //
            bound_index.clear();
            initial_index.clear();
            break;

        } else {
            //
            diff_value = dis[m + 2] - dis[m];
            data_diference_value.push_back(diff_value);
            //  check bound
            if (diff_value < -bound_threshold && is_neg == 0) {
                bound_index.push_back(m);
                is_neg = 1;
                is_pos = 0;
            } else if (diff_value > bound_threshold && is_pos == 0) {
                bound_index.push_back(m + 2);
                is_neg = 0;
                is_pos = 1;
            }
            // initial
            if (diff_value < -initial_threshold && diff_value > -100 && is_initial_neg == 0 && is_neg == 0 && is_pos == 1 &&
                m > (3 * dis.size() / 8 - buff_index_offset)) {
                // qDebug()<<"m1: " <<m <<" "<< dis.size()/4-buff_index_offset;
                initial_index.push_back(m);
                is_initial_neg = 1;
                is_initial_pos = 0;
            } else if (diff_value > initial_threshold && diff_value < 100 && is_initial_pos == 0 && is_neg == 0 && is_pos == 1 &&
                       (m < 5 * dis.size() / 8 - buff_index_offset)) {
                // qDebug()<<"m2: " <<m <<" "<< 3*dis.size()/4-buff_index_offset;
                initial_index.push_back(m + 2);
                is_initial_neg = 0;
                is_initial_pos = 1;
            }
        }
    }

    return 0;
}

void PointCloudsChart::ReceivePointCloudData(std::vector<ProtocolData> data) {

    if (isShowFileData == true) {  // file is running
        return;
    }
    uint16_t        pointNum = data.at(0).data.size();
    double          angle = 0, pickAngle, curSpd = 0.0, curVoltage = 0;
    int             pickDis      = 0;
    uint16_t        curIntensity = 0, curVersion = 0, curCode = 0;
    uint16_t        deepth = 0, intensity = 0, HrFlag = 0;
    double          minDetaAngle = 10000;
    uint16_t        index        = 0;
    QVector<double> coordX(pointNum), coordY(pointNum), HrPointX, HrPointY;
    // double tmpDeepth=0;
    lastData = data;
    QVector<uint> hrIndex;
    /**/
    /*std::vector<float> ss_angle, ss_dis, ss_intensity,ss_param;

    for(uint ss=0; ss<data.at(0).data.size(); ss++) {
        ss_angle.push_back(data.at(0).data.at(ss).angle);
        ss_dis.push_back(data.at(0).data.at(ss).deepth);
        ss_intensity.push_back(data.at(0).data.at(ss).indensity);
    }

    //int ret = samsung_calc_bound_tri(ss_angle, ss_dis, ss_intensity,&ss_param);
    int ret = samsung_calc_bound(ss_angle, ss_dis, ss_intensity,&ss_param);*/


    /**/


    if (isStartRecord == false) {  //超过显示数据

        curSpd                                        = data.at(0).speed;
        curVersion                                    = data.at(0).version;
        bool                            isSearchPoint = true, isSearchPoint1 = true;
        uint                            searchPointIndex = 0, searchPointIndex1 = 0;
        uint                            sampleCount = 0;
        static float                    angleLast   = 0;
        std::vector<float>              delta_angle, series_angle;
        std::vector<std::vector<float>> angle_vector;

        for (uint16_t m = 0; m < pointNum; m++) {
            angle  = data.at(0).data.at(m).angle;
            deepth = data.at(0).data.at(m).deepth;

            if (angleName == "series") {
                float ag = angle - angleLast;
                if (ag >= 0 && ag < 10) {
                    delta_angle.push_back(ag);
                } else if (ag < 0) {
                    ag += 360;
                    delta_angle.push_back(ag);
                }

                angleLast = angle;
                series_angle.push_back(angle);
            }
            //            else if(angleName == "series") {
            //                angle_vector.push_back(angle);
            //            }


            // deepth = ((double)(tmpDeepth*tmpDeepth - 64))/(2.0*tmpDeepth);
            coordX[m] = qCos(qDegreesToRadians(angle)) * deepth;
            coordY[m] = -qSin(qDegreesToRadians(angle)) * deepth;

            if (data.at(0).data.at(m).HrDenote == 1) {  // HrPoint
                HrPointX.push_back(coordX[m]);
                HrPointY.push_back(coordY[m]);
                hrIndex.push_back(m);
            }


            double deta = fabs(pickPointAngle - angle);
            if (deta < minDetaAngle && deepth != 0) {
                index        = m;
                minDetaAngle = deta;
            }
            if (isExeCompensation == true) {
                if (isSearchPoint == true && fabs(samplePt - angle) < 1) {
                    isSearchPoint    = false;
                    searchPointIndex = m;
                }

                if (isSearchPoint1 == true && fabs(samplePt1 - angle) < 1) {
                    isSearchPoint1    = false;
                    searchPointIndex1 = m;
                }
            }
        }


    } else {
        QFile fileOut(fName);
        if (!fileOut.open(QIODevice::Append | QIODevice::ReadWrite)) {
            QMessageBox::warning(this, tr("保存文件"), tr("打开保存文件失败：") + fileOut.errorString());
            return;
        }
        QTextStream tsOut(&fileOut);
        recordFrameCnt++;
        tsOut << "#" << recordFrameCnt << " " << pointNum << endl;
        tsOut << data.at(0).speed << endl;
        tsOut << data.at(0).version << endl;
        tsOut << data.at(0).healthCode << endl;
        tsOut << data.at(0).mcuVoltage << endl;
        curSpd     = data.at(0).speed;
        curVersion = data.at(0).version;

        for (uint16_t m = 0; m < pointNum; m++) {  //记录数据到文本
            angle     = data.at(0).data.at(m).angle;
            deepth    = data.at(0).data.at(m).deepth;
            intensity = data.at(0).data.at(m).indensity;
            coordX[m] = qCos(qDegreesToRadians(angle)) * deepth;
            coordY[m] = -qSin(qDegreesToRadians(angle)) * deepth;
            HrFlag    = data.at(0).data.at(m).HrDenote;

            tsOut << angle << " " << deepth << " " << intensity << " " << HrFlag << endl;
            double deta = fabs(pickPointAngle - angle);
            if (deta < minDetaAngle && deepth != 0) {
                index        = m;
                minDetaAngle = deta;
            }
        }
        fileOut.close();
    }
    if (pointNum > index) {  //显示数据信息
        pickLine->start->setCoords(0, 0);
        pickLine->end->setCoords(coordX[index], coordY[index]);
        pickAngle    = data.at(0).data.at(index).angle;
        pickDis      = data.at(0).data.at(index).deepth;
        curIntensity = data.at(0).data.at(index).indensity;
        curVoltage   = data.at(0).mcuVoltage / 100.0;
        curCode      = data.at(0).healthCode;
        pick->setText(QString::asprintf("total:%d curNum:%d curAngle:%.2f curDis:%d curIntensity:%d", pointNum, index, pickAngle, pickDis, curIntensity));
        version->setText(QString::asprintf("version: %d", curVersion));
        speed->setText(QString::asprintf("speed: %.1f Hz", curSpd));
        others->setText(QString::asprintf("DDS vol:%.2f   code:%4X H", curVoltage, curCode));

        if (isRecordPoint == true) {
            QFile fileOut(fPointName);
            if (!fileOut.open(QIODevice::Append | QIODevice::ReadWrite)) {
                QMessageBox::warning(this, tr("保存文件"), tr("打开保存文件失败：") + fileOut.errorString());
                return;
            }
            QTextStream tsOut(&fileOut);
            tsOut << pickDis << " " << curIntensity << endl;
            fileOut.close();
        }


        curStatisticsCnt = accumalateDis.size();
        if (curStatisticsCnt < kStatisticalMax) {
            accumalateDis.push_back(pickDis);
            accumalatePeak.push_back(curIntensity);
        } else {
            std::vector<float>              tmp = CalcStatistics(accumalateDis);  //统计信息
            std::vector<uint16_t>::iterator k   = accumalateDis.begin();
            accumalateDis.erase(k);

            std::vector<float>              tmpPeak = CalcStatistics(accumalatePeak);  //统计信息
            std::vector<uint16_t>::iterator kPeak   = accumalatePeak.begin();
            accumalatePeak.erase(kPeak);

            mean->setText(QString::asprintf("maxDis:%d minDis:%d meanDis:%d meanPeak:%.2f std:%.2f",
                                            (uint16_t)tmp.at(0),
                                            (uint16_t)tmp.at(1),
                                            (uint16_t)tmp.at(2),
                                            tmpPeak.at(2),
                                            tmp.at(3)));
        }
    }

    for (int m = 0; m < hrIndex.size(); m++) {
        coordX[hrIndex[m]] = 0;
        coordY[hrIndex[m]] = 0;
    }
    if (is_integration_pointcloud == false) {
        customPlot->graph(0)->setData(coordX, coordY);
        customPlot->graph(1)->setData(HrPointX, HrPointY);
    } else {
        customPlot->graph(0)->addData(coordX, coordY);
        customPlot->graph(1)->addData(HrPointX, HrPointY);
    }

    customPlot->replot();

    if (dynamicCalibration != nullptr) {
        dynamicCalibration->ReceivePointCloudData(data);
    }
}

void PointCloudsChart::ReceiveFileData(std::vector<ProtocolData> data) {
    if (isShowFileData == false) {
        return;
    }
    uint16_t      pointNum = data.at(0).data.size();
    double        angle = 0, pickAngle, curSpd = 0.0, curVoltage = 0;
    int           pickDis      = 0;
    uint16_t      curIntensity = 0, curVersion = 0, curCode = 0;
    uint16_t      deepth       = 0;
    double        minDetaAngle = 10000;
    uint16_t      index        = 0;
    QVector<uint> hrIndex;
    lastData = data;
    QVector<double> coordX(pointNum), coordY(pointNum), HrPointX, HrPointY;

    // if(isStartRecord == false) {
    for (uint16_t m = 0; m < pointNum; m++) {
#if 1
        //            if(m < 6 ) {
        ////                if(angle > 340) {
        ////                    angle = data.at(0).data.at(m).angle
        ////                }
        ////                else {
        ////                    angle = data.at(0).data.at(m).angle*degree_factor;
        ////                }

        //                angle = data.at(0).data.at(m).angle*degree_factor;
        //                deepth = 0;//data.at(0).data.at(m).deepth;
        //            }
        // else
        {
            if (m > (pointNum - 20) && angle < 10) {
                angle += 360;
                deepth = 0;
            } else {
                if (m < 10 && angle > 350) {
                    deepth = 0;
                } else {
                    deepth = data.at(0).data.at(m).deepth;
                }
            }
            //                if(m >=0 && m <= 2) {
            //                    deepth = 0;
            //                }

            angle = data.at(0).data.at(m).angle * degree_factor;
        }
        // deepth = data.at(0).data.at(m).deepth;


        if (angle > 360) {
            angle -= 360;
        }

#else
        angle  = data.at(0).data.at(m).angle;
        deepth = data.at(0).data.at(m).deepth;
#endif
        coordX[m]   = qCos(qDegreesToRadians(angle)) * deepth;
        coordY[m]   = -qSin(qDegreesToRadians(angle)) * deepth;
        double deta = fabs(pickPointAngle - angle);
        if (deta < minDetaAngle && deepth != 0) {
            index        = m;
            minDetaAngle = deta;
        }
        if (data.at(0).data.at(m).HrDenote == 1) {  // HrPoint
            HrPointX.push_back(coordX[m]);
            HrPointY.push_back(coordY[m]);
            hrIndex.push_back(m);
        }
    }
    //}
    /*else {
        QFile fileOut(fName);
        if(!fileOut.open(QIODevice::Append|QIODevice::ReadWrite))
        {
            QMessageBox::warning(this, tr("保存文件"),
                                 tr("打开保存文件失败：") + fileOut.errorString());
            return;
        }
        QTextStream tsOut(&fileOut);
        recordFrameCnt++;
        tsOut<<"#" << recordFrameCnt << " " << pointNum <<endl;
        tsOut<<data.at(0).speed <<endl;
        tsOut<<data.at(0).version<<endl;
        tsOut<<data.at(0).healthCode<<endl;
        tsOut<<data.at(0).mcuVoltage<<endl;

        for(uint16_t m=0; m<pointNum; m++) {
            angle = data.at(0).data.at(m).angle;
            deepth = data.at(0).data.at(m).deepth;
            intensity = data.at(0).data.at(m).indensity;
            coordX[m] = qCos(qDegreesToRadians(angle))*deepth;
            coordY[m] = -qSin(qDegreesToRadians(angle))*deepth;
            tsOut<< angle <<" "<<deepth<<" "<<intensity<<endl;
        }
        fileOut.close();
    }*/
    if (pointNum > index) {
        pickLine->start->setCoords(0, 0);
        pickLine->end->setCoords(coordX[index], coordY[index]);
        pickAngle    = data.at(0).data.at(index).angle;
        pickDis      = data.at(0).data.at(index).deepth;
        curIntensity = data.at(0).data.at(index).indensity;
        curSpd       = data.at(0).speed;
        curVoltage   = data.at(0).mcuVoltage / 100.0;
        curCode      = data.at(0).healthCode;
        pick->setText(QString::asprintf("total:%d curNum:%d curAngle:%.2f curDis:%d curIntensity:%d", pointNum, index, pickAngle, pickDis, curIntensity));
        version->setText(QString::asprintf("version: %d", curVersion));
        speed->setText(QString::asprintf("speed: %.1f Hz", curSpd));

        others->setText(QString::asprintf("DDS vol:%.2f code: %4X H", curVoltage, curCode == 0 ? 0x0000 : curCode));
        curStatisticsCnt = accumalateDis.size();
        if (curStatisticsCnt < kStatisticalMax) {
            accumalateDis.push_back(pickDis);
        } else {
            std::vector<float>              tmp = CalcStatistics(accumalateDis);  //统计信息
            std::vector<uint16_t>::iterator k   = accumalateDis.begin();
            accumalateDis.erase(k);
            mean->setText(
                QString::asprintf("maxDis:%d minDis:%d meanDis:%d std:%.2f", (uint16_t)tmp.at(0), (uint16_t)tmp.at(1), (uint16_t)tmp.at(2), tmp.at(3)));
        }
    }
    for (int m = 0; m < hrIndex.size(); m++) {
        coordX[hrIndex[m]] = 0;
        coordY[hrIndex[m]] = 0;
    }
    if (is_integration_pointcloud == false) {
        customPlot->graph(0)->setData(coordX, coordY);
        customPlot->graph(1)->setData(HrPointX, HrPointY);
    } else {
        customPlot->graph(0)->addData(coordX, coordY);
        customPlot->graph(1)->addData(HrPointX, HrPointY);
    }
    customPlot->replot();
}

void PointCloudsChart::on_actionSaveAs_triggered() {
    if (saveAsCapturePtr == nullptr) {
        saveAsCapturePtr = new SaveAsCapture();
        saveAsCapturePtr->show();
        saveAsCapturePtr->getCustomPlot(customPlot);
    } else {
        saveAsCapturePtr->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
        saveAsCapturePtr->show();
    }
}

void PointCloudsChart::on_actionRecord_triggered() {
    if (recordPointCloud == nullptr) {
        recordPointCloud = new RecordPointCloud();
        recordPointCloud->show();
        connect(recordPointCloud, &RecordPointCloud::StarRecord, this, &PointCloudsChart::RecStartRecord);
        connect(recordPointCloud, &RecordPointCloud::StopRecord, this, &PointCloudsChart::RecStopRecord);
    } else {
        recordPointCloud->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
        recordPointCloud->show();
    }
}

void PointCloudsChart::on_actionImportCloud_triggered() {
    if (importPointCloudPtr == nullptr) {
        importPointCloudPtr = new ImportPointCloud();
        importPointCloudPtr->show();
        connect(importPointCloudPtr, &ImportPointCloud::transmitPointCloudData, this, &PointCloudsChart::ReceiveFileData);
        connect(importPointCloudPtr, &ImportPointCloud::transmitShowFileDataCmd, this, &PointCloudsChart::RecShowFileDataCmd);

    } else {
        importPointCloudPtr->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
        importPointCloudPtr->show();
    }
}

void PointCloudsChart::on_actionRecordCalibration_triggered() {
    if (dynamicCalibration == nullptr) {
        dynamicCalibration = new DynamicCalibration();
        dynamicCalibration->show();
        connect(dynamicCalibration, &DynamicCalibration::TransmitSerialCmd, this, &PointCloudsChart::TransmitSerialCmd);
        // connect(importPointCloudPtr,&ImportPointCloud::transmitShowFileDataCmd,this,&PointCloudsChart::RecShowFileDataCmd);
        connect(dynamicCalibration, &DynamicCalibration::TransmitCalibrationData, this, &PointCloudsChart::TransmitCalibrationData);

    } else {
        dynamicCalibration->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
        dynamicCalibration->show();
    }
}

void PointCloudsChart::on_actionScreenShot_triggered() {
    QString fileName = QFileDialog::getSaveFileName(this, "Save document...", qApp->applicationDirPath(), "*.bmp");
    if (!fileName.isEmpty()) {
        ui->customPlot->saveBmp(fileName, ui->customPlot->width(), ui->customPlot->height());
    }
    /**********************************************/
    //    QString strFile = QFileDialog::getOpenFileName(
    //                this,
    //                tr("打开文件"),
    //                tr(""),
    //                tr("Text Files(*.cspc);Text Files(*.csv);All files(*)")
    //                );
    //    if( strFile.isEmpty()) {
    //        return;
    //    }
    //    QFile fileIn(strFile);
    //    if( ! fileIn.open(QIODevice::ReadOnly)) {
    //        QMessageBox::warning(this, tr("打开文件"), tr("打开文件失败：") + fileIn.errorString());
    //        return;
    //    }


    //    uint32_t fileSize = fileIn.size();
    //    qDebug() << "file size: "<<fileSize;

    //    QTextStream tsIn(&fileIn);
    //    QString line ;
    //    QStringList item;

    //    int line_cnt = 0;
    //    while( ! tsIn.atEnd() )
    //    {
    //        hr_n_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                hr_n_tof[m].push_back(item[n].toFloat());
    //                qDebug()<<"1 "<< hr_n_tof[m][n];
    //            }
    //        }
    //        wt_n_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                wt_n_tof[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        bk_n_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                bk_n_tof[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        hr_n_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                hr_n_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        wt_n_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                wt_n_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        bk_n_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                bk_n_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        hr_a_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                hr_a_tof[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        wt_a_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                wt_a_tof[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        bk_a_tof.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                bk_a_tof[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        hr_a_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                hr_a_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        wt_a_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                wt_a_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }
    //        bk_a_peak.resize(9);
    //        for(uint m=0; m<9; m++) {
    //            line = tsIn.readLine();
    //            line = line.trimmed();
    //            item = line.split(',');
    //            line_cnt = item.size();
    //            for(int n=0; n<line_cnt-1; n++) {
    //                bk_a_peak[m].push_back(item[n].toFloat());
    //                //qDebug()<< item[n].toFloat();
    //            }
    //        }

    //    }
    //    fileIn.close();
    //    qDebug()<<"2 ";
    //    std::vector<std::vector<std::vector<float>>> data_test;

    //    data_test.resize(12);
    //    data_test[0] = hr_n_tof;
    //    data_test[1] = wt_n_tof;
    //    data_test[2] = bk_n_tof;
    //    data_test[3] = hr_n_peak;
    //    data_test[4] = wt_n_peak;
    //    data_test[5] = bk_n_peak;
    //    data_test[6] = hr_a_tof;
    //    data_test[7] = wt_a_tof;
    //    data_test[8] = bk_a_tof;
    //    data_test[9] = hr_a_peak;
    //    data_test[10] = wt_a_peak;
    //    data_test[11] = bk_a_peak;
    //    std::vector<float> param;
    //    sampleDisance.push_back(50);
    //    sampleDisance.push_back(110);
    //    sampleDisance.push_back(170);
    //    sampleDisance.push_back(220);
    //    sampleDisance.push_back(300);
    //    sampleDisance.push_back(600);
    //    sampleDisance.push_back(1000);
    //    sampleDisance.push_back(3000);
    //    sampleDisance.push_back(7000);

    //    int status = CalibrationProcess(data_test,sampleDisance,&param);
    //    if(status != 1) {
    //        qDebug()<<status;
    //        QMessageBox::warning(NULL, "数据","标定出错");
    //    }

    //    calibrationpPram = param;
    //    //a505a6070002
    //    QByteArray data;
    //    uint8_t noneData = 0;
    //    data.push_back(0xA5);
    //    data.push_back(0x05);
    //    data.push_back(0xA6);
    //    data.push_back(noneData);
    //    data.push_back(noneData);
    //    data.push_back(0x02);

    //    uint8_t tmpBuff[4] = {0};
    //    for(uint m=0; m<1024; m=m+4) {
    //        memcpy(tmpBuff,&param[m/4],4);
    //        data.push_back(tmpBuff[0]);
    //        data.push_back(tmpBuff[1]);
    //        data.push_back(tmpBuff[2]);
    //        data.push_back(tmpBuff[3]);

    //    }
    //    uint8_t checkNum=0;
    //    for(uint m=0; m<(6+1024); m++) {
    //        checkNum ^= data[m];

    //    }

    //    data[3] = checkNum;
    //    //pack param
    //    qDebug() << "execute param download...";
    //    TransmitSerialCmd(data);


    /*************************************************/
}

void PointCloudsChart::RecStartRecord(QString fileName) {
    if (fileName == "delta" || fileName == "series") {
        angleName = fileName;
        return;
    }


    QDateTime current_time = QDateTime::currentDateTime();
    QString   data         = current_time.toString("yyyy-MM-dd-hh-mm-ss-");

    if (fileName.at(0) == "-") {
        fPointName = "point" + fileName + ".cspc";
        QFile fileOut(fPointName);

        if (!fileOut.open(QIODevice::Append | QIODevice::ReadWrite)) {
            QMessageBox::warning(this, tr("保存文件"), tr("打开保存文件失败：") + fileOut.errorString());
            return;
        }
        fileOut.close();
        isRecordPoint = true;

    } else if (fileName.at(0) == "*") {
        isRecordPoint = false;
    } else {
        fName = data + fileName + ".cspc";
        QFile fileOut(fName);

        if (!fileOut.open(QIODevice::Append | QIODevice::ReadWrite)) {
            QMessageBox::warning(this, tr("保存文件"), tr("打开保存文件失败：") + fileOut.errorString());
            return;
        }
        fileOut.close();
        isStartRecord  = true;
        recordFrameCnt = 0;
        ui->actionRecord->setText("Rec!!!!");
    }
}

void PointCloudsChart::RecStopRecord() {
    isStartRecord = false;
    isRecordPoint = false;
    ui->actionRecord->setText("Record");
}

void PointCloudsChart::RecShowFileDataCmd(bool isFileData) {
    isShowFileData = isFileData;
    if (isShowFileData == true) {
        ui->actionImportCloud->setText("impt!!");
    } else {
        ui->actionImportCloud->setText("ImtPtCld");
    }
}

std::vector<float> PointCloudsChart::CalcStatistics(std::vector<uint16_t> data) {
    // 求均值
    uint32_t sum  = std::accumulate(std::begin(data), std::end(data), 0.0);
    double   mean = sum / data.size();

    // 求方差与标准差 最大最小值

    double variance = 0.0;
    for (ulong i = 0; i < data.size(); i++) {
        variance = variance + pow(data[i] - mean, 2);
    }
    variance                  = variance / data.size();
    double standard_deviation = sqrt(variance);

    std::vector<uint16_t>::iterator biggest  = std::max_element(data.begin(), data.end());
    std::vector<uint16_t>::iterator smallest = std::min_element(data.begin(), data.end());


    std::vector<float> statistData;
    statistData.push_back(*biggest);
    statistData.push_back(*smallest);
    statistData.push_back(mean);
    statistData.push_back(standard_deviation);
    return statistData;
}

void PointCloudsChart::on_actionStandBy_triggered() {
    QByteArray qba;
    qba.push_back(0xAA);  // AA 55 FD 02
    qba.push_back(0x55);
    qba.push_back(0xFD);
    qba.push_back(0x02);


    QByteArray buff;
    uint8_t    fill[1] = {0};
    buff.append(0xA5);
    buff.append(0x05);
    buff.append(0xF0);
    buff.append(fill[0]);
    buff.append(0x02);
    buff.append(fill[0]);
    buff.append(fill[0]);
    buff.append(fill[0]);
    buff.append(0xFD);
    buff.append(0x02);
    //    buff.append(0xF5);
    //    buff.append(0x0A);

    uint8_t ckeck = 0;
    for (uint m = 0; m < 10; m++) {
        if (m != 3) {
            ckeck ^= buff[m];
        }
    }
    buff[3] = ckeck;

    qba += buff;
    qDebug() << buff.toHex();
    emit TransmitSerialCmd(qba);
}

void PointCloudsChart::on_actionRunning_triggered() {
    QByteArray qba;
    qba.push_back(0xAA);  // AA 55 FE 01
    qba.push_back(0x55);
    qba.push_back(0xF0);
    qba.push_back(0x0F);

    QByteArray buff;
    uint8_t    fill[1] = {0};
    buff.append(0xA5);
    buff.append(0x05);
    buff.append(0xF0);
    buff.append(fill[0]);
    buff.append(0x02);
    buff.append(fill[0]);

    //    buff.append(0x46);
    buff.append(fill[0]);
    buff.append(fill[0]);
    // running
    //    buff.append(0xFE);
    //    buff.append(0x01);
    // turn on
    buff.append(0xF0);
    buff.append(0x0F);

    //    buff.append(fill[0]);
    //    buff.append(fill[0]);

    uint8_t ckeck = 0;
    for (uint m = 0; m < 10; m++) {
        if (m != 3) {
            ckeck ^= buff[m];
        }
    }
    buff[3] = ckeck;

    qba += buff;


    qDebug() << buff.toHex();
    emit TransmitSerialCmd(qba);
}


int PointCloudsChart::samsung_calc_bound_tri(std::vector<float> angle, std::vector<float> dis, std::vector<float> intensity, std::vector<float> *param) {
    if (angle.size() != dis.size() || dis.size() != intensity.size()) {
        return -1;
    }

    std::vector<float> data_diference_value;
    const int          bound_threshold = 20;

    std::vector<float> bound_index;
    std::vector<float> bound_value;
    int                diff_value = 0;
    uint               dis_last = 0, dis_index_last = 0;
    std::vector<float> angle_index;


    for (uint m = 0; m < dis.size(); m++) {
        if (m + 1 == (dis.size())) {  // last one
            uint i = 0;
            while (1) {
                if (dis[i] == 0) {
                    i++;
                } else {
                    break;
                }
            }
            diff_value = dis[i] - dis_last;
            if (abs(diff_value) > bound_threshold) {
                angle_index.push_back(i);
            }
            if (angle_index.size() != 8) {
                return -2;

            } else {
                for (uint j = 0; j < 8; j++) {
                    // qDebug()<<angle[angle_index[j]];
                    param->push_back(angle_index[j]);
                }
                // qDebug()<<"   ";
            }
            bound_index.clear();
            return 0;

        } else {

            if (dis[m + 1] == 0) {
                continue;
            }

            if (dis[m] != 0) {
                dis_last       = dis[m];
                dis_index_last = m;
            }

            diff_value = dis[m + 1] - dis_last;
            // data_diference_value.push_back(diff_value);
            //  check bound
            if (abs(diff_value) > bound_threshold) {
                angle_index.push_back(m);
            }
        }
    }

    return 0;
}


void PointCloudsChart::on_actionPointcloudQuality_triggered() {
    if (pointCloudQualityPtr == nullptr) {
        pointCloudQualityPtr = new PointCloudQuality();
        pointCloudQualityPtr->show();

    } else {
        pointCloudQualityPtr->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
        pointCloudQualityPtr->show();
    }
}
