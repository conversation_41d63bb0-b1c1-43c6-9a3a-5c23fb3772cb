cmake_minimum_required(VERSION 3.16)

# Project configuration
project(WIN_BASE VERSION 2.3.11 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Force MinGW compiler usage
if(WIN32)
    set(CMAKE_C_COMPILER "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/gcc.exe")
    set(CMAKE_CXX_COMPILER "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe")
    set(CMAKE_MAKE_PROGRAM "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/mingw32-make.exe")

    # Use Ninja generator for better performance
    if(NOT CMAKE_GENERATOR STREQUAL "Ninja")
        message(STATUS "Recommend using Ninja generator: cmake -G Ninja ..")
    endif()
endif()

# Set Qt5 path explicitly to avoid conflicts with other Qt versions
set(QT5_ROOT_PATH "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64")
set(CMAKE_PREFIX_PATH "${QT5_ROOT_PATH}")
set(Qt5_DIR "${QT5_ROOT_PATH}/lib/cmake/Qt5")

# Verify Qt5 path exists
if(NOT EXISTS "${Qt5_DIR}")
    message(FATAL_ERROR "Qt5 not found at ${Qt5_DIR}. Please check your Qt5.14.2 installation.")
endif()

message(STATUS "Using Qt5 from: ${QT5_ROOT_PATH}")

# Find Qt5 components
find_package(Qt5 REQUIRED COMPONENTS
    Core
    Gui
    Widgets
    SerialPort
    PrintSupport
    AxContainer
    Sql
)

# Enable Qt5 features
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add compile definitions
add_definitions(
    -DQT_DEPRECATED_WARNINGS
    -DQT_MESSAGELOGCONTEXT
    -DUSING_LOG_TO_FILE=0
    -DUSING_CUSTOM_STYLE=0
    -DUSING_CUSTOM_HIDE=0
    -DAPP_VERSION="2.3.11"
)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/polynomialFit
    ${CMAKE_CURRENT_SOURCE_DIR}/customPlot
    ${CMAKE_CURRENT_SOURCE_DIR}/eigen-3.4.0
    ${CMAKE_CURRENT_SOURCE_DIR}/dll
)

# Source files
set(SOURCES
    main.cpp
    widget.cpp
    dynamic_calibration.cpp
    import_pointcloud.cpp
    pointcloud_quality.cpp
    show_image.cpp
    lidar_protocol.cpp
    other_protocol.cpp
    record_pointcloud.cpp
    saveas_capture.cpp
    serial_base.cpp
    calibration_chart.cpp
    greymap_chart.cpp
    histogram_chart.cpp
    pointclouds_chart.cpp
    lidar_database.cpp
    task_process/task_base.cpp
)

# Header files
set(HEADERS
    widget.h
    dynamic_calibration.h
    import_pointcloud.h
    lidar_protocol.h
    other_protocol.h
    pointcloud_quality.h
    record_pointcloud.h
    saveas_capture.h
    serial_base.h
    calibration_chart.h
    greymap_chart.h
    histogram_chart.h
    pointclouds_chart.h
    lidar_database.h
    show_image.h
    task_process/task_base.h
    dtof_calibration.h
)

# UI files
set(UI_FILES
    widget.ui
    dynamiccalibration.ui
    pointcloudquality.ui
    showimage.ui
    importpointcloud.ui
    recordpointcloud.ui
    saveascapture.ui
    calibrationchart.ui
    greymapchart.ui
    histogramchart.ui
    pointcloudschart.ui
    lidardatabase.ui
)

# Resource files
set(RESOURCES
    logo.qrc
    logo2.qrc
    res.qrc
)

# Add subdirectories for custom modules
add_subdirectory(polynomialFit)
add_subdirectory(customPlot)

# Create executable
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# Link Qt5 libraries
target_link_libraries(${PROJECT_NAME}
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::SerialPort
    Qt5::PrintSupport
    Qt5::AxContainer
    Qt5::Sql
)

# Link static library
target_link_libraries(${PROJECT_NAME}
    ${CMAKE_CURRENT_SOURCE_DIR}/libdToF_calibration.a
)

# Link custom modules
target_link_libraries(${PROJECT_NAME}
    polynomialFit
    customPlot
)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Set target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "WIN_BASE-V2_3_11_T5"
    WIN32_EXECUTABLE TRUE
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Add compiler flags for Windows
if(WIN32)
    # For MinGW/GCC - handle large object files and encoding
    target_compile_options(${PROJECT_NAME} PRIVATE
        "-Wa,-mbig-obj"
        "-finput-charset=UTF-8"
        "-fexec-charset=UTF-8"
    )
    target_compile_options(customPlot PRIVATE
        "-finput-charset=UTF-8"
        "-fexec-charset=UTF-8"
    )
    target_compile_options(polynomialFit PRIVATE
        "-finput-charset=UTF-8"
        "-fexec-charset=UTF-8"
    )

    # Set application icon
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/logo.ico")
        set_target_properties(${PROJECT_NAME} PROPERTIES
            WIN32_EXECUTABLE TRUE
        )
        # Note: For icon support, you may need to create a .rc file
        # or use a different approach depending on your build system
    endif()
endif()

# Qt deployment
if(WIN32)
    # Find windeployqt executable
    set(WINDEPLOYQT_EXECUTABLE "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin/windeployqt.exe")
    set(QT5_BIN_DIR "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64/bin")
    set(MINGW_BIN_DIR "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin")

    if(EXISTS "${WINDEPLOYQT_EXECUTABLE}")
        # Add custom target for Qt deployment
        add_custom_target(deploy
            COMMAND ${CMAKE_COMMAND} -E env
                "PATH=${QT5_BIN_DIR};${MINGW_BIN_DIR}"
                "QT_DIR=${QT5_ROOT_PATH}"
                "QTDIR=${QT5_ROOT_PATH}"
                ${WINDEPLOYQT_EXECUTABLE} --debug --compiler-runtime $<TARGET_FILE:${PROJECT_NAME}>
            DEPENDS ${PROJECT_NAME}
            COMMENT "Deploying Qt libraries and dependencies"
        )

        # Automatically deploy after build with proper environment
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E env
                "PATH=${QT5_BIN_DIR};${MINGW_BIN_DIR}"
                "QT_DIR=${QT5_ROOT_PATH}"
                "QTDIR=${QT5_ROOT_PATH}"
                ${WINDEPLOYQT_EXECUTABLE} --debug --compiler-runtime --dir ${CMAKE_BINARY_DIR}/bin $<TARGET_FILE:${PROJECT_NAME}>
            COMMENT "Auto-deploying Qt dependencies to bin directory"
        )

        message(STATUS "Qt deployment configured with windeployqt: ${WINDEPLOYQT_EXECUTABLE}")
    else()
        message(WARNING "windeployqt not found at ${WINDEPLOYQT_EXECUTABLE}")
    endif()
endif()

# Installation rules
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)
