void on_actionLCopy_triggered();
#ifndef LIDAR_DATABASE_H
#define LIDAR_DATABASE_H

#include <QMainWindow>
#include <string.h>
#include <QTimer>
#include <QTime>
#include <QFileDialog>
#include <QDebug>
#include <QtSql/QSqlDatabase>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlTableModel>
#include <QStandardItemModel>
#include <QFile>
#include <QMessageBox>
#include "show_image.h"

#include <fstream> //定义读写已命名文件的类型
#include <sstream> //多定义的类型则用于读写存储在内存中的string对象
#include "lidar_protocol.h"


const   uint8_t  kMcuIdRowCnt = 1;
const   uint8_t  kSystemModeRowCnt = 1;
const   uint8_t  kTemperatureRowCnt = 1;
const   uint8_t  KRegisterRowCnt = 1;
const   uint8_t  kVersionBaudRowCnt = 2;
const   uint8_t  kDateRowCnt = 1;
const   uint8_t  KZeroAngleRowCnt = 1;
const   uint8_t  KCalibrationRowCnt = 32;
const   uint8_t  kTgRowCnt = 3;
const   uint8_t  KRefleRowCnt = 12;
const   uint8_t  kMeasureRangeRowCnt = 2;
const   uint8_t  KHisFacFreqRowCnt = 2;
const   uint8_t  KGreymapImageRowCnt = 1;
const   uint8_t  KHistogramImageRowCnt = 1;
const   uint8_t  KCalibrationImageRowCnt = 1;
const   uint8_t  KDebug1RowCnt = 1;
const   uint8_t  KDebug2RowCnt = 1;
const   uint8_t  KDebug3RowCnt = 1;
const   uint8_t  KDebug4RowCnt = 1;
const   uint8_t  KImageRowCnt = 10;
const   uint8_t  KLaserRowCnt = 1;
const   uint8_t  KLedRowCnt = 11;
const   int kParamTitleCnt = 76+2;
const QString kTableName = "lidarparam";

typedef struct {
    union{
        uint16_t allBits;
        struct{
           bool readMcuID           : 1;
           bool readVersion         : 1;
           bool readZeroAngle       : 1;
           bool readDate            : 1;
           bool readCalibration     : 1;
           bool readReflectivity    : 1;
           bool readRange           : 1;
           bool readSeserve1        : 1;
           bool readSeserve2        : 1;
           bool readSeserve3        : 1;
           bool readSeserve4        : 1;
           bool readSeserve5        : 1;
           bool readSeserve6        : 1;
           bool readSeserve7        : 1;
           bool readSeserve8        : 1;
           bool readSeserve9        : 1;
        }bit;
    };

}StReadParamFlag;


/*enum {
    KStarTask0 = 0,
    kStopTask0 ,
    KStarTask1 ,
    kStopTask1 ,
    KStarTask2 ,
    kStopTask2 ,
    KStarTask3 ,
    kStopTask3 ,
    KStarTask4 ,
    kStopTask4 ,
    KStarTask5 ,
    kStopTask5 ,
    KStarTask6 ,
    kStopTask6 ,
    KStarTask7 ,
    kStopTask7 ,
    KStarTask8 ,
    kStopTask8 ,
    KStarTask9 ,
    kStopTask9 ,
    KStarTask10 ,
    kStopTask10 ,
    kSize,
};

typedef struct {
    bool isExeCmd;
    bool isWaitAck;
    bool isLockExe;
}PtrTaskFlag;

typedef struct {
    bool isExeTask;
    uint64_t lastCnt;
    std::vector<PtrTaskFlag> taskFlag;
    std::vector<void (*)(void)>  functionPtr;
}PtrTask;*/


namespace Ui {
class LidarDatabase;
}

class LidarDatabase : public QMainWindow
{
    Q_OBJECT

public:
    /*协议id枚举*/
    enum {
        kTriMode = 0xA0,
        kTofMode = 0xA1,
        kSysParam = 0xA2,
        kMcuId = 0xA3,
        kVersionBuad = 0xA4,
        kMeasureRange = 0xA5,
        kCalibrationParam = 0xA6,
        kTgParam = 0xA7,
        kReflelParam = 0xA8,
        kHisAndFacula = 0xA9,
        kTemperature = 0xAA,
        kRegisterSetting = 0xAB,
        kZeroAngleSetting = 0xAC,
        kLaserControl	= 0xAD,
        kLedControl = 0xAE,
        kVbdControl = 0xAF,
        kDebugSetting4 = 0xBC,
        kDebugSetting3 = 0xBD,
        kDebugSetting2 = 0xBE,
        kDebugSetting = 0xBF,
        kXtalk = 0xD1,

        kSize,
    };
    /*协议cmd枚举*/
    enum {
            kH2D = 0x01,	/*host->device*/
            kD2H = 0X02,	/*device->host*/
            kHWS = 0x04,	/*host write success*/
            kHRS = 0x08,	/*host read success*/
            kHSS = 0x10,	/*host send success   check err is err*/
            kDS = 0x20,    /*device fixed frequency send*/
            kDNP = 0x40,    /*device not parse logic*/
    };

    /*模式与枚举*/
    enum{
            kTriStopMode                = 0xA5FF,
            kTriTestMode  				= 0xA5FE,
            kTriFaculaMode  			= 0xA5FD,
            kTriScanMode  				= 0xA5FC,
            ktRICalibrationMode   = 0xA5FB,

            kTofStopMode  				= 0xA5FF,
            kTofTestMode  				= 0xA5FE,
            kTofFaculaMode  			= 0xA5FD,
            kTofScanMode  				= 0xA5FC,
            kTofCalibrationMode         = 0xA5FB,
            kTofSinglePointMode			= 0xA5FA,
            kTofRangingMode             = 0xA5F9,
            kTofSize
    };

    const QString  paramTitle[kParamTitleCnt] = {
        "McuId",
        "Mode",
        "Temperature",
        "Register",
        "Version",
        "Baud",
        "Date",
        "ZeroAngle",
        "Cal_p02",
        "Cal_p01",
        "Cal_p00",
        "Cal_mk0",
        "Cal_p12",
        "Cal_p11",
        "Cal_p10",
        "Cal_mk1",
        "Cal_p22",
        "Cal_p21",
        "Cal_p20",
        "Cal_mk2",
        "Cal_p32",
        "Cal_p31",
        "Cal_p30",
        "Cal_mk3",
        "Cal_p42",
        "Cal_p41",
        "Cal_p40",
        "Cal_mk4",
        "Cal_p52",
        "Cal_p51",
        "Cal_p50",
        "Cal_mk5",
        "Cal_p62",
        "Cal_p61",
        "Cal_p60",
        "Cal_mk6",
        "Cal_p72",
        "Cal_p71",
        "Cal_p70",
        "Cal_mk7",
        "Tg_p2",
        "Tg_p1",
        "Tg_p0",
        "Refle_p03",
        "Refle_p02",
        "Refle_p01",
        "Refle_p00",
        "Refle_p13",
        "Refle_p12",
        "Refle_p11",
        "Refle_p10",
        "Refle_p23",
        "Refle_p22",
        "Refle_p21",
        "Refle_p20",
        "RangeLow",
        "RangeUp",
        "His_freq",
        "Facula_freq",
        "Greymap",
        "Histogram",
        "Calibration",
        "Debug1",
        "Debug2",
        "Debug3",
        "Debug4",
        "Image1",
        "Image2",
        "Image3",
        "Image4",
        "Image5",
        "Image6",
        "Image7",
        "Image8",
        "Image9",
        "Image10",
        "Laser",
        "Led"
    };


    typedef struct {
            uint8_t     isInitParam;
            uint8_t 	uID[12];
            uint16_t 	version;
            uint16_t	buadType;
            uint16_t    measureRange[2];
            float       calibrationP2;
            float       calibrationP1;
            float       calibrationP0;
            float 		tgCoefficientP2;
            float 		tgCoefficientP1;
            float 		tgCoefficientP0;
            float		materialConfficientP2;
            float		materialConfficientP1;
            float		materialConfficientP0;
            uint32_t	histogramAndSpadSampleFreq[2];
            uint16_t    currentTemperature;


    }PtrSysParam;


    typedef struct {
        uint32_t   addr;
        uint16_t   size;
        uint8_t    data[8192];


    }app_bin_one_frame;

    typedef struct {
        uint8_t     header;
        uint8_t     cmd;
        uint8_t     id;
        uint8_t     xor_check;
        uint16_t    length;
        uint8_t     data[0];

    }app_cmd_protocol;

    typedef struct {
       bool   app_unlock;
       bool   app_to_bootloader_ok;
       bool   start_iap;
       bool   load_one_frame_ok;
       bool   read_one_frame_ok;
       bool   stop_iap;
       app_bin_one_frame   frame;

    }app_bin_status;

    explicit LidarDatabase(QWidget *parent = nullptr);
    ~LidarDatabase();
    void GuiSetup();
    void SqlSetup();
    void CreateConnectionByName(const QString &connectionName);
    QSqlDatabase GetConnectionByName(const QString &connectionName);
    void ReadParamThroughId(uint8_t id);
    void delay_ms(int msec);
    void iap_app_unlock(uint8_t cmd, uint8_t id);
    void iap_app_to_bootloader(uint8_t cmd, uint8_t id);
    void iap_app_cmd_pack(uint8_t cmd, uint8_t id, app_bin_one_frame *frame);

signals:
    void TransmitDataLidar(QByteArray str);
    void TransmitImage(QByteArray map, int w, int h);
    void TransmitZeroAngle(int feedbackType, QByteArray data,float angle);
    void TransmitHistType(uint8_t type);
    void FeedbackInfoTmp(QByteArray fdb);

public slots:
    void FeedbackInfoLidar(QByteArray fdb);
    void ReceviceCalibrationData(std::vector<float> p);
    /*******IAP*********/
    void sig_iap_ack(iap_ack_t ack);

private slots:
    void on_pushButtonTransmit_clicked();
    void IdSelectSlots();
    void CmdSelectSlots();
    void timeOut();
    void ReShow();

    void on_actionDSchOnce_triggered();

    void on_actionDSchAll_triggered();

    void on_actionDSubmit_triggered();

    void on_actionDSubmitAll_triggered();

    void on_actionDInsert_triggered();

    void on_actionDIstImg_triggered();

    void on_actionDRevert_triggered();

    void on_actionDRevertAll_triggered();

    void on_actionLCopy_triggered();

    void on_actionDShowIamge_triggered();

    void on_comboBoxRunningMode_editTextChanged(const QString &arg1);

    void on_actionOTA_triggered();

private:
    Ui::LidarDatabase *ui;
    uint8_t tCmd,rCmd;
    uint8_t tId,rId;
    std::map<uint16_t,QString> modeMap;
    std::map<uint8_t,int> baudMap;
    uint  tableWidgetRowNum;
    std::map<QString,uint8_t>   tableRowCntMap;
    std::vector<uint8_t> IntegralItemRowCnt;/*每一项的累计和*/

    QSqlDatabase db;
    bool  isConnectioned;
    QSqlTableModel *model;
    QStandardItemModel *curItemModel = nullptr;
    //QSqlQuery query;
    //image *imagePtr = nullptr;
    QTimer *timer = nullptr;
    StReadParamFlag readParamFlag;
    bool isStartReadParam,isStartReadCalibration, isMarch;
    uint32_t  paramCnt,lastParamCnt,readParamIndex,timeCount;

    ShowImage *showImage = nullptr;

    //PtrTask taskList;
    std::vector<float> tofCalibParam,tofCalibParamLast,recordCalibrationParam;

    /**/
    std::vector<app_bin_one_frame> _app_bin_op_t;
    app_bin_status                 _app_bin_status;


};

#endif // LIDAR_DATABASE_H
