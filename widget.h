#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include <QThread>
#include <Windows.h>
#include <set>
#include <QAbstractNativeEventFilter>
#include <dbt.h>
#include <QTimer>
#include <QTime>
#include <QFileDialog>
#include <ActiveQt/QAxObject>
#include <QDesktopServices>
#include <QUrl>

#include "serial_base.h"
#include "lidar_protocol.h"
#include "other_protocol.h"

#include "histogram_chart.h"
#include "greymap_chart.h"
#include "calibration_chart.h"
#include "pointclouds_chart.h"
#include "lidar_database.h"


QT_BEGIN_NAMESPACE
namespace Ui { class Widget; }
QT_END_NAMESPACE





class Widget : public QWidget, public QAbstractNativeEventFilter
{
    Q_OBJECT

public:
    enum {
        kTriMode = 0xA0,
        kTofMode = 0xA1,
        kSysParam = 0xA2,
        kMcuId = 0xA3,
        kVersionBuad = 0xA4,
        kMeasureRange = 0xA5,
        kCalibrationParam = 0xA6,
        kTgParam = 0xA7,
        kMaterialParam = 0xA8,
        kHisAndFacula = 0xA9,
        kTemperature = 0xAA,
        kRegisterSetting = 0xAB,
        kZeroAngleSetting = 0xAC,
        kDebugSetting4 = 0xBC,
        kDebugSetting3 = 0xBD,
        kDebugSetting2 = 0xBE,
        kDebugSetting = 0xBF,

        kSize,
    };
    /*协议cmd枚举*/
    enum {
            kH2D = 0x01,	/*host->device*/
            kD2H = 0X02,	/*device->host*/
            kHWS = 0x04,	/*host write success*/
            kHRS = 0x08,	/*host read success*/
            kHSS = 0x10,	/*host send success   check err is err*/
            kDS = 0x20,    /*device fixed frequency send*/
            kDNP = 0x40,    /*device not parse logic*/
    };

    /*模式与枚举*/
    enum{
            kTriStopMode                = 0xA5FF,
            kTriTestMode  				= 0xA5FE,
            kTriFaculaMode  			= 0xA5FD,
            kTriScanMode  				= 0xA5FC,
            ktRICalibrationMode   = 0xA5FB,

            kTofStopMode  				= 0xA5FF,
            kTofTestMode  				= 0xA5FE,
            kTofFaculaMode  			= 0xA5FD,
            kTofScanMode  				= 0xA5FC,
            kTofCalibrationMode   = 0xA5FB,
            kTofSize
    };


    typedef struct {
            uint8_t     isInitParam;
            uint8_t 	uID[12];
            uint16_t 	version;
            uint16_t	buadType;
            uint16_t    measureRange[2];
            float       calibrationP2;
            float       calibrationP1;
            float       calibrationP0;
            float 		tgCoefficientP2;
            float 		tgCoefficientP1;
            float 		tgCoefficientP0;
            float		materialConfficientP2;
            float		materialConfficientP1;
            float		materialConfficientP0;
            uint32_t	histogramAndSpadSampleFreq[2];
            uint16_t    currentTemperature;


    }PtrSysParam;


    typedef struct {
        int     pId;
        int     vId;
        QString portName;
    }SerialInfo;

    typedef struct {
        QString lastPort;
        QString lastBaud;
        bool isOpenSerial;
        SerialInfo  totalPort;
    }WidgetSerialInfomation;


    Widget(QWidget *parent = nullptr);
    ~Widget();
    void Setup();
    void closeEvent(QCloseEvent * event) {
        Q_UNUSED(event)
        emit QuitThreadLidar(true);
        emit QuitThreadOther(true);
    }
    bool nativeEventFilter(const QByteArray & eventType, void * message, long * result);
    void ShowCmd(void);
signals :
    void QuitThreadLidar(bool isClose);
    void TransmitDataLidar(QByteArray str);
    void OpenSerialDeviceLidar(bool isOpen, QString comPort, int buad);
    void InitSerialPtrLidar(bool isInit, QString threadName);
    void SetProtocolTypeLidar(int protocolType);

    void QuitThreadOther(bool isClose);
    void TransmitDataOther(QByteArray str);
    void OpenSerialDeviceOther(bool isOpen, QString comPort, int buad);
    void InitSerialPtrOther(bool isInit, QString threadName);
    void SetProtocolTypeOther(int protocolType);
    void SetAdminFuction(bool isEnable);
    void RecordData(bool isOk);

    //void transmitHistongramData(QByteArray tr);

private slots: 
    void ResizeButton();
    void OpenedLidar();
    void ClosedLidar();
    void LidarSendToMainPointCloud(std::vector<ProtocolData> data);
    //void FeedbackInfoLidar(QByteArray fdb);
    void OpenedOther();
    void ClosedOther();
    void OtherSendToMainPointCloud(std::vector<ProtocolData> data);
    void on_pushButton_clicked();
    void on_pushButton_2_clicked();
    //void FeedbackInfoOther(QByteArray fdb);
    //void IdSelectSlots();
    //void CmdSelectSlots();
    void timeOut();
    //void ReceviceCalibrationData(QVector<double> p);
    void on_comboBox_baud1_editTextChanged(const QString &arg1);

    void on_comboBox_baud1_currentIndexChanged(const QString &arg1);

    void on_comboBox_baud2_currentIndexChanged(const QString &arg1);

    //void on_pushButton_send_clicked();

    void on_pushButton_HISTONGRAM_clicked();

    void on_pushButton_GREYMAP_clicked();

    void on_pushButton_CALIBRATION_clicked();

    void on_pushButton_POINTCLOUD_clicked();

    void on_pushButton_DATABASE_clicked();

    //void on_pushButton_SAVE_PARAM_clicked();

    //void on_pushButton_LOAD_PARAM_clicked();



private:
    Ui::Widget *ui;
    SerialBase *lidarDevice = nullptr;
    SerialBase *otherDevice = nullptr;
    QThread *serialThreadLidar = nullptr;
    QThread *serialThreadOther = nullptr;
    bool isFirstDetect;
    std::vector<SerialInfo>  totalPort;
    std::vector<WidgetSerialInfomation> widgetSerialInfo;
    QTimer *timer = nullptr;
    int baudLidar,baudOther;
    PtrSysParam ptrSysParam;
    uint8_t tCmd,rCmd;
    uint8_t tId,rId;
    std::map<uint16_t,QString> modeMap;
    std::map<uint8_t,int> baudMap;

    HistogramChart *hisChartPtr = nullptr;
    GreymapChart *greyMapPtr = nullptr;
    CalibrationChart *calibrationPtr = nullptr;
    PointCloudsChart *pointCloudPtr = nullptr;
    LidarDatabase  *lidarDatabase = nullptr;

    uint  tableWidgetRowNum;
    std::map<QString,uint8_t>   tableRowCntMap;
    std::vector<uint8_t> IntegralItemRowCnt;/*每一项的累计和*/
    QRect pointCldRect,centerRect;
};








#endif // WIDGET_H

