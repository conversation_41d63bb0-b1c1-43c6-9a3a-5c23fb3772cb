#ifndef RECORD_POINTCLOUD_H
#define RECORD_POINTCLOUD_H

#include <QWidget>

namespace Ui {
class RecordPointCloud;
}

class RecordPointCloud : public QWidget
{
    Q_OBJECT

public:
    explicit RecordPointCloud(QWidget *parent = nullptr);
    ~RecordPointCloud();
signals:
    void StarRecord(QString fileName);
    void StopRecord(void);

private slots:
    void on_pushButtonStartRecord_clicked();

    void on_pushButtonStopRecord_clicked();

private:
    Ui::RecordPointCloud *ui;
};

#endif // RECORD_POINTCLOUD_H
