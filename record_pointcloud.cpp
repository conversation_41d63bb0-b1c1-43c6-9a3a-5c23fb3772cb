#include "record_pointcloud.h"
#include "ui_recordpointcloud.h"

RecordPointCloud::RecordPointCloud(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::RecordPointCloud)
{
    ui->setupUi(this);
}

RecordPointCloud::~RecordPointCloud()
{
    delete ui;
}

void RecordPointCloud::on_pushButtonStartRecord_clicked()
{
    QString fileName = ui->lineEditFileName->text();
    emit StarRecord(fileName);
    ui->labelProcess->setText("Recording...");
}

void RecordPointCloud::on_pushButtonStopRecord_clicked()
{
    emit StopRecord();
    ui->labelProcess->setText("Record!");
}
