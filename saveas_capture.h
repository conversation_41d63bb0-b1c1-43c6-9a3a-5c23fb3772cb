#ifndef SAVEAS_CAPTURE_H
#define SAVEAS_CAPTURE_H

#include <QWidget>
#include "qcpdocumentobject.h"
#include "customPlot/qcustomplot.h"

namespace Ui {
class SaveAsCapture;
}

class SaveAsCapture : public QWidget
{
    Q_OBJECT

public:
    explicit SaveAsCapture(QWidget *parent = nullptr);
    ~SaveAsCapture();
    void getCustomPlot(QCustomPlot *plot);

private slots:
    void on_pushButtonClearAll_clicked();

    void on_pushButtonSavePdf_clicked();

    void on_pushButtonInsert_clicked();

private:
    Ui::SaveAsCapture *ui;
    QCustomPlot *customPlot = nullptr;
    QCPDocumentObject *plotObjectHandler = nullptr;

};

#endif // SAVEAS_CAPTURE_H
