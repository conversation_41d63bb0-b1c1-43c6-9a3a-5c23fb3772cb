#ifndef CALIBRATION_CHART_H
#define CALIBRATION_CHART_H

#include <QMainWindow>
#include <QTime>
#include <QTimer>
#include "customPlot/qcustomplot.h"
#include "polynomialFit/polynomialFit.h"
#include "polynomialFit/augmentedMatrix.h"
#include "polynomialFit/linearFit.h"


namespace Ui {
class CalibrationChart;
}

typedef struct {
    uint currentStep;
    uint allStepsNum;
    QVector<uint>  settingDistance;
    QVector<uint>  gettingDistance;

}TRACK_CMD_LIST;


class CalibrationChart : public QMainWindow
{
    Q_OBJECT

public:
    explicit CalibrationChart(QWidget *parent = nullptr);
    ~CalibrationChart();
    void DefaultTaskList();
    void timerEvent(QTimerEvent *event);
    void SendTrackCmd(uint16_t dat);

public slots:
    void receiveCalibrationData(QByteArray rec);
    void FeedbackInfoLidar(QByteArray fdb);
    void onGetMousePos(QPoint pos);               //菜单 点击   获取当前位置
    void onMenuAction(QAction *act);              //事件操作
    void receiveHistDis(float dis);
    void ReceiveLidarInfo(std::vector<std::vector<float>> data);
signals:
    void TransmitCalibrationData(std::vector<float> p);
    void TransmitCmd(QByteArray data);
    void TransmitTestCmd(QByteArray data);
private slots:

    void on_pushButton_SAVE_LOG_clicked();

    void on_pushButton_CLEAR_LOG_clicked();

    void on_stopCMD_clicked();

    void on_resetCMD_clicked();

    void on_settingSpeed_returnPressed();

    void on_RunDis_clicked();

    void on_addSamplePoint_clicked();

    void on_exeRun_clicked();

    void on_samplePointDis_returnPressed();

    void on_offset_returnPressed();

    void on_exeNum_returnPressed();

    void on_saveFileName_returnPressed();

    void on_settingDistance_returnPressed();

    void on_lineEdit_FILE_NAME_returnPressed();

private:
    Ui::CalibrationChart *ui;

    QCPGraph *subGraphRando = nullptr;
    QCPGraph *subGraphRandoPoint = nullptr;
    //QCPItemText *wavePacketText = nullptr;
    QCPAxisRect *subRectLeft = nullptr;

    //QTimer *timer = nullptr;
    bool isStartSample;
    uint16_t sampleCnt,sampleIndex;
    QVector<float> sampleTdc;
    QVector<float> sampleAver;

    QVector<float> samplePeak;
    QVector<float> sampleAverPeak;

    Polynomial *pf = nullptr;

    uint   statisticsCount;
    std::vector<float>  tofMeadm;
    std::vector<float>  PeakMeadm;

    QFile file,trackFile;
    bool  isStartRecordFile;

    QVector<QCPGraphData>  sequenceData;
    uintmax_t   sequenceCnt;
    bool isSequence;

    int tempOffset;

    QMenu *RightClickMenu;                          //右键点击菜单
    QAction *deleteAction;                          //单行删除事件（如需要其它事件，可新定义）
    QAction *deleteMultiAction;                     //多行删除事件（如需要其它事件，可新定义）
    int     seletMouseRow;                          //选择到的表格的行数信息
    uint16_t totalRows,trackCnt;
    TRACK_CMD_LIST trackCmdList;
    bool    isExecuteStep;
    bool    isReached,isSaveTrackFile;
    uint m_timerId;
    uint lastOffset;
    std::vector<float> calibrationParam;

    QStandardItemModel *curItemModel = nullptr;




};

#endif // CALIBRATION_CHART_H
