#ifndef HISTONGRAM_CHART_H
#define HISTONGRAM_CHART_H

#include <QWidget>
#include <QDebug>
#include <QFile>
#include "customPlot/qcustomplot.h"


namespace Ui {
class histogramChart;
}

typedef struct {
    bool        kernel_enable;
    bool        raw_enable;
    uint8_t     parameters_size;
    uint8_t     parameters[16];
    float       threshold_duty;
    bool        output_enable;
    bool        derivative_enable;
    bool        output_centroid_enable;
    bool        output_intensity_enable;
    bool        output_pulse_integration_enable;
    bool        output_pulse_width_enable;
    float       centroid;
    uint32_t    intensity;
    uint32_t    intensity_integration;
    uint16_t    pulse_width;

}St_convolution_settings;

class HistogramChart : public QWidget
{
    Q_OBJECT

public:
    explicit HistogramChart(QWidget *parent = nullptr);
    ~HistogramChart();

public slots:
    void receiveHistogramData(QByteArray rec);
    void receiveHistType(uint8_t type);

signals:
    void sendHistDis(float dis);

private:
    Ui::histogramChart *ui;
    uint16_t  histogramBuff[1350];
    uint8_t convolutionType;
    St_convolution_settings St_convolution_settings_;

    QFile convolution_file;
    bool is_start_save_data_;


    QCustomPlot *customPlot = nullptr;
    QCPBars *fossil = nullptr;
    QCPAxis *keyAxis = nullptr;
    QCPAxis *valueAxis = nullptr;
    QCPItemText *wavePacketText = nullptr;

};

#endif // HISTONGRAM_CHART_H
