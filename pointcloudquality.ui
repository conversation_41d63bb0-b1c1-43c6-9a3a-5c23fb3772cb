<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PointCloudQuality</class>
 <widget class="QMainWindow" name="PointCloudQuality">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1208</width>
    <height>747</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="6,1">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout" stretch="3,1,2,2">
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="title">
           <string>测试配置</string>
          </property>
          <widget class="QWidget" name="verticalLayoutWidget_2">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>1051</width>
             <height>191</height>
            </rect>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2" stretch="5,1">
            <item>
             <widget class="QTableWidget" name="configTable">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <row>
               <property name="text">
                <string>段一</string>
               </property>
              </row>
              <row>
               <property name="text">
                <string>段二</string>
               </property>
              </row>
              <row>
               <property name="text">
                <string>段三</string>
               </property>
              </row>
              <row>
               <property name="text">
                <string>段四</string>
               </property>
              </row>
              <column>
               <property name="text">
                <string>板偏角(°)</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>板夹角(°)</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>板距离(mm)</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>采样帧数</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>采集角start(°)</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>采集角end(°)</string>
               </property>
              </column>
              <item row="0" column="0">
               <property name="text">
                <string>90</string>
               </property>
              </item>
              <item row="0" column="1">
               <property name="text">
                <string>65</string>
               </property>
              </item>
              <item row="0" column="2">
               <property name="text">
                <string>400</string>
               </property>
              </item>
              <item row="0" column="3">
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item row="0" column="4">
               <property name="text">
                <string>329.4</string>
               </property>
              </item>
              <item row="0" column="5">
               <property name="text">
                <string>30.6</string>
               </property>
              </item>
              <item row="1" column="0">
               <property name="text">
                <string>90</string>
               </property>
              </item>
              <item row="1" column="1">
               <property name="text">
                <string>85</string>
               </property>
              </item>
              <item row="1" column="2">
               <property name="text">
                <string>250</string>
               </property>
              </item>
              <item row="1" column="3">
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item row="1" column="4">
               <property name="text">
                <string>140</string>
               </property>
              </item>
              <item row="1" column="5">
               <property name="text">
                <string>220</string>
               </property>
              </item>
              <item row="2" column="0">
               <property name="text">
                <string>90</string>
               </property>
              </item>
              <item row="2" column="1">
               <property name="text">
                <string>145</string>
               </property>
              </item>
              <item row="2" column="2">
               <property name="text">
                <string>185</string>
               </property>
              </item>
              <item row="2" column="3">
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item row="2" column="4">
               <property name="text">
                <string>20</string>
               </property>
              </item>
              <item row="2" column="5">
               <property name="text">
                <string>50</string>
               </property>
              </item>
              <item row="3" column="0">
               <property name="text">
                <string>90</string>
               </property>
              </item>
              <item row="3" column="1">
               <property name="text">
                <string>145</string>
               </property>
              </item>
              <item row="3" column="2">
               <property name="text">
                <string>185</string>
               </property>
              </item>
              <item row="3" column="3">
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item row="3" column="4">
               <property name="text">
                <string>130</string>
               </property>
              </item>
              <item row="3" column="5">
               <property name="text">
                <string>160</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <spacer name="horizontalSpacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="testItemAdd">
                <property name="text">
                 <string>add</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="testItemDel">
                <property name="text">
                 <string>delete</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_Cal">
          <property name="text">
           <string>开始</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTableWidget" name="testTable">
          <row>
           <property name="text">
            <string>段一</string>
           </property>
          </row>
          <column>
           <property name="text">
            <string>测试项</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>规格</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>测试值</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>结果</string>
           </property>
          </column>
         </widget>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="logTextEdit"/>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QCustomPlot" name="plot" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
