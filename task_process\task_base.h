#ifndef TASK_BASE_H
#define TASK_BASE_H

#include <QObject>
#include <QTimer>
#include <QDebug>
#include <QVector>
#include <functional>

//class Task_base : public QObject
//{
//    Q_OBJECT
//public:
//    explicit Task_base(QObject *parent = nullptr);

//    void add_task(std::function<void()> task) {
//            tasks.push_back(task);
//    }

//    void start() {
//        currentIndex = 0;
//        execute_current_task();
//    }

//    void execute_current_task();


//    /*friend void task1_process(Task_base& object); // 声明为友元函数
//    friend void task2_process(Task_base& object); // 声明为友元函数
//    friend void task3_process(Task_base& object); // 声明为友元函数
//    friend void task4_process(Task_base& object); // 声明为友元函数

//    void task1_process_relate() {
//            task1_process(*this);
//    }
//    void task2_process_relate() {
//            task2_process(*this);
//    }
//    void task3_process_relate() {
//            task3_process(*this);
//    }
//    void task4_process_relate() {
//            task4_process(*this);
//    }*/

//private slots:
//    void handle_timeout();

//private:
//    std::vector<std::function<void()>> tasks;   // 存储任务的容器
//    uint currentIndex;                       // 当前任务索引
//    QTimer* timer;                   // 超时定时器



//signals:

//};

#endif // TASK_BASE_H
