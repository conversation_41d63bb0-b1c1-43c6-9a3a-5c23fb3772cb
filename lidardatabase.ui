<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LidarDatabase</class>
 <widget class="QMainWindow" name="LidarDatabase">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1116</width>
    <height>813</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1116</width>
    <height>787</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1116</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Database</string>
  </property>
  <property name="windowIcon">
   <iconset resource="logo2.qrc">
    <normaloff>:/new/prefix1/icon/logo/database.png</normaloff>:/new/prefix1/icon/logo/database.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	font: 75 9pt &quot;Calisto MT&quot;;
	/*font: 75 10pt &quot;Agency FB&quot;;*/
}

QLabel
{ 
	/*font: 25 10pt &quot;Microsoft YaHei&quot;;  */
	border-radius: 10px;	
}

QLineEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

QTextEdit
{ 
    border:2px solid gray;
    /*font: 25 10pt;*/ 
    border-radius:10px;
	
}

/*按钮静止无操作样式*/
QPushButton 
{
    /*background-color:blue;*/
    /*background-color: rgba(0, 102, 116, 240); 
    color:white; */
    border:2px solid gray;
    /*font: 25 10pt; */
    border-radius:10px;
}


 
/*鼠标悬停在按钮*/
QPushButton:hover
{
    background-color: gray; 
    color:rgb(6,168,255);
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QPushButton:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}




/* === QGroupBox === */
QGroupBox {
    border: 2px solid gray;
    margin-top: 2ex;
}

QGroupBox::title {
    color: rgba(0, 102, 116, 240);
    subcontrol-origin: margin;
    subcontrol-position: top left;
    margin-left: 5px;
}


/* === QRadioButton === */
QRadioButton {
	/*background-color: rgba(0, 102, 116, 240); */
	/*color: white; */
    /*border: 2px solid gray;*/
	border-radius:10px;
}
/* === QComboBox === */
QComboBox 
{
	color:rgb(0,0,0);
	/*background:rgba(0, 102, 116, 240);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:hover
{
    color:gray;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:pressed
{
    /*color:rgb(6,168,255);*/
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox:on
{
    /*color:rgb(6,168,255)*/
	/*color:rgb(255,255,255);*/;
    border: 2px solid gray;
    border-radius:10px;
}

QComboBox QAbstractItemView
{
	/*color:rgb(6,168,255);*/
	border: 2px solid gray;
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item
{
	/*color:rgb(6,168,255);*/
	border-radius:10px;
}
 
QComboBox QAbstractItemView::item:selected
{	
	color:rgb(255,255,255);
	background-color:gray;
}


/********QGroupBox*******/

QGroupBox 
{
	color:rgb(6,168,255);
    border: 2px solid gray;
    border-radius:10px;
}

/********QToolBox*******/
QToolBox::tab {
    color: white;
	background-color: rgba(0, 102, 116, 240);
	font: 25 18pt &quot;Agency FB&quot;;
    border: 2px solid gray;
    border-radius:10px;
}
QToolBoxButton 
{
    min-height: 40px; 
	text-align: right;
}

QToolBox::tab:hover
{
    color: gray;
	background-color: rgb(0, 170, 127);
	font: 25 18pt &quot;Agency FB&quot;; 
    border:2px solid gray;
    border-radius:10px;
}
 
/*鼠标按下按钮*/
QToolBox::tab:pressed
{
    background-color: gray; 
    color:white; 
    border:2px solid gray;
    border-radius:10px;
}



/*********/
 

</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTextEdit" name="textEditLog">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>310</y>
      <width>971</width>
      <height>211</height>
     </rect>
    </property>
    <property name="html">
     <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Calisto MT'; font-size:9pt; font-weight:72; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-family:'SimSun'; font-weight:400;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
   <widget class="QTableView" name="tableViewLidarInfomation">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>540</y>
      <width>971</width>
      <height>151</height>
     </rect>
    </property>
   </widget>
   <widget class="QTableView" name="tableViewLidarDatabase">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>710</y>
      <width>971</width>
      <height>181</height>
     </rect>
    </property>
   </widget>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>10</y>
      <width>971</width>
      <height>281</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="5,1">
     <item>
      <widget class="QGroupBox" name="groupBox_2">
       <property name="title">
        <string/>
       </property>
       <widget class="QRadioButton" name="radioButtonReserve3">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>80</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>vbd</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonCalibration">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>50</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Calibration</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonDebug1">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>20</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Peak2</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonDebug2">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>50</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Unlock</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReflectivity">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>110</y>
          <width>121</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reflectivity</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonTriMode">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>20</y>
          <width>121</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Trigonometry</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonZeroAngle">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>80</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>ZeroAngle</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonControlFreq">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>80</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>controlFreq</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonDebug3">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>80</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Boot</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonTG">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>80</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>TG</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonRegister">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>110</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Register</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonLed">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>50</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>led</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonMcuID">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>50</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>McuID</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonVersionBaud">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>50</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>VerBaud</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonTofMode">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>20</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>ToF</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonTemperature">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>110</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Temperature</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonDebug4">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>110</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>ChaHist</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonMeasureRange">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>20</y>
          <width>131</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>MeasureRange</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonLaser">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>20</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>laser</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>110</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Xtalk</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_2">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>150</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_3">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>180</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_4">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>210</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_5">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>240</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_6">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>180</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_7">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>150</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_8">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>210</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_9">
        <property name="geometry">
         <rect>
          <x>150</x>
          <y>240</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_10">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>180</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_11">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>150</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_12">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>210</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_13">
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>240</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_14">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>180</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_15">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>150</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_16">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>210</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_17">
        <property name="geometry">
         <rect>
          <x>460</x>
          <y>240</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_18">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>180</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_19">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>150</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_20">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>210</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
       <widget class="QRadioButton" name="radioButtonReserve4_21">
        <property name="geometry">
         <rect>
          <x>620</x>
          <y>240</y>
          <width>115</width>
          <height>19</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Reserve</string>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string/>
         </property>
         <widget class="QWidget" name="">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>20</y>
            <width>131</width>
            <height>101</height>
           </rect>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <item>
            <widget class="QRadioButton" name="radioButtonWrite">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>Write</string>
             </property>
             <property name="icon">
              <iconset resource="logo2.qrc">
               <normaloff>:/new/prefix1/icon/logo/write.png</normaloff>:/new/prefix1/icon/logo/write.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>30</width>
               <height>30</height>
              </size>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="radioButtonRead">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="inputMethodHints">
              <set>Qt::ImhNone</set>
             </property>
             <property name="text">
              <string>Read</string>
             </property>
             <property name="icon">
              <iconset resource="logo2.qrc">
               <normaloff>:/new/prefix1/icon/logo/read.png</normaloff>:/new/prefix1/icon/logo/read.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>30</width>
               <height>30</height>
              </size>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonTransmit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Transmit</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>450</y>
      <width>91</width>
      <height>41</height>
     </rect>
    </property>
    <property name="text">
     <string>CurrentLog</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_3">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>570</y>
      <width>111</width>
      <height>61</height>
     </rect>
    </property>
    <property name="text">
     <string>SettingLidar</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_4">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>760</y>
      <width>111</width>
      <height>41</height>
     </rect>
    </property>
    <property name="text">
     <string>LidarDatabase</string>
    </property>
   </widget>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>20</y>
      <width>133</width>
      <height>121</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>SettingRunMode</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="comboBoxRunningMode">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="editable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QProgressBar" name="progressBar_iap">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>350</y>
      <width>131</width>
      <height>36</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="styleSheet">
     <string notr="true">/*斑马线的设置*/
/*QProgressBar::chunk
{
text-align:center;
font-size:48px;
border-radius:11px;
background:qlineargradient(spread:pad,x1:0,y1:0,x2:1,y2:0,stop:0 #E07502,stop:1  #E07502);
border-radius:4px;
border:0.5px solid #26B4FF;
background-color:skyblue;
width:8px;margin:0.5px;
 
}
QProgressBar#progressBar
{
height:22px;
text-align:center;
font-size:48px;
color:white;
border-radius:11px;
background: #E07502 ;
}*/
QProgressBar {
    border: 2px solid grey;
    border-radius: 5px;
    text-align: center;/*文字的位置*/
}

QProgressBar::chunk {
    background-color: #05B8CC;
    width: 20px;/*进度条每隔的宽度*/
}

QProgressBar::chunk {
    background-color: #E07502;
    width: 10px;
    margin: 0.5px;/*刻度之间的间隔*/
}

</string>
    </property>
    <property name="value">
     <number>0</number>
    </property>
   </widget>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="iconSize">
    <size>
     <width>50</width>
     <height>50</height>
    </size>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionLCopy"/>
   <addaction name="actionDShowIamge"/>
   <addaction name="separator"/>
   <addaction name="actionDInsert"/>
   <addaction name="actionDIstImg"/>
   <addaction name="separator"/>
   <addaction name="actionDSubmit"/>
   <addaction name="actionDSubmitAll"/>
   <addaction name="separator"/>
   <addaction name="actionDRevert"/>
   <addaction name="actionDRevertAll"/>
   <addaction name="separator"/>
   <addaction name="actionDSchOnce"/>
   <addaction name="actionDSchAll"/>
   <addaction name="separator"/>
   <addaction name="actionOTA"/>
  </widget>
  <action name="actionLCopy">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/copy.png</normaloff>:/new/prefix1/icon/logo/copy.png</iconset>
   </property>
   <property name="text">
    <string>LCopy</string>
   </property>
   <property name="toolTip">
    <string>拷贝数据</string>
   </property>
  </action>
  <action name="actionDShowIamge">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/magnifier.png</normaloff>:/new/prefix1/icon/logo/magnifier.png</iconset>
   </property>
   <property name="text">
    <string>DShowImage</string>
   </property>
   <property name="toolTip">
    <string>显示图片</string>
   </property>
  </action>
  <action name="actionDSchOnce">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/searchOnce.png</normaloff>:/new/prefix1/icon/logo/searchOnce.png</iconset>
   </property>
   <property name="text">
    <string>DSchOnce</string>
   </property>
   <property name="toolTip">
    <string>查询</string>
   </property>
  </action>
  <action name="actionDSchAll">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/serachAll.png</normaloff>:/new/prefix1/icon/logo/serachAll.png</iconset>
   </property>
   <property name="text">
    <string>DSchAll</string>
   </property>
   <property name="toolTip">
    <string>查询所有</string>
   </property>
  </action>
  <action name="actionDSubmit">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/upLoad1.png</normaloff>:/new/prefix1/icon/logo/upLoad1.png</iconset>
   </property>
   <property name="text">
    <string>DSubmit</string>
   </property>
   <property name="toolTip">
    <string>提交一条</string>
   </property>
  </action>
  <action name="actionDSubmitAll">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/upLoad.png</normaloff>:/new/prefix1/icon/logo/upLoad.png</iconset>
   </property>
   <property name="text">
    <string>DSubmitAll</string>
   </property>
   <property name="toolTip">
    <string>提交所有</string>
   </property>
  </action>
  <action name="actionDInsert">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/insert.png</normaloff>:/new/prefix1/icon/logo/insert.png</iconset>
   </property>
   <property name="text">
    <string>DInsert</string>
   </property>
   <property name="toolTip">
    <string>插入一条数据</string>
   </property>
  </action>
  <action name="actionDIstImg">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/insertImage.png</normaloff>:/new/prefix1/icon/logo/insertImage.png</iconset>
   </property>
   <property name="text">
    <string>DIstImg</string>
   </property>
   <property name="toolTip">
    <string>插入图片</string>
   </property>
  </action>
  <action name="actionDRevert">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/reveret1.png</normaloff>:/new/prefix1/icon/logo/reveret1.png</iconset>
   </property>
   <property name="text">
    <string>DRevert</string>
   </property>
   <property name="toolTip">
    <string>撤销一条</string>
   </property>
  </action>
  <action name="actionDRevertAll">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/revertAll.png</normaloff>:/new/prefix1/icon/logo/revertAll.png</iconset>
   </property>
   <property name="text">
    <string>DRevertAll</string>
   </property>
   <property name="toolTip">
    <string>撤销所有操作</string>
   </property>
  </action>
  <action name="actionOTA">
   <property name="icon">
    <iconset resource="logo2.qrc">
     <normaloff>:/new/prefix1/icon/logo/download2.png</normaloff>:/new/prefix1/icon/logo/download2.png</iconset>
   </property>
   <property name="text">
    <string>OTA</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="logo2.qrc"/>
 </resources>
 <connections/>
</ui>
