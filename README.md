# WIN_BASE - Qt激光雷达应用程序

## 项目概述

WIN_BASE是一个基于Qt5的激光雷达数据处理和可视化应用程序，版本2.3.11。该项目提供了点云数据处理、动态校准、数据质量分析等功能。

## 开发环境要求

### 必需软件

1. **Qt 5.14.2**
   - 安装路径：`D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64`
   - 包含模块：Core, Gui, Widgets, SerialPort, PrintSupport, AxContainer, Sql

2. **MinGW 7.3.0 (64-bit)**
   - 安装路径：`D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64`
   - 编译器：gcc/g++ 7.3.0

3. **CMake 3.16+**
   - 用于项目构建管理

4. **Ninja (推荐)**
   - 快速构建工具
   - 可选，但推荐使用以提高构建速度

### 开发工具

- **Visual Studio Code**
  - 推荐的IDE
  - 已配置Qt5路径和编译器设置
  - 支持CMake项目

## 项目结构

```
winbase_app_0908/
├── CMakeLists.txt          # 主构建文件
├── README.md               # 项目说明文档
├── WinBase.pro            # 原qmake项目文件（已弃用）
├── main.cpp               # 程序入口
├── widget.cpp/h           # 主窗口
├── customPlot/            # 自定义绘图模块
│   ├── CMakeLists.txt
│   ├── qcustomplot.cpp/h
│   └── ...
├── polynomialFit/         # 多项式拟合模块
│   ├── CMakeLists.txt
│   ├── polynomialFit.cpp/h
│   └── ...
├── task_process/          # 任务处理模块
├── eigen-3.4.0/          # Eigen线性代数库
├── dll/                   # 动态链接库
├── resources/             # 资源文件
│   ├── logo.qrc
│   ├── logo2.qrc
│   └── res.qrc
└── build/                 # 构建输出目录
    ├── bin/               # 可执行文件和Qt依赖
    │   ├── WIN_BASE-V2_3_11_T5.exe
    │   ├── Qt5*.dll       # Qt库文件
    │   ├── platforms/     # Qt平台插件
    │   ├── imageformats/  # 图像格式插件
    │   └── ...
    ├── lib/               # 静态库文件
    └── CMakeFiles/        # CMake中间文件
```

## 构建说明

### 1. 环境准备

确保已安装上述必需软件，并且路径配置正确。

### 2. 使用CMake + MinGW + Ninja构建（推荐）

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目（使用Ninja生成器）
cmake -G Ninja ..

# 构建项目
ninja
```

### 3. 使用CMake + MinGW + Make构建

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 构建项目
mingw32-make -j4
```

### 4. VSCode中构建

1. 打开项目根目录
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 选择 `CMake: Configure`
4. 选择 `CMake: Build`

## 功能特性

- **点云数据处理**：支持激光雷达点云数据的导入、处理和分析
- **动态校准**：提供激光雷达动态校准功能
- **数据可视化**：使用QCustomPlot进行数据图表显示
- **质量分析**：点云数据质量评估和分析
- **数据库支持**：集成SQLite数据库存储
- **串口通信**：支持串口设备通信
- **多格式导出**：支持多种数据格式的导出

## 依赖库

- **Qt5**：GUI框架和核心功能
- **Eigen 3.4.0**：线性代数运算
- **QCustomPlot**：图表绘制
- **libdToF_calibration.a**：ToF校准静态库

## 自动部署

项目配置了Qt依赖自动部署功能：

- 构建完成后自动运行 `windeployqt`
- 自动复制必需的Qt库和依赖到 `build/bin/` 目录
- 支持debug和release模式部署
- 设置了正确的环境变量避免Qt版本冲突
- 可执行文件和所有依赖都在 `build/bin/` 目录中

### 手动部署（如果自动部署失败）

```bash
cd build/bin
$env:PATH = "D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin;D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin;" + $env:PATH
D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\windeployqt.exe --debug --compiler-runtime WIN_BASE-V2_3_11_T5.exe
```

## 开发注意事项

1. **编码**：项目使用UTF-8编码，支持中文注释
2. **编译器**：必须使用MinGW 7.3.0，不支持MSVC
3. **Qt版本**：严格要求Qt 5.14.2，避免版本冲突
4. **构建工具**：推荐使用Ninja以提高构建速度

## 故障排除

### 常见问题

1. **Qt找不到**
   - 检查Qt安装路径是否正确
   - 确认环境变量配置

2. **编译器错误**
   - 确认使用MinGW而非MSVC
   - 检查编译器路径配置

3. **依赖库缺失**
   - 运行windeployqt自动部署
   - 手动复制缺失的DLL文件

## 版本信息

- **当前版本**：2.3.11
- **Qt版本**：5.14.2
- **编译器**：MinGW 7.3.0
- **构建系统**：CMake 3.16+

## 许可证

[项目许可证信息]

## 联系方式

[开发团队联系信息]
