#include "show_image.h"
#include "ui_showimage.h"

ShowImage::ShowImage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::ShowImage)
{
    ui->setupUi(this);

}

ShowImage::~ShowImage()
{
    delete ui;
}

void ShowImage::RecQPixmap(QByteArray map, int w, int h)
{
    Q_UNUSED(w)
    Q_UNUSED(h)
    QPixmap  image;
    image.loadFromData(map);
    int ww = image.width();
    int hh = image.height();
    this->setFixedSize(ww, hh);
    this->setVisible(true);
    //this->setGeometry();
    this->setGeometry((qApp->desktop()->width() - ww)/2,(qApp->desktop()->height() - hh)/2,ww,hh);
    ui->labelImage->setPixmap(image);

}
