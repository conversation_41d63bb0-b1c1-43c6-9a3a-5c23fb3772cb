# customPlot library CMakeLists.txt

# Find Qt5 components needed for customPlot
find_package(Qt5 REQUIRED COMPONENTS
    Core
    Gui
    Widgets
    PrintSupport
)

# Source files for customPlot
set(CUSTOM_PLOT_SOURCES
    axistag.cpp
    mytracer.cpp
    qcpdocumentobject.cpp
    qcustomplot.cpp
)

# Header files for customPlot
set(CUSTOM_PLOT_HEADERS
    axistag.h
    mytracer.h
    qcpdocumentobject.h
    qcustomplot.h
)

# Create static library
add_library(customPlot STATIC
    ${CUSTOM_PLOT_SOURCES}
    ${CUSTOM_PLOT_HEADERS}
)

# Set include directories for this library
target_include_directories(customPlot PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link Qt5 libraries
target_link_libraries(customPlot
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::PrintSupport
)

# Enable Qt5 features for this library
set_target_properties(customPlot PROPERTIES
    AUTOMOC ON
)
