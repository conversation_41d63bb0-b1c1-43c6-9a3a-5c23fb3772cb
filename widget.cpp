#include "widget.h"
#include "QApplication"
#include "qlogging.h"
#include "ui_widget.h"

#include "lidar_protocol.h"
#include <iostream>

//#include <Eigen/Dense>
//#include <Eigen/Core>

void OutputMessage(QtMsgType type, const QMessageLogContext &context,
                   const QString &msg) {

  static QMutex mutex;
  mutex.lock();
  QString text;
  switch (type) {
  case QtDebugMsg:
    text = QString("[D]");
    break;

  case QtWarningMsg:
    text = QString("[W]");
    break;

  case QtCriticalMsg:
    text = QString("[C]");
    break;

  case QtFatalMsg:
    text = QString("[F]");
    break;
  default:
    break;
  }
  if (!msg.isEmpty()) {
    QString contextInfo = QString("[%1] [%2] [%3]")
                              .arg(QString(context.file))
                              .arg(QString(context.function))
                              .arg(context.line);
    QString currentDate_time =
        QDateTime::currentDateTime().toString("hh:mm:ss");
    QString currentDate =
        QDateTime::currentDateTime().toString("yyyy-MM-dd") + "_log.log";
    QString currentDateName = QString(">%1").arg(currentDate_time);

    QString message = QString("%1 %2 %3  %4")
                          .arg(currentDateName)
                          .arg(text)
                          .arg(contextInfo)
                          .arg(msg);
    QString logPath = qApp->applicationDirPath() + "/log/";
    QFile file(logPath + currentDate);
    QDir dir(logPath);
    if (!dir.exists()) {
      dir.mkpath(logPath);
    }

    file.open(QIODevice::WriteOnly | QIODevice::Append);
    QTextStream text_stream(&file);
    text_stream << message << "\r\n";
    file.flush();
    file.close();
    mutex.unlock();
  }
}

Widget::Widget(QWidget *parent)
    : QWidget(parent), ui(new Ui::Widget), isFirstDetect(true),
      baudLidar(115200), baudOther(115200), tCmd(0x05), rCmd(0), tId(0xA1),
      rId(0) {
//注册MessageHandler
#if (USING_LOG_TO_FILE == 1)
  qInstallMessageHandler(OutputMessage);
#endif
  qDebug() << USING_LOG_TO_FILE;

  widgetSerialInfo.resize(2);
  widgetSerialInfo.at(0).isOpenSerial = true;
  widgetSerialInfo.at(1).isOpenSerial = true;

  ui->setupUi(this);

  Setup();
#if (USING_CUSTOM_STYLE == 1)
  ui->pushButton_DATABASE->setVisible(false);
  ui->pushButton_GREYMAP->setVisible(false);
  ui->pushButton_HISTONGRAM->setVisible(false);
  ui->pushButton_CALIBRATION->setVisible(false);
  QTimer::singleShot(100, this, &Widget::ResizeButton);
#endif
  ui->comboBox_baud1->lineEdit()->setEchoMode(QLineEdit::Password);
}

Widget::~Widget() {
  delete ui;
  if (lidarDevice != nullptr) {
    delete lidarDevice;
  }
  if (otherDevice != nullptr) {
    delete otherDevice;
  }

  serialThreadLidar->quit();
  serialThreadLidar->wait();
  serialThreadOther->quit();
  serialThreadOther->wait();

  if (timer != nullptr) {
    delete timer;
  }

  if (hisChartPtr != nullptr) {
    delete hisChartPtr;
  }
  if (greyMapPtr != nullptr) {
    delete greyMapPtr;
  }
  if (calibrationPtr != nullptr) {
    delete calibrationPtr;
  }

  if (pointCloudPtr != nullptr) {
    delete pointCloudPtr;
  }

  if (lidarDatabase != nullptr) {
    delete lidarDatabase;
  }
}

void Widget::Setup() {
  // qDebug()<<"main tid:vSerialPort"<< QThread::currentThreadId();
  /*lidar device*/
  lidarDevice = new lidardevice::ProcessProtocol();
  serialThreadLidar = new QThread();
  lidarDevice->moveToThread(serialThreadLidar);
  serialThreadLidar->start();

  /*other device*/
  otherDevice = new otherdevice::ProcessProtocol();
  serialThreadOther = new QThread();
  otherDevice->moveToThread(serialThreadOther);
  serialThreadOther->start();

  /*注册时间滤波*/
  qApp->installNativeEventFilter(this);

  /*关联lidar协议线程*/
  connect(this, &Widget::QuitThreadLidar,
          (lidardevice::ProcessProtocol *)lidarDevice,
          &lidardevice::ProcessProtocol::QuitThread);
  connect(this, &Widget::SetProtocolTypeLidar,
          (lidardevice::ProcessProtocol *)lidarDevice,
          &lidardevice::ProcessProtocol::SetProtocolType);
  connect(this, &Widget::OpenSerialDeviceLidar, lidarDevice,
          &SerialBase::OpenSerialDevice);
  connect(this, &Widget::InitSerialPtrLidar, lidarDevice,
          &SerialBase::InitSerialPtr);
  connect(lidarDevice, &SerialBase::Opened, this, &Widget::OpenedLidar);
  connect(lidarDevice, &SerialBase::Closed, this, &Widget::ClosedLidar);
  connect((lidardevice::ProcessProtocol *)lidarDevice,
          &lidardevice::ProcessProtocol::ProcessSendToMainPointCloud, this,
          &Widget::LidarSendToMainPointCloud);
  connect(this, &Widget::RecordData,
          (lidardevice::ProcessProtocol *)lidarDevice,
          &lidardevice::ProcessProtocol::RecordData);
  emit InitSerialPtrLidar(true, "lidar");

  /*关联lidar 到histogram*/

  /*关联other协议线程*/
  connect(this, &Widget::QuitThreadOther,
          (otherdevice::ProcessProtocol *)otherDevice,
          &otherdevice::ProcessProtocol::QuitThread);
  connect(this, &Widget::TransmitDataOther,
          (otherdevice::ProcessProtocol *)otherDevice,
          &otherdevice::ProcessProtocol::TransmitData);
  connect(this, &Widget::SetProtocolTypeOther,
          (otherdevice::ProcessProtocol *)otherDevice,
          &otherdevice::ProcessProtocol::SetProtocolType);
  connect(this, &Widget::OpenSerialDeviceOther, otherDevice,
          &SerialBase::OpenSerialDevice);
  connect(this, &Widget::InitSerialPtrOther, otherDevice,
          &SerialBase::InitSerialPtr);
  connect(otherDevice, &SerialBase::Opened, this, &Widget::OpenedOther);
  connect(otherDevice, &SerialBase::Closed, this, &Widget::ClosedOther);
  connect((otherdevice::ProcessProtocol *)otherDevice,
          &otherdevice::ProcessProtocol::ProcessSendToMainPointCloud, this,
          &Widget::OtherSendToMainPointCloud);
  // connect((lidardevice::ProcessProtocol*)otherDevice,&otherdevice::ProcessProtocol::FeedbackInfo,this,&Widget::FeedbackInfoOther());
  emit InitSerialPtrOther(true, "Other");

  /*打开定时器用于判断反馈超时等逻辑*/
  /*
  timer = new QTimer();
  timer->start(10);
  connect(timer, &QTimer::timeout, this, &Widget::timeOut);
  */

  /*注册自定义类型*/
  qRegisterMetaType<std::vector<ProtocolData>>("std::vector<ProtocolData>");
  qRegisterMetaType<iap_ack_t>("iap_ack_t");

  /*波特率列表*/
  ui->comboBox_baud1->addItem("9600");
  ui->comboBox_baud1->addItem("115200");
  ui->comboBox_baud1->addItem("153600");
  ui->comboBox_baud1->addItem("230400");
  ui->comboBox_baud1->addItem("250000");
  ui->comboBox_baud1->addItem("460800");
  ui->comboBox_baud1->addItem("others");
  ui->comboBox_baud1->setCurrentIndex(3);

  ui->comboBox_baud2->addItem("115200");
  ui->comboBox_baud2->addItem("153600");
  ui->comboBox_baud2->addItem("230400");
}

void Widget::ResizeButton() {
  pointCldRect = ui->pushButton_POINTCLOUD->geometry();
  centerRect = ui->pushButton_HISTONGRAM->geometry();
  ui->pushButton_POINTCLOUD->setGeometry(QRect(centerRect.x() - 20, 10,
                                               centerRect.width() + 40,
                                               centerRect.height() + 40));
  // ui->horizontalLayout_3->setGeometry(QRect(170,1,85,85));
}

void Widget::on_comboBox_baud1_editTextChanged(const QString &arg1) {
#if (USING_CUSTOM_HIDE == 1)
  if (arg1 == "admin") {
    ui->pushButton_POINTCLOUD->setGeometry(pointCldRect);
    ui->pushButton_DATABASE->setVisible(true);
    ui->pushButton_GREYMAP->setVisible(true);
    ui->pushButton_HISTONGRAM->setVisible(true);
    ui->pushButton_CALIBRATION->setVisible(true);
    emit SetAdminFuction(true);
  } else if (arg1 == "record") {
    emit RecordData(true);
  } else if (arg1 == "close") {
    /*ui->pushButton_POINTCLOUD->setGeometry(QRect(centerRect.x()-20,10,centerRect.width()+40,centerRect.height()+40));
    ui->pushButton_DATABASE->setVisible(false);
    ui->pushButton_GREYMAP->setVisible(false);
    ui->pushButton_HISTONGRAM->setVisible(false);
    ui->pushButton_CALIBRATION->setVisible(false);*/
    emit RecordData(false);
  }
#endif
}

void Widget::timeOut() {}

bool Widget::nativeEventFilter(const QByteArray &eventType, void *message,
                               long *result) {
  MSG *msg = reinterpret_cast<MSG *>(message);
  int msgType = msg->message;

  if (isFirstDetect) { /*第一次打开时检测端口*/
    isFirstDetect = false;
    foreach (const QSerialPortInfo &info,
             QSerialPortInfo::availablePorts()) //遍历端口  并返回信息
    {
      SerialInfo si;
      si.pId = info.productIdentifier();
      si.vId = info.vendorIdentifier();
      si.portName = info.portName();
      totalPort.push_back(si);
    }
    ui->comboBox->clear();
    for (uint16_t n = 0; n < totalPort.size(); n++) {
      ui->comboBox->addItem(totalPort.at(n).portName);
      ui->comboBox_2->addItem(totalPort.at(n).portName);
    }
    ui->comboBox->setCurrentIndex(0);
    ui->comboBox_2->setCurrentIndex(0);
    // widgetSerialInfo.at(0).lastPort = ui->comboBox->currentText();
  }

  if (msgType == WM_DEVICECHANGE) {
    PDEV_BROADCAST_HDR lpdb = (PDEV_BROADCAST_HDR)msg->lParam;
    switch (msg->wParam) {
    case DBT_DEVICEARRIVAL:
      if (lpdb->dbch_devicetype == DBT_DEVTYP_PORT) {
        PDEV_BROADCAST_PORT lpdbv = (PDEV_BROADCAST_PORT)lpdb;
        QString port =
            QString::fromLocal8Bit(lpdbv->dbcp_name); /*插入的串口名*/
        /*lidar端口*/
        for (uint16_t m = 0; m < ui->comboBox->count(); m++) {
          if (ui->comboBox->itemText(m) == port) {
            break;
          }
          if (m == ui->comboBox->count() - 1) {
            ui->comboBox->addItem(port);
          }
        }
        if (ui->comboBox->count() == 0) {
          ui->comboBox->addItem(port);
        }

        if (port == widgetSerialInfo.at(0).lastPort) {
          ui->comboBox->setCurrentText(widgetSerialInfo.at(0).lastPort);
          emit OpenSerialDeviceLidar(true, widgetSerialInfo.at(0).lastPort,
                                     baudLidar);
        } else {
          /*设为第一个*/
          // ui->comboBox->setCurrentIndex(0);
        }
        /*other端口*/
        for (uint16_t m = 0; m < ui->comboBox_2->count(); m++) {
          if (ui->comboBox_2->itemText(m) == port) {
            break;
          }
          if (m == ui->comboBox_2->count() - 1) {
            ui->comboBox_2->addItem(port);
          }
        }
        if (ui->comboBox_2->count() == 0) {
          ui->comboBox_2->addItem(port);
        }

        if (port == widgetSerialInfo.at(1).lastPort) {
          ui->comboBox_2->setCurrentText(widgetSerialInfo.at(1).lastPort);
          emit OpenSerialDeviceOther(true, widgetSerialInfo.at(1).lastPort,
                                     baudOther);
        } else { /*设为第一个*/
                 // ui->comboBox_2->setCurrentIndex(0);
        }
      }
      break;
    case DBT_DEVICEREMOVECOMPLETE:
      if (lpdb->dbch_devicetype == DBT_DEVTYP_PORT) {
        PDEV_BROADCAST_PORT lpdbv = (PDEV_BROADCAST_PORT)lpdb;
        QString port =
            QString::fromLocal8Bit(lpdbv->dbcp_name); /*拔出的串口名*/
        qDebug() << "close port: " << port;
        totalPort.clear();
        foreach (const QSerialPortInfo &info,
                 QSerialPortInfo::availablePorts()) { /*遍历端口  并返回信息*/
          SerialInfo si;
          si.pId = info.productIdentifier();
          si.vId = info.vendorIdentifier();
          si.portName = info.portName();
          totalPort.push_back(si);
        }

        /*lidar端口*/ /*other端口*/
        ui->comboBox->clear();
        ui->comboBox_2->clear();
        for (uint16_t n = 0; n < totalPort.size(); n++) { /*显示端口信息*/
          ui->comboBox->addItem(totalPort.at(n).portName);
          ui->comboBox_2->addItem(totalPort.at(n).portName);
        }

        /*lidarl端口*/
        if (port == widgetSerialInfo.at(0).lastPort) {
          ui->comboBox->setCurrentIndex(0);
          emit OpenSerialDeviceLidar(false, widgetSerialInfo.at(0).lastPort,
                                     baudLidar);
        } else {
          /*设为第一个*/
          ui->comboBox->setCurrentText(widgetSerialInfo.at(0).lastPort);
        }
        /*other端口*/
        if (port == widgetSerialInfo.at(1).lastPort) {
          ui->comboBox_2->setCurrentIndex(0);
          emit OpenSerialDeviceOther(false, widgetSerialInfo.at(1).lastPort,
                                     baudOther);
        } else { /*设为第一个*/
          ui->comboBox_2->setCurrentText(widgetSerialInfo.at(1).lastPort);
        }
      }
      break;
    case DBT_DEVTYP_PORT:
      break;
    default:
      break;
    }
  }
  return QWidget::nativeEvent(eventType, message, result);
}
/*lidar协议线程反馈*/
void Widget::OpenedLidar() {
  widgetSerialInfo.at(0).isOpenSerial = false;
  ui->pushButton->setText("close");
}

void Widget::ClosedLidar() {
  widgetSerialInfo.at(0).isOpenSerial = true;
  ui->pushButton->setText("open");
}
/**/

/*other协议线程反馈*/
void Widget::OpenedOther() {
  widgetSerialInfo.at(1).isOpenSerial = false;
  ui->pushButton_2->setText("close");
}

void Widget::ClosedOther() {
  widgetSerialInfo.at(1).isOpenSerial = true;
  ui->pushButton_2->setText("open");
}
/**/

/*打开lidar协议端口*/
void Widget::on_pushButton_clicked() {
  if (widgetSerialInfo.at(0).isOpenSerial == true) {
    widgetSerialInfo.at(0).lastPort = ui->comboBox->currentText();
    emit OpenSerialDeviceLidar(true, widgetSerialInfo.at(0).lastPort,
                               baudLidar);
  } else {
    emit OpenSerialDeviceLidar(false, widgetSerialInfo.at(0).lastPort,
                               baudLidar);
  }
}

/*打开other协议端口*/
void Widget::on_pushButton_2_clicked() {
  if (widgetSerialInfo.at(1).isOpenSerial == true) {
    widgetSerialInfo.at(1).lastPort = ui->comboBox_2->currentText();
    emit OpenSerialDeviceOther(true, widgetSerialInfo.at(1).lastPort,
                               baudOther);
  } else {
    emit OpenSerialDeviceOther(false, widgetSerialInfo.at(1).lastPort,
                               baudOther);
  }
}

/*接收lidar点云数据并显示*/

void Widget::LidarSendToMainPointCloud(std::vector<ProtocolData> data) {
  Q_UNUSED(data)
  static QTime lastTimeMs;
  QTime startTime = QTime::currentTime();
  qDebug() << "used time... " << lastTimeMs.msecsTo(startTime);
  lastTimeMs = startTime;
}

/*接收other点云数据并显示*/
void Widget::OtherSendToMainPointCloud(std::vector<ProtocolData> data) {
  Q_UNUSED(data)
}

void Widget::on_comboBox_baud1_currentIndexChanged(const QString &arg1) {
  baudLidar = arg1.toInt();
}

void Widget::on_comboBox_baud2_currentIndexChanged(const QString &arg1) {
  baudOther = arg1.toInt();
}

void Widget::on_pushButton_HISTONGRAM_clicked() {
  if (hisChartPtr == nullptr) {
    hisChartPtr = new HistogramChart();
    hisChartPtr->show();
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::transmitHistogramData, hisChartPtr,
            &HistogramChart::receiveHistogramData);
    connect(lidarDatabase, &LidarDatabase::TransmitHistType, hisChartPtr,
            &HistogramChart::receiveHistType);
    connect(hisChartPtr, &HistogramChart::sendHistDis, calibrationPtr,
            &CalibrationChart::receiveHistDis);
    void sendHistDis(float dis);
  } else {
    hisChartPtr->setWindowFlags(
        Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint |
        Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    hisChartPtr->show();
  }
}

void Widget::on_pushButton_GREYMAP_clicked() {
  if (greyMapPtr == nullptr) {
    greyMapPtr = new GreymapChart();
    greyMapPtr->show();
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::transmitGreymapData, greyMapPtr,
            &GreymapChart::receiveGreymapData);
  } else {
    greyMapPtr->setWindowFlags(Qt::CustomizeWindowHint |
                               Qt::WindowMinMaxButtonsHint |
                               Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    greyMapPtr->show();
  }
}

void Widget::on_pushButton_CALIBRATION_clicked() {
  if (calibrationPtr == nullptr) {
    calibrationPtr = new CalibrationChart();
    calibrationPtr->show();
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::transmitCalibrationData,
            calibrationPtr, &CalibrationChart::receiveCalibrationData);
    connect(calibrationPtr, &CalibrationChart::TransmitCalibrationData,
            lidarDatabase, &LidarDatabase::ReceviceCalibrationData);
    connect((otherdevice::ProcessProtocol *)otherDevice,
            &otherdevice::ProcessProtocol::FeedbackInfo, calibrationPtr,
            &CalibrationChart::FeedbackInfoLidar);
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::FeedbackInfo, calibrationPtr,
            &CalibrationChart::FeedbackInfoLidar);
    connect(calibrationPtr, &CalibrationChart::TransmitCmd,
            (otherdevice::ProcessProtocol *)otherDevice,
            &otherdevice::ProcessProtocol::TransmitData);
    connect(calibrationPtr, &CalibrationChart::TransmitTestCmd,
            (lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::TransmitData);
    //            connect(lidarDatabase, &LidarDatabase::FeedbackInfoTmp
    //            ,calibrationPtr,&CalibrationChart::FeedbackInfoLidar);
  } else {
    calibrationPtr->setWindowFlags(
        Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint |
        Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    calibrationPtr->show();
  }
}

void Widget::on_pushButton_POINTCLOUD_clicked() {
  if (pointCloudPtr == nullptr) {
    pointCloudPtr = new PointCloudsChart();
    pointCloudPtr->show();
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::transmitPointCloudData,
            pointCloudPtr, &PointCloudsChart::ReceivePointCloudData);
    connect(pointCloudPtr, &PointCloudsChart::TransmitSerialCmd,
            (lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::TransmitData);
    connect(this, &Widget::SetAdminFuction, pointCloudPtr,
            &PointCloudsChart::SetAdminFuction);

    connect(pointCloudPtr, &PointCloudsChart::TransmitLidarInfo, calibrationPtr,
            &CalibrationChart::ReceiveLidarInfo);

  } else {
    pointCloudPtr->setWindowFlags(
        Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint |
        Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    pointCloudPtr->show();
  }
}

void Widget::on_pushButton_DATABASE_clicked() {
  if (lidarDatabase == nullptr) {
    lidarDatabase = new LidarDatabase();
    lidarDatabase->show();
    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::FeedbackInfo, lidarDatabase,
            &LidarDatabase::FeedbackInfoLidar);
    connect(lidarDatabase, &LidarDatabase::TransmitDataLidar,
            (lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::TransmitData);
    connect(pointCloudPtr, &PointCloudsChart::TransmitCalibrationData,
            lidarDatabase, &LidarDatabase::ReceviceCalibrationData);
    connect(calibrationPtr, &CalibrationChart::TransmitCalibrationData,
            lidarDatabase, &LidarDatabase::ReceviceCalibrationData);

    connect((lidardevice::ProcessProtocol *)lidarDevice,
            &lidardevice::ProcessProtocol::sig_iap_ack, lidarDatabase,
            &LidarDatabase::sig_iap_ack);

  } else {
    lidarDatabase->setWindowFlags(
        Qt::CustomizeWindowHint | Qt::WindowMinMaxButtonsHint |
        Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
    lidarDatabase->show();
  }
}

/*static char ConvertHexChar(char ch)
{
    if((ch >= '0') && (ch <= '9'))
        return ch-0x30;
    else if((ch >= 'A') && (ch <= 'F'))
        return ch-'A'+10;
    else if((ch >= 'a') && (ch <= 'f'))
        return ch-'a'+10;
    else return ch-ch;//不在0-f范围内的会发送成0
}
*/

int samsung_calc_bound(std::vector<float> angle, std::vector<float> dis,
                       std::vector<float> intensity) {
  if (angle.size() != dis.size() || dis.size() != intensity.size()) {
    return -1;
  }

  std::vector<float> data_diference_value;
  const int bound_threshold = 180;
  const int initial_threshold = 40;
  std::vector<float> bound_index;
  std::vector<float> bound_value;
  int is_neg = 0;
  int is_pos = 0;
  std::vector<float> initial_index;
  std::vector<float> initial_value;
  int is_initial_neg = 0;
  int is_initial_pos = 0;
  int diff_value = 0;

  for (uint m = 0; m < dis.size(); m += 2) {
    if (m + 2 > dis.size()) {
      if (m + 2 == dis.size() + 1) {
        diff_value = dis[1] - dis[m];
      } else if (m + 2 == dis.size() + 2) {
        diff_value = dis[2] - dis[m];
      }
      // check bound
      if (diff_value < -bound_threshold && is_neg == 0) {
        bound_index.push_back(m);
        is_neg = 1;
        is_pos = 0;
      } else if (diff_value > bound_threshold && is_pos == 0) {
        bound_index.push_back(m + 2);
        is_neg = 0;
        is_pos = 1;
      }
      data_diference_value.push_back(diff_value);

      int bound_size = bound_index.size();
      if (bound_size != 12) {
        // disp("search bound is error!");
      } else {
        int angle_size = angle.size();
        int ring_angle_index = 1;
        if (bound_index[11] > angle_size) {
          ring_angle_index = bound_index[11] - angle_size;
        } else {
          ring_angle_index = bound_index[11];
        }
        bound_value.push_back(bound_index[0]);
        bound_value.push_back(bound_index[3]);
        bound_value.push_back(bound_index[4]);
        bound_value.push_back(bound_index[7]);
        bound_value.push_back(bound_index[8]);
        bound_value.push_back(bound_index[ring_angle_index]);

        if (bound_value[1] - bound_value[0] > 30 ||
            bound_value[3] - bound_value[2] > 30 ||
            bound_value[5] - bound_value[4] > 30) {
          // disp("bound is too large!");
        }
      }
      //
      int initial_size = initial_index.size();

      if (initial_size != 2) {
        // disp("initial angle is error!");
      } else {
        if ((angle[initial_index[2]] > 108) &&
            (angle[initial_index[1]] < 180) &&
            ((angle[initial_index[2]] + angle[initial_index[1]]) / 2.0 > 170) &&
            ((angle[initial_index[2]] + angle[initial_index[1]]) / 2.0 < 180)) {
          initial_value.push_back(
              (angle[initial_index[2]] + angle[initial_index[1]]) / 2.0);
        } else {
          // disp("initial angle range is error!")
        }
      }

      //
      bound_index.clear();
      initial_index.clear();
      break;
    }
    //
    diff_value = dis[m + 2] - dis[m];
    data_diference_value.push_back(diff_value);
    //  check bound
    if (diff_value < -bound_threshold && is_neg == 0) {
      bound_index.push_back(m);
      is_neg = 1;
      is_pos = 0;
    } else if (diff_value > bound_threshold && is_pos == 0) {
      bound_index.push_back(m + 2);
      is_neg = 0;
      is_pos = 1;
    }
    // initial
    if (diff_value < -initial_threshold && diff_value > -100 &&
        is_initial_neg == 0 && is_neg == 0 && is_pos == 1 &&
        m > dis.size() / 4) {
      initial_index.push_back(m);
      is_initial_neg = 1;
      is_initial_pos = 0;
    } else if (diff_value > initial_threshold && diff_value < 100 &&
               is_initial_pos == 0 && is_neg == 0 && is_pos == 1 &&
               m < 3 * dis.size() / 4) {
      initial_index.push_back(m + 2);
      is_initial_neg = 0;
      is_initial_pos = 1;
    }
  }

  return 1;
}
