#ifndef OTHERPROTOCOL_H
#define OTHERPROTOCOL_H

#include <QObject>
#include "serial_base.h"
#include <QDebug>
#include <QTime>
#include <Windows.h>
#include <QAbstractNativeEventFilter>
#include <dbt.h>
#include <math.h>


namespace  otherdevice{

    class ProcessProtocol : public SerialBase {
        Q_OBJECT
    public:
        enum ProtocolType {
          kNarwal = 0,
          kSamsung,
          kCoin,
          kOther
        };

        enum NarwalHead {
          kFrameHead1 = 0xA5,
          kFrameHead2 = 0x5A,
          kFrameHead3 = 0xAA
        };
        enum NarwalStatus {
          kParseData1 = 1,
          kParseData2 = 2,
          kParseData3 = 3,
          kParseNone
        };


        ProcessProtocol();
        ~ProcessProtocol();
        void ParseProtocol();
    signals:
        void ProcessSendToMainPointCloud(std::vector<ProtocolData> data);
        void FeedbackInfo(QByteArray fdb);
        void transmitHistogramData(QByteArray tr);
        void transmitGreymapData(QByteArray tr);
        void transmitCalibrationData(QByteArray tr);
        void transmitPointCloudData(std::vector<ProtocolData> data);
        void TransmitTemperature(float temp);

    public slots:
        void QuitThread(bool isClose);
        void TransmitData(QByteArray str);
        void SetProtocolType(int protocolType);
        void RecordData(bool isOk);

    private:
        bool isQuitThread;
        QByteArray ringBuff;
        int protocolTyp;
        ProtocolData protocolData;
        std::vector<ProtocolData> vprotocolData;
        uint8_t ver;
        uint16_t health;
        uint16_t voltage;

        uint checkSumCnt;
        bool isRecordDataToDisk;
};
}

#endif // NARWALPROTOCOL_H
