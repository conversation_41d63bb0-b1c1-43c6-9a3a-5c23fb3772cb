#ifndef GREYMAP_CHART_H
#define GREYMAP_CHART_H

#include <QMainWindow>
#include <QDebug>

namespace Ui {
class GreymapChart;
}

class GreymapChart : public QMainWindow
{
    Q_OBJECT

public:
    explicit GreymapChart(QWidget *parent = nullptr);
    ~GreymapC<PERSON>();

public slots:
    void receiveGreymapData(QByteArray rec);


private:
    Ui::GreymapChart *ui;
    uint32_t  lastSpadCount;
    uint16_t  lastSpadRow,lastSpadColumn;
};

#endif // GREYMAP_CHART_H
