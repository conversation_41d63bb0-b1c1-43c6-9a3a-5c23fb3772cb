﻿//#include "lidar_protocol.h"
//#include <QThread>
//#include <QFile>
//#include <QApplication>

//std::map<uint16_t,QString> ddsSensorCode {
//    {0x0001,"spi error!"},//0
//    {0x0002,"vbd calculate"},//1
//    {0x0004,"sample temperature error"},//2
//    {0x0008,"greymap register error"},//3
//    {0x0010,"mp read error"},//4
//    {0x0020,"greymap interrupt error"},//5
//    {0x0040,"inter firmware error"},//6
//    {0x0080,"ranging register error"},//7
//    {0x0100,"stop configure error"},//8
//    {0x0200,"fps configure error"},//9
//    {0x0400,"start configure error"},//10
//    {0x0800,"encoder error"},//11
//    {0x1000,"tdc clock phase error"},//12
//    {0x2000,"laser error"},//13
//    {0x4000,"voltage error"},//14
//    {0x8000,"ranging interrupt error"},//15
//};

//#include "sys/time.h"
//namespace lidardevice {

//    void Sleep(int msec)
//    {
//        QTime dieTime = QTime::currentTime().addMSecs(msec);
//        while( QTime::currentTime() < dieTime )
//        QCoreApplication::processEvents(QEventLoop::AllEvents, 2000);
//    }


//    ProcessProtocol::ProcessProtocol() : isQuitThread(false)
//        , protocolTyp(0)
//        ,ver(0)
//        ,voltage(0)
//        ,health(0)
//        ,checkSumCnt(0)
//        ,isRecordDataToDisk(false)
//    {

//    }
//    ProcessProtocol::~ProcessProtocol()
//    {

//    }

//    void ProcessProtocol::QuitThread(bool isClose)
//    {
//        isQuitThread = isClose;
//        qDebug()<<thName<<": QuitThread id"<< QThread::currentThreadId();
//    }

//    void ProcessProtocol::ParseProtocol()
//    {
//        //qDebug()<<thName<<": ParseProtocol id"<< QThread::currentThreadId();
//        int parseDataStatus = kParseNone;

//        QByteArray tmp_data = GetSerialPtr()->readAll();
//        /*if(isRecordDataToDisk == false) {
//            static QTime lastTimeMs;
//            QTime startTime = QTime::currentTime();

//            QFile file("RecordData.txt");
//            file.open(QIODevice::WriteOnly | QIODevice::Append);
//            QTextStream text_stream(&file);
//            text_stream << lastTimeMs.msecsTo(startTime) <<" :"<<tmp_data.toHex(' ') << "\r\n";
//            file.flush();
//            file.close();
//            lastTimeMs = startTime;
//        }*/

//        /*粘包*/
//        ringBuff += tmp_data;//GetSerialPtr()->readAll();
//        /*获取长度*/
//        int length = ringBuff.length();


//        /*转化为uint8_t 数组*/
//        uint8_t *data = (uint8_t*)ringBuff.data();
//        /*解析数据*/
//        for(int n=0; n<length; n++) {
//            /*剩余长度*/
//            int leftDataNum = length - n;
//            /*获取头标识*/

//            if(data[n] == kFrameHead1) {
//                if(leftDataNum < 6) {/*小于获取长度信息的索引值 继续接收数据*/
//                    return;
//                }
//                parseDataStatus = kParseData1;
//            }
//            else if(data[n] == kFrameHead2) {
//                if(leftDataNum < 5) {/*小于获取长度信息的索引值 继续接收数据*/
//                    return;
//                }
//                parseDataStatus = kParseData2;
//            }
//            else if(data[n] == kFrameHead3) {
//                if(leftDataNum < 4) {/*小于获取长度信息的索引值 继续接收数据*/
//                    return;
//                }
//                parseDataStatus = kParseData3;
//            }
//            else {
//                parseDataStatus = kParseNone;
//            }

//            /*判断数据长度与解析数据*/
//            switch(parseDataStatus) {
//                case kParseData1 :
//                    //qDebug() << "entering parse 1...";
//                    //header(1) + cmd(1) + id(1) + checkXor(1) + byteNumber(2) + data(n)
//                    //header(1) + 0x5a(1) + num(2) + sum(2) + type(1) + data(n)
//                    //header(1) + 0x5a(1) + 0x04(1) + 0x00(1) + 保留(2) + 0x06(1) + xor(1) + MSB(1) + LSB(1) + MSB(1)
//                    if(data[n+1] == 0x5A && data[n+6] == 0x01) {/*获取雷达信息*/
//                        int dataNum = data[n+3]<<8 | data[n+2];
//                        int frameNum = 7 + dataNum;
//                        if(leftDataNum < frameNum) {/*长度不够获取起始包*/
//                            qDebug() << "lidar information return!";
//                            return;
//                        }

//                        uint16_t checkSum = 0,check = data[n+5]<<8 | data[n+4];
//                        for(int m=0; m<frameNum; m++) {
//                            if(m == 4 || m == 5) {
//                                continue;
//                            }
//                            checkSum += data[n+m];
//                        }
//                        if(checkSum == check) {
//                            /*get lidar device info*/
//                            //qDebug() << "current version: " << data[n+26];
//                            ver = data[n+26];
//                            ringBuff.remove(0,n+frameNum);
//                            qDebug() << "lidar information!";
//                        }
//                        else {
//                            ringBuff.remove(0,n+7);
//                            qDebug() << "lidar information check err!";
//                        }


//                    }
//                    else if(data[n+1] == 0x5A && data[n+2] == 0x04) {
//                        if(leftDataNum < 11) {/*长度不够获取起始包*/
//                            //qDebug() << "lidar health code return!";
//                            return;
//                        }
//                        uint16_t tmpVoltage = 0;
//                        if(((data[n+4]<<8) + data[n+5]) == 0) {
//                            tmpVoltage = 0;
//                        }
//                        else {
//                            tmpVoltage = ~(((data[n+4]<<8) + data[n+5]) + 122);
//                        }


//                        voltage = tmpVoltage;
//                        QByteArray volCode;

//                        health = data[n+9] | (data[n+10]<<8);
//                        volCode.append(data[n+10]);
//                        volCode.append(data[n+9]);

//                        ringBuff.remove(0,n+11);
//                        if(health != 0) {
//                            QString tmp;

//                            for(uint t=0; t<16; t++) {
//                                if((health&(1<<t)) != 0) {
//                                    tmp += ddsSensorCode.at((1<<t))+" | ";
//                                }
//                            }
//                            qWarning() << "DDS Code! " << tmp;
//                        }

//                    }
//                    else if((data[n+1]&0x02) == 0x02 && (data[n+1]&0x01) == 0x00 && (data[n+1]&0xC0) == 0x00) {/*获取不同的应答信息  根据cmd和id区分...*/
//                        int dataNum = data[n+5]<<8 | data[n+4];
//                        int frameNum = 6 + dataNum*2;
//                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
//                            /*1:长度不够获取整个数据包*/
//                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
//                            /*3:当长度异常并且过长的数据抛掉*/
//                            if(frameNum > 3000+6+10) {//2048+6+10
//                                ringBuff.remove(0,n+2);
//                                qDebug() << "lidar interaction callback return!";
//                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
//                            }
//                            return;

//                        }

//                        uint8_t checkSum = 0,check = data[n+3];
//                        for(int m=0; m<frameNum; m++) {
//                            if(m != 3) {
//                                checkSum ^= data[n+m];
//                            }
//                        }
//                        //qDebug()<<"receive interactions";
//                        if(checkSum == check) {
//                            /*get lidar device info*/
//                            if((data[n+1]&0x22) == 0x22) {
//                                QByteArray str;
//                                if(data[n+2] == 0xAC) {
//                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
//                                    transmitHistogramData(str);
//                                }
//                                else if(data[n+2] == 0xAD) {
//                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
//                                    qDebug() << "rec greymap size : " << str.size();
//                                    transmitGreymapData(str);
//                                }
//                                else if(data[n+2] == 0xAE) {
//                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
//                                    transmitCalibrationData(str);
//                                }
//                            }
//                            else {
//                                QByteArray str;
//                                //str.push_back(data[n+0]);
////                                str.push_back(data[n+1]);/*cmd*/
////                                str.push_back(data[n+2]);/*id*/
////                                str.append((char*)&data[n+6],dataNum*2);/*data*/

//                                str.append((char*)&data[n],6+dataNum*2);
//                                FeedbackInfo(str);
//                            }

//                            //transmitGreymapData(str);
//                            //transmitCalibrationData(str);


//                            ringBuff.remove(0,n+frameNum);

//                            //qDebug() << "lidar interaction callback!";
//                        }
//                        else {
//                            checkSumCnt++;
//                            qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
//                            qDebug() << "lidar interaction callback check err!";
//                            ringBuff.remove(0,n+6);
//                        }

//                    }
//                    else if(data[n+1] == 0x01 ) {
//                        uint8_t sum = 0;
//                        uint8_t check =0;
//                        /*sum = 0xA5  + 0x01 + data[n+2] + data[n+3];
//                        if(sum == data[n+4]) {
//                            QByteArray str;

//                            str.append((char*)&data[n+2],2);
//                            transmitCalibrationData(str);
//                            ringBuff.remove(0,n+5);
//                        }
//                        else {
//                            ringBuff.remove(0,n+5);
//                        }

//                        check = uint8_t(~sum);*/
//                        sum = 0xA5  + 0x01 + data[n+2] + data[n+3]+ data[n+4] + data[n+5];
//                        if(sum == data[n+6]) {
//                            QByteArray str;

//                            str.append((char*)&data[n+2],4);
//                            transmitCalibrationData(str);
//                            ringBuff.remove(0,n+7);
//                        }
//                        else {
//                            ringBuff.remove(0,n+7);
//                        }
//                    }
//                    else {

//                        qDebug() << "n: " << n <<  ringBuff.toHex() ;
//                        ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/
//                        //qDebug() << "remove header";
//                    }
//                    n = -1;
//                    length = ringBuff.length();
//                    //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/

//                    break;
//                case kParseData2 :
////                    if(data[n+1] == 0x34 && data[n+2] == 0x81 && data[n+3] == 0x28 && data[n+4] == 0x00) {
////                        uint16_t sum = 0;
////                        uint8_t check =0;
////                        for(int m=1; m<0x28+5; m++) {
////                            sum += data[n+m];
////                        }
////                        check = uint8_t(~sum);

////                        if(check == data[n+5+0x28]) {
////                            //qDebug()<<"5300!";
////                            QByteArray str;
////                            str.append((char*)&data[n],46);
////                            transmitCalibrationData(str);
////                            ringBuff.remove(data[n],n+6+0x28);
////                        }
////                        else {
////                            ringBuff.remove(0,5);
////                        }
////                    }


//                    break;
//            case kParseData3 :
//                   if(data[n+1] == 0x55 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
//                       int dataNum = data[n+3];
//                       int frameNum = 10 + dataNum*3;

//                       if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
//                           /*1:长度不够获取整个数据包*/
//                           /*2:当长度异常但数据比较短则通过校验方式抛掉*/
//                           /*3:当长度异常并且过长的数据抛掉*/
//                           if(frameNum > 1024) {
//                               ringBuff.remove(0,n+2);
//                               qDebug() << "lidar scan return!";
//                               return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
//                           }
//                           return;

//                       }
//                       else {
//                           uint16_t check = data[n+9]<<8 | data[n+8];
//                           uint8_t checkSumH = 0,checkSumL = 0;
//                           checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
//                           checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
//                           for(int m=0; m<dataNum*3; m=m+3) {
//                               /*进行异或校验提取*/
//                               checkSumL ^= data[n+10+m]^data[n+10+m+1];
//                               checkSumH ^= data[n+10+m+2];
//                           }
//                           if((checkSumH<<8|checkSumL) == check) {
//                               /*get lidar pointcloud info*/
//                               float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
//                               float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
//                               double incAngle = 0;
//                               bool isParse = true;
//                               //qDebug()<< " "<< startAngle << " " << endAngle;
//                               //2.0 计算角度
//                               if(dataNum != 0)
//                               {
//                                   //qDebug() << startAngle << " " << endAngle;
//                                   if(startAngle > endAngle)
//                                   {
//                                       incAngle = (360 - startAngle + endAngle)/(dataNum-1);
//                                   }
//                                   else
//                                   {
//                                       incAngle = (endAngle - startAngle)/((dataNum-1) == 0 ? 1:(dataNum-1));
//                                   }

//                                   if(endAngle < startAngle && endAngle > 26) {
//                                          isParse = false;
//                                          qDebug() << "nn: " << n << " "; //ringBuff.toHex() ;
//                                   }
//                               }
//                               if(isParse == true) {
//                                   if((data[n+2]&0x01) == 0x01) {/*起始包*/
//                                       //qDebug()<<" ";
//                                       //2.1 计算距离
//                                       double spd = (data[n+2]>>1)/10.0;
//                                       for(int m=0; m<dataNum; m++)
//                                       {
//                                           quint16 down= (data[n+10+3*m+1]>>2)&0x3f;
//                                           uint16_t up = 0x00ff&data[n+10+3*m+2];//左移高位会自动补1
//                                           up = up<<6;

//                                           float tmpdepth = up | down;

//                                           //tmpdepth = sqrt(tmpdepth*tmpdepth - 15.6*64.0);
//                                           //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
//                                           float tmpAngle = startAngle ;
//                                           //qDebug()<< "start_angle: "<<tmpAngle;
//                                           uint tmpIndensity = ((data[n+10+3*m+1]&0x03)<<6) | (((data[n+10+3*m])>>2)&0x3f);
//                                           tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
//                                           PolarData dat;
//                                           dat.angle = tmpAngle;
//                                           dat.deepth = tmpdepth;
//                                           dat.indensity = tmpIndensity;
//                                           dat.HrDenote = data[n+10+3*m]&0x01;
//                                           protocolData.speed = spd;
//                                           protocolData.version = ver;
//                                           protocolData.mcuVoltage = voltage;
//                                           protocolData.healthCode = health;
//                                           protocolData.data.push_back(dat);
//                                           vprotocolData.push_back(protocolData);


//                                           /*struct timeval tv;
//                                           struct timezone tz;
//                                           static double time_ms_last;

//                                           gettimeofday(&tv, &tz); //获取当前时间
//                                           double time_ms = (double)tv.tv_usec/1000.0;
//                                           double delta_time = time_ms-time_ms_last;
//                                           if(delta_time < 0) {
//                                               delta_time += 1000;
//                                           }
//                                           qDebug() << delta_time;
//                                           time_ms_last = time_ms;*/

//                                           //qDebug() << "total size: " <<protocolData.data.size();
//                                           emit transmitPointCloudData(vprotocolData);
//                                           //emit ProcessSendToMainPointCloud(vprotocolData);
//                                           protocolData.data.clear();
//                                           protocolData.speed = 0;

//                                           vprotocolData.clear();
//                                           //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
//                                       }
//                                   }
//                                   else {/*点云包*/
//                                   //2.1 计算距离
//                                   for(int m=0; m<dataNum; m++)
//                                   {
//                                       quint16 down= (data[n+3*m+1+10]>>2)&0x3f;
//                                       uint16_t up = 0x00ff&data[n+3*m+2+10];//左移高位会自动补1
//                                       up = up<<6;

//                                       float tmpdepth = up | down;
//                                       //tmpdepth = sqrt((4*tmpdepth*tmpdepth)/(64.0) - 15.6*64.0);
//                                       //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
//                                       float tmpAngle = startAngle + m*incAngle ;

//                                       uint tmpIndensity = ((data[n+3*m+1+10]&0x03)<<6) | (((data[n+3*m+10])>>2)&0x3f);
//                                       tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
//                                       PolarData dat;
//                                       dat.angle = tmpAngle;
//                                       dat.deepth = tmpdepth;
//                                       dat.indensity = tmpIndensity;
//                                       dat.HrDenote = data[n+10+3*m]&0x01;
//                                       protocolData.data.push_back(dat);
//                                      // qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
//                                   }
//                               }
//                               }
//                               //qDebug() << "finished!";
//                               ringBuff.remove(0,n+frameNum);
//                           }
//                           else {
//                                qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
//                                qDebug() << "lidar scan check err!";
//                                ringBuff.remove(0,n+10);
//                           }
//                       }
//                   }
//                   else if(data[n+1] == 0x66 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
//                       int dataNum = data[n+3];
//                       int frameNum = 10 + dataNum*4;

//                       if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
//                           /*1:长度不够获取整个数据包*/
//                           /*2:当长度异常但数据比较短则通过校验方式抛掉*/
//                           /*3:当长度异常并且过长的数据抛掉*/
//                           if(frameNum > 1024) {
//                               ringBuff.remove(0,n+2);
//                               qDebug() << "lidar calibration return!";
//                               return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
//                           }
//                           return;

//                       }
//                       else {
//                           uint16_t check = data[n+9]<<8 | data[n+8];
//                           uint8_t checkSumH = 0,checkSumL = 0;
//                           checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
//                           checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
//                           for(int m=0; m<dataNum*4; m=m+4) {
//                               /*进行异或校验提取*/
//                               checkSumL ^= data[n+10+m]^data[n+10+m+1];
//                               checkSumH ^= data[n+10+m+2]^data[n+10+m+3];
//                           }
//                           if((checkSumH<<8|checkSumL) == check) {
//                               /*get lidar pointcloud info*/
//                               float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
//                               float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
//                               //qDebug()<< "s: "<< startAngle << "e: " << endAngle;
//                               double incAngle = 0;
//                               bool isParse = true;
//                               //2.0 计算角度
//                               if(dataNum != 0)
//                               {
//                                   if(startAngle > endAngle)
//                                   {
//                                       incAngle = (360 - startAngle + endAngle)/(dataNum-1);
//                                   }
//                                   else
//                                   {
//                                       incAngle = (endAngle - startAngle)/(dataNum-1);
//                                   }
//                                   if(endAngle < startAngle && endAngle > 26) {
//                                       isParse = false;
//                                       qDebug() << "nn: " << n << " ";//ringBuff.toHex() ;
//                                   }
//                               }
//                               if(isParse == true) {
//                                   if((data[n+2]&0x01) == 0x01) {/*起始包*/
//                                       //2.1 计算距离
//                                       double spd = (data[n+2]>>1)/10.0;;
//                                       for(int m=0; m<dataNum; m++)
//                                       {
//                                           uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

//                                           float tmpdepth = up/100.0*15.55;
//                                           //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);

//                                           float tmpAngle = startAngle ;

//                                           uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
//                                           tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
//                                           PolarData dat;
//                                           dat.angle = tmpAngle;
//                                           dat.deepth = tmpdepth;
//                                           dat.indensity = tmpIndensity;
//                                           protocolData.speed = spd;
//                                           protocolData.version = ver;
//                                           protocolData.mcuVoltage = voltage;
//                                           protocolData.healthCode = health;
//                                           protocolData.data.push_back(dat);
//                                           vprotocolData.push_back(protocolData);
//                                           emit transmitPointCloudData(vprotocolData);
//                                           //emit ProcessSendToMainPointCloud(vprotocolData);
//                                           protocolData.data.clear();
//                                           protocolData.speed = 0;

//                                           vprotocolData.clear();
//                                           //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
//                                       }
//                                   }
//                                   else {/*点云包*/
//                                   //2.1 计算距离
//                                   for(int m=0; m<dataNum; m++)
//                                   {
//                                       uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

//                                       float tmpdepth = up/100.0*15.55;
//                                       //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
//                                       //tmpdepth = sqrt(tmpdepth*tmpdepth - 64.0);
//                                       float tmpAngle = startAngle + m*incAngle ;

//                                       uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
//                                       tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
//                                       PolarData dat;
//                                       dat.angle = tmpAngle;
//                                       dat.deepth = tmpdepth;
//                                       dat.indensity = tmpIndensity;
//                                       protocolData.data.push_back(dat);
//                                       //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
//                                   }
//                               }
//                               }
//                               ringBuff.remove(0,n+frameNum);
//                           }
//                           else {
//                                qDebug() << "lidar calibration check err!";
//                                ringBuff.remove(0,n+10);
//                           }
//                       }
//                   }
//                   else {
//                       qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
//                       qDebug() << "lidar calibration no header!";
//                       ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/

//                   }
//                   n = -1;
//                   length = ringBuff.length();
//                   //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/
//                   break;
//               default:
//                   break;

//            }

//        }


//    }
//    void ProcessProtocol::TransmitData(QByteArray str)//通过槽函数处理的才是多线程中的函数
//    {
//        //qDebug()<<thName<<": TransmitData id"<< QThread::currentThreadId();
//        Q_UNUSED(str)
//        if(str.size() == 0) {
//            return;
//        }
//        if(GetSerialPtr()->isOpen())
//        {
//            GetSerialPtr()->write(str);
//        }
//    }
//    void ProcessProtocol::SetProtocolType(int protocolType)
//    {
//        qDebug()<<thName<<": SetProtocolType id"<< QThread::currentThreadId() << " "<<protocolType ;
//        protocolTyp = protocolType;
//    }
//    void ProcessProtocol::RecordData(bool isOk)
//    {
//        isRecordDataToDisk = isOk;
//        QFile file("RecordData.txt");
//        if(file.open(QIODevice::WriteOnly | QIODevice::Append)) {
//            QTextStream text_stream(&file);
//            QString currentDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh-mm-ss");
//            text_stream << currentDate << " "<< isRecordDataToDisk <<"\r\n";
//            file.flush();
//            file.close();
//        }
//    }
//}

//#pragma pack(1) //单字节对齐

//typedef struct _Frame
//{
//    uint8_t head;//head也可以为uint16、uint32。这里假设为0xA5（随便你怎么定）
//    uint8_t type;//数据类型标记（推荐定义为枚举类型）
//    uint8_t len;//下面的data内存区的长度
//    uint8_t *data;
//    //data[len]为校验和
//}Frame;//报文主体结构

//#pragma pack() //对齐结束

//#pragma pack(1) //单字节对齐
//typedef struct _Info
//{
//    uint16_t voltage;//电压
//    float current;//电流
//    uint32_t time;//运行时间
//}Info;
//#pragma pack() //对齐结束

//typedef struct {
//    int16_t minLen;
//    int16_t maxLen;
//}DidLenLimit;

//QByteArray frameBuf;

//#define FRAME_HEAD_LEN          3
//#define FRAME_CHECK_LEN         1


//int getHeadOffset(const char* data, int count)
//{
//    for(int m=0; m<count; m++) {
//        if(*(uint8_t *)data == 0xA5) {
//            return m;
//        }
//    }
//    return -1;
//}


///***************************************************************
//函数名：checksum
//描  述：u8和校验
//输入值：*data 数据指针 len 数据长度
//输出值：无
//返回值：uint8 cs
//***************************************************************/
//uint8_t checkSum(uint8_t *data,uint8_t len)
//{
//    uint8_t cs = 0;
//    while(len-- > 0)
//        cs += *(data++);
//    return cs;
//}

//bool typeVaild(uint8_t type)
//{
//    if(type >= 0 && type <= 12) {
//        return true;
//    }
//    else {
//        return false;
//    }
//}

//DidLenLimit getValidLenScope(uint8_t type)
//{
//    Q_UNUSED(type)
//    DidLenLimit limitType;
//    limitType.maxLen = 1000;
//    limitType.minLen = 1;
//    return limitType;
//}

///*
//功能：根据type解析data域的数据
//参数：数据类型type， data数据域指针， len数据域长度
//*/
//     //parseDataField(pFrame->type, pFrame->data, pFrame->len);//见附录示例函数
//void parseDataField(uint8_t type, uint8_t *data, uint8_t len)
//{
//    if(1 == type)//data域是个float
//    {
//        float power = *((float*)data);//读出收到的float数据

//    }

//    if(2 == type)//data域是个struct Info
//    {
//        Info *pInfo = (Info *)data;
//        uint16_t vol = pInfo->voltage;//从收到的报文中取出：电压
//        float cur = pInfo->current;//电流
//        uint32_t t = pInfo->time;//运行时间
//    }
//}

///*如果想把这段代码写的更规范，可以用switch-case

//switch(type)
//{
//    case 1: parseFloat(data);break;
//    case 2: parseInfo(data);break;
//    default:;
//}

//更专业的写法可以把type和处理函数做成数组（或者Map映射结构），解析时由type索引到处理函数的指针。代码会更简洁，只需一行代码就能处理所有的type
//map[type](data);*/

///*
//功能：处理一条head打头的报文（有可能是假head）
//参数：报文缓冲首地址pFrame，缓存中未处理的字节数leftLen
//返回：报文合法则返回本条报文的长度；报文不合法返回-1（尾巴校验错误、数据域、type错误等等）；无法确定合法不合法回0（例如报文不完整）
//FRAME_HEAD_LEN 包括了head type len这3个字段的长度
//*/
//int16_t parseDataFromOneFrame(const Frame *pFrame, int16_t leftLen)
//{
//    if(!typeVaild(pFrame->type))//查询type的值是否在合法范围内
//        return -1;//如果是个假head，很容易就会走到这里

//    if(leftLen <= FRAME_HEAD_LEN)//字节数太少，无法做进一步判定
//        return 0;//只能当做不完整包

//    //查询type对应的data域的长度的合法范围
//    DidLenLimit lenScope = getValidLenScope(pFrame->type);

//    //尚未收到校验位，无法判定是否合法
//    if(leftLen < FRAME_HEAD_LEN + lenScope.minLen + FRAME_CHECK_LEN)
//    {
//        return 0;//当做不完整包
//    }

//    //len域脱离了合法范围
//    if((pFrame->len > lenScope.maxLen) || (pFrame->len < lenScope.minLen))
//    {
//        return -1;//len有误，要么是假head，要么报文出错
//    }
//    //len范围合法，但尚未收到校验位，无法判定整个报文是否合法
//    if(leftLen < FRAME_HEAD_LEN + pFrame->len + FRAME_CHECK_LEN)
//    {
//        return 0;//当做不完整包
//    }

//    //计算校验位
//    uint16_t calValue = checkSum((uint8_t*)pFrame, FRAME_HEAD_LEN + pFrame->len);
//    //与收到的校验位比对
//    if(pFrame->data[pFrame->len] != calValue)//
//    {
//        return -1;//校验失败
//    }
//    //至此校验成功,根据type解析出data域数据
//    parseDataField(pFrame->type, pFrame->data, pFrame->len);

//    //至此，报文合法，并且data已提取完成，返回该条报文的总长度
//    return FRAME_HEAD_LEN + pFrame->len + FRAME_CHECK_LEN;//合法报文的实际长度;
//}

///*
//对于单片机平台，可以每收到一个字节，或者每一个传输中断，都调一下本函数
//对于操作系统平台，可以在每次接收事件时，调一下本函数
//*/
//void receiveBytes(QByteArray bytes)
//{
//    frameBuf.append(bytes);//把收到的字节都缓存起来

//    while(1)//该循环把缓存分析完之后，会退出。（分析完缓存，并不不是说缓存数据都删干净了，有可能该循环退出以后，缓存中尚存在一个不完整包，等下次进入本函数后会拼接起来）
//    {
//        int stc_offset = getHeadOffset((const char*)frameBuf.constData(), frameBuf.count());//在未处理掉的缓冲区中寻找head，返回报文头的位置。没有找到报文头则返回负数

//        if(stc_offset < 0)//整个缓冲区都找不到报文头
//        {
//            frameBuf.clear();//全是废报文，清空缓冲区
//            break;
//        }
//        else
//        {
//            frameBuf.remove(0, stc_offset);//删除head前面的废报文
//        }

//        //开始处理head打头的报文（有可能是假head，下面会处理掉这种情况）  先判断frameBuf.count()长度要大于Frame需求的大小
//        if(frameBuf.count() < FRAME_HEAD_LEN) {//不完整包，则退出while循环等待下次拼接
//            break;
//        }
//        int8_t retval = parseDataFromOneFrame((const Frame *)frameBuf.constData(), frameBuf.count());//解析did等

//        if(retval > 0)//报文合法
//        {
//            frameBuf.remove(0, retval);//把这条合法报文从缓存中删掉
//        }
//        else if(0 == retval)//不完整包，则退出while循环等待下次拼接
//        {
//            break;
//        }
//        else // <0, head打头但是报文有误（包括但不限于：校验失败、数据域长度与type不匹配、type不可识别等）
//        {
//            frameBuf.remove(0, 1);//删掉一个字节，然后立即进行下一轮循环，继续向下分析
//        }
//    }
//}




#include "lidar_protocol.h"
#include <QThread>
#include <QFile>
#include <QApplication>

std::map<uint16_t,QString> ddsSensorCode {
    {0x0001,"spi error!"},//0
    {0x0002,"vbd calculate"},//1
    {0x0004,"sample temperature error"},//2
    {0x0008,"greymap register error"},//3
    {0x0010,"mp read error"},//4
    {0x0020,"greymap interrupt error"},//5
    {0x0040,"inter firmware error"},//6
    {0x0080,"ranging register error"},//7
    {0x0100,"stop configure error"},//8
    {0x0200,"fps configure error"},//9
    {0x0400,"start configure error"},//10
    {0x0800,"encoder error"},//11
    {0x1000,"tdc clock phase error"},//12
    {0x2000,"laser error"},//13
    {0x4000,"voltage error"},//14
    {0x8000,"ranging interrupt error"},//15
};

#include "sys/time.h"
namespace lidardevice {

    void Sleep(int msec)
    {
        QTime dieTime = QTime::currentTime().addMSecs(msec);
        while( QTime::currentTime() < dieTime )
        QCoreApplication::processEvents(QEventLoop::AllEvents, 2000);
    }


    uint8_t calculateChecksumDds(const QByteArray &data)
    {
        uint8_t checksum = 0;
        for (uint8_t byte : data) {
            checksum ^= byte;
        }
        return checksum;
    }

    uint8_t calculateChecksum1(const QByteArray &data)
    {
        uint8_t checksum = 0;
        for (uint8_t byte : data) {
            checksum ^= byte;
        }
        return checksum;
    }

    uint16_t calculateChecksumInfo(const QByteArray &data)
    {
        uint16_t checksum = 0;
        for (uint8_t byte : data) {
            checksum += byte;
        }
        return checksum;
    }


    uint16_t calculateChecksum2(const QByteArray &data)
    {
        /*quint16 checksum = 0;
        for (quint8 byte : data) {
            checksum ^= byte;
        }*/
        uint8_t * data_ptr = (uint8_t*)data.data();
        uint8_t xor_low = data_ptr[0] ^ data_ptr[2] ^ data_ptr[4] ^ data_ptr[6];
        uint8_t xor_up = data_ptr[1] ^ data_ptr[3] ^ data_ptr[5] ^ data_ptr[7];
        uint8_t data_length = data.size()-8;
        for(uint16_t m=0; m<data_length; m=m+3) {
            xor_low ^= data_ptr[8+m] ^ data_ptr[9+m];
            xor_up ^= data_ptr[10+m];
        }

        uint16_t checksum =  (xor_up<<8) | xor_low;
        return checksum & 0xFFFF;
    }

    uint16_t calculateChecksum3(const QByteArray &data)
    {
        /*quint16 checksum = 0;
        for (quint8 byte : data) {
            checksum ^= byte;
        }*/
        uint8_t * data_ptr = (uint8_t*)data.data();
        uint8_t xor_low = data_ptr[0] ^ data_ptr[2] ^ data_ptr[4] ^ data_ptr[6];
        uint8_t xor_up = data_ptr[1] ^ data_ptr[3] ^ data_ptr[5] ^ data_ptr[7];
        uint8_t data_length = data.size()-8;
        for(uint16_t m=0; m<data_length; m=m+2) {
            xor_low ^= data_ptr[8+m];
            xor_up ^= data_ptr[9+m];
        }

        uint16_t checksum =  (xor_up<<8) | xor_low;
        return checksum & 0xFFFF;
    }

    uint16_t calculateChecksumCalib(const QByteArray &data)
    {

        uint8_t * data_ptr = (uint8_t*)data.data();
        uint8_t xor_low = data_ptr[0] ^ data_ptr[2] ^ data_ptr[4] ^ data_ptr[6];
        uint8_t xor_up = data_ptr[1] ^ data_ptr[3] ^ data_ptr[5] ^ data_ptr[7];
        uint8_t data_length = data.size()-8;
        for(uint16_t m=0; m<data_length; m=m+4) {
            xor_low ^= data_ptr[8+m] ^ data_ptr[9+m];
            xor_up ^= data_ptr[10+m] ^ data_ptr[11+m];
        }

        uint16_t checksum =  (xor_up<<8) | xor_low;
        return checksum & 0xFFFF;
    }



    ProcessProtocol::ProcessProtocol() : isQuitThread(false)
        , protocolTyp(0)
        ,ver(0)
        ,voltage(0)
        ,health(0)
        ,checkSumCnt(0)
        ,isRecordDataToDisk(false)
    {

    }
    ProcessProtocol::~ProcessProtocol()
    {

    }

    void ProcessProtocol::QuitThread(bool isClose)
    {
        isQuitThread = isClose;
        qDebug()<<thName<<": QuitThread id"<< QThread::currentThreadId();
    }

    void ProcessProtocol::ParseProtocol()
    {
        QByteArray cur_data = GetSerialPtr()->readAll();

        data += cur_data;

        while (data.size() >= 2) {

            uint8_t *data_addr = (uint8_t*)data.data();
            uint8_t header1 = data_addr[0];
            uint8_t header2 = data_addr[1];

            static QElapsedTimer timer;

            if (header1 == 0xA5 && header2 != 0x5A && header2 != 0x01) {
                // 协议1

                // spirit
                // iap


                if (data.size() < 6) return;
                cspc_iap_protocol_t *cspc_iap_protocol_ptr = (cspc_iap_protocol_t*)data.data();
                if(cspc_iap_protocol_ptr->data_length < 10000) {

                    uint16_t data_length = 0;
                    if(cspc_iap_protocol_ptr->id == 0xBE ||
                       cspc_iap_protocol_ptr->id == 0xBD ||
                       cspc_iap_protocol_ptr->id == 0xC0 ||
                       cspc_iap_protocol_ptr->id == 0xC1 ||
                       cspc_iap_protocol_ptr->id == 0xC2 ) {
                        data_length = cspc_iap_protocol_ptr->data_length;//iap
                    }
                    else {
                        data_length = cspc_iap_protocol_ptr->data_length*2;//spirit
                    }


                    if (data.size() < data_length+6) return;

                    if (calculateChecksum1(data.left(3) + data.mid(4, 2+data_length)) == cspc_iap_protocol_ptr->check) {

                        if(cspc_iap_protocol_ptr->id == 0xBE) {

                            iap_ack_t  ack;
                            ack.id = cspc_iap_protocol_ptr->id;
                            ack.ack_type = 1;
                            ack.ack_result = 1;
                            emit sig_iap_ack(ack);

                            QByteArray str;
                            str.append((char*)&cspc_iap_protocol_ptr->header,6+data_length);
                            FeedbackInfo(str);
                        }
                        else if(cspc_iap_protocol_ptr->id == 0xBD) {
                             iap_ack_t  ack;
                             ack.id = cspc_iap_protocol_ptr->id;
                             ack.ack_type = 1;
                             ack.ack_result = 1;
                             emit sig_iap_ack(ack);
                        }
                        else if(cspc_iap_protocol_ptr->id == 0xC0) {
                             iap_ack_t  ack;
                             ack.id = cspc_iap_protocol_ptr->id;
                             ack.ack_type = 1;
                             ack.ack_result = 1;
                             emit sig_iap_ack(ack);
                        }
                        else if(cspc_iap_protocol_ptr->id == 0xC1) {

                            if(cspc_iap_protocol_ptr->cmd == (k_D2H|k_HWS|k_HSS)) {
                                iap_ack_t  ack;
                                ack.id = cspc_iap_protocol_ptr->id;
                                ack.ack_type = 1;
                                ack.ack_result = 1;
                                emit sig_iap_ack(ack);
                            }
                            else if(cspc_iap_protocol_ptr->cmd == (k_D2H|k_HRS|k_HSS)){
                                iap_ack_t  ack;
                                ack.id = cspc_iap_protocol_ptr->id;
                                ack.ack_type = 0;
                                ack.ack_result = 1;
                                if(data_length > 0) {
                                    ack.data.append((char*)cspc_iap_protocol_ptr->data,data_length);
                                }
                                emit sig_iap_ack(ack);
                            }
                            else {
                                iap_ack_t  ack;
                                ack.id = cspc_iap_protocol_ptr->id;
                                ack.ack_type = 1;
                                ack.ack_result = 0;
                                emit sig_iap_ack(ack);
                            }


                        }
                        else if(cspc_iap_protocol_ptr->id == 0xC2) {
                            iap_ack_t  ack;
                            ack.id = cspc_iap_protocol_ptr->id;
                            ack.ack_type = 1;
                            ack.ack_result = 1;
                            emit sig_iap_ack(ack);
                        }
                        /*else if(cspc_iap_protocol_ptr->id == 0xAD) {
                            QByteArray str;
                            if(data_length < 288*4+4) {
                                str.append((char*)cspc_iap_protocol_ptr->data,data_length);
                                for(int nn=0; nn<(288*4+4-data_length); nn++) {
                                    str.append(0x01);
                                }
                               // sig_greymap_data(str);
                            }
                            else {
                                str.append((char*)cspc_iap_protocol_ptr->data,288*4+4);
                                //sig_greymap_data(str);
                            }


                            //_buff_cache.remove(0,data_length+6);
                        }
                        else if(cspc_iap_protocol_ptr->id == 0xAE) {
                             std::vector<float> sing_val;
                             float vol = 0;
                             mempcpy(&vol,(char *)cspc_iap_protocol_ptr->data,4);
                             vol *= 15.55f;
                             sing_val.push_back(vol);

                             vol = *(cspc_iap_protocol_ptr->data+4) | (*(cspc_iap_protocol_ptr->data+5))<<8;
                             sing_val.push_back(vol);

                             mempcpy(&vol,(char *)(cspc_iap_protocol_ptr->data+8),4);
                             sing_val.push_back(vol);

                             mempcpy(&vol,(char *)(cspc_iap_protocol_ptr->data+12),4);
                             sing_val.push_back(vol);

                             vol = *(cspc_iap_protocol_ptr->data+16) | (*(cspc_iap_protocol_ptr->data+17))<<8;
                             sing_val.push_back(vol);

                             mempcpy(&vol,(char *)(cspc_iap_protocol_ptr->data+20),4);
                             sing_val.push_back(vol);

                             mempcpy(&vol,(char *)(cspc_iap_protocol_ptr->data+24),4);
                             sing_val.push_back(vol);



                             //emit sig_single_point_data(sing_val);

                        }*/
                        else {
                            if((cspc_iap_protocol_ptr->cmd&0x22) == 0x22) {
                                if(cspc_iap_protocol_ptr->id == 0xAC) {
                                    QByteArray str;
                                    str.append((char*)cspc_iap_protocol_ptr->data,data_length);
                                    transmitHistogramData(str);
                                }
                                else if(cspc_iap_protocol_ptr->id == 0xAD) {
                                    QByteArray str;
                                    str.append((char*)cspc_iap_protocol_ptr->data,data_length);
                                    qDebug() << "rec greymap size : " << str.size();
                                    transmitGreymapData(str);
                                }
                                else if(cspc_iap_protocol_ptr->id == 0xAE) {
                                    QByteArray str;
                                    str.append((char*)cspc_iap_protocol_ptr->data,data_length);
                                    transmitCalibrationData(str);
                                }
                            }
                            else {
                                QByteArray str;
                                str.append((char*)&cspc_iap_protocol_ptr->header,6+data_length);
                                FeedbackInfo(str);
                            }
                        }

                        //qDebug() << "ok in Protocol 1."<<data_length;
                        data  = data.mid(6+data_length);

                    }
                    else {
                        //qDebug() << "error in Protocol 1.";
                        data  = data.mid(1);

                    }
                }
                else {
                    data  = data.mid(1);
                }

            }
            else if (header1 == 0xA5 && header2 == 0x5A) {
                // 协议2
                if (data.size() < 7) return;

                if(data_addr[2] == 0x04 && data_addr[3] == 0x00 && data_addr[6] == 0x06) {// dds
                    if (data.size() < 11) return;
                    cspc_dds_protocol_t *cspc_dds_protocol = (cspc_dds_protocol_t*)data.data();
                    if (calculateChecksumDds(data.left(7) + data.mid(8, 3)) == cspc_dds_protocol->check) {

                        uint16_t tmp_voltage = cspc_dds_protocol->vol;
                        tmp_voltage = (tmp_voltage>>8) | (0xFFFF&(tmp_voltage<<8));
                        voltage = ~(0xFFFF&(tmp_voltage+122));
                        float vol = voltage/100.0;

                        //qDebug()<<cspc_dds_protocol->vol<<" " <<cspc_dds_protocol->status <<" " <<cspc_dds_protocol->def<<" " <<cspc_dds_protocol->code<<data.toHex() ;
                        health = cspc_dds_protocol->code;
                        if(cspc_dds_protocol->code != 0) {
                            QString tmp;
                            uint16_t health = cspc_dds_protocol->code;
                            for(uint t=0; t<16; t++) {
                                if((health&(1<<t)) != 0) {
                                    tmp += ddsSensorCode.at((1<<t))+" | ";
                                }
                            }
                            //qWarning() << "DDS Code! " << tmp;
                        }



                        //qDebug() << "Checksum ok in Protocol dds."<<data.size()<< " "<< vol;
                        data  = data.mid(11);

                    }
                    else {
                        qDebug() << "Checksum error in Protocol dds.";
                        data  = data.mid(1);
                    }
                }
                else {//lidar info
                    cspc_lidar_info_protocol_t * cspc_lidar_info_protocol = (cspc_lidar_info_protocol_t*)data.data();

                    if(cspc_lidar_info_protocol->data_length < 200) {
                        if (data.size() < 7 + cspc_lidar_info_protocol->data_length) return;


                        if (calculateChecksumInfo(data.left(4) + data.mid(6, cspc_lidar_info_protocol->data_length+1)) == cspc_lidar_info_protocol->check) {
                            //qDebug() << "Checksum ok in Protocol 2."<<data.size()<<" type:"<<cspc_lidar_info_protocol->type;
                            if(cspc_lidar_info_protocol->type == 0x01) {
                                ver = cspc_lidar_info_protocol->data[19];
                            }

                            data  = data.mid(7+cspc_lidar_info_protocol->data_length);


                        }
                        else {
                            qDebug() << "Checksum error in Protocol 2.";
                            data  = data.mid(1);
                        }
                    }
                    else {
                        data  = data.mid(1);
                    }
                }

            }
            else if (header1 == 0xAA && header2 == 0x55) {
                // 协议3
                if (data.size() < 10) return;

                cspc_protocol_t *cspc_protocol = (cspc_protocol_t*)data.data();

                int data_length = cspc_protocol->lsn * 3;
                if (data.size() < 10 + data_length) return;
                uint8_t  check_error_cnt = 0;


                if (calculateChecksum2(data.left(8) + data.mid(10, data_length)) == cspc_protocol->check) {

                    float start_angle = ((cspc_protocol->fsa) >> 1)/64.0;
                    float end_angle= ((cspc_protocol->lsa) >> 1)/64.0;
                    double inc_angle = 0;
                    bool isParse = true;
                    //qDebug()<< " "<< startAngle << " " << endAngle;
                    //2.0 计算角度
                    if(cspc_protocol->lsn != 0)
                    {
                        //qDebug() << startAngle << " " << endAngle;
                        if(start_angle > end_angle)
                        {
                            inc_angle = (360 - start_angle + end_angle)/(cspc_protocol->lsn-1);
                        }
                        else
                        {
                            inc_angle = (end_angle - start_angle)/((cspc_protocol->lsn-1) == 0 ? 1:(cspc_protocol->lsn-1));
                        }

                        if(start_angle > end_angle && end_angle > 26) {
                               isParse = false;
                               qDebug() << "angle error";
                        }
                    }

                    if((cspc_protocol->mt&0x01) == 0x01) {/*起始包*/

                        //2.1 计算距离
                        double spd = (cspc_protocol->mt>>1)/10.0;
                        for(int m=0; m<cspc_protocol->lsn; m++)  {

                            vprotocolData.push_back(protocolData);
                            emit transmitPointCloudData(vprotocolData);
                            protocolData.data.clear();
                            protocolData.speed = 0;
                            vprotocolData.clear();


                            quint16 down= (cspc_protocol->data[3*m+1]>>2)&0x3f;
                            uint16_t up = 0x00ff&cspc_protocol->data[3*m+2];//左移高位会自动补1
                            up = up<<6;

                            float tmp_depth = up | down;

                            float tmp_angle = start_angle ;
                            //qDebug()<< "start_angle: "<<tmpAngle;
                            uint tmp_indensity = ((cspc_protocol->data[3*m+1]&0x03)<<6) | (((cspc_protocol->data[3*m])>>2)&0x3f);
                            tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;
                            PolarData dat;
                            dat.angle = tmp_angle;
                            dat.deepth = tmp_depth;
                            dat.indensity = tmp_indensity;
                            dat.HrDenote = data[3*m]&0x01;
                            protocolData.speed = spd;
                            protocolData.version = ver;
                            protocolData.mcuVoltage = voltage;
                            protocolData.healthCode = health;
                            protocolData.data.push_back(dat);

                        }
                    }
                    else {/*点云包*/
                        //2.1 计算距离
                        for(int m=0; m<cspc_protocol->lsn; m++)
                        {
                            quint16 down= (cspc_protocol->data[3*m+1]>>2)&0x3f;
                            uint16_t up = 0x00ff&cspc_protocol->data[3*m+2];//左移高位会自动补1
                            up = up<<6;

                            float tmp_depth = up | down;
                            float tmp_angle = start_angle + m*inc_angle ;

                            uint tmp_indensity = ((cspc_protocol->data[3*m+1]&0x03)<<6) | (((cspc_protocol->data[3*m])>>2)&0x3f);
                            tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;

                            /*if(tmpAngle > 370) {
                                qDebug()<<tmpAngle;
                            }*/

                            PolarData dat;
                            dat.angle = tmp_angle;
                            dat.deepth = tmp_depth;
                            dat.indensity = tmp_indensity;
                            dat.HrDenote = cspc_protocol->data[3*m]&0x01;
                            protocolData.data.push_back(dat);

                        }

                    }


                    quint64 elapsed =  timer.elapsed();
                    timer.start();

                    data  = data.mid(10+data_length);
                    //qDebug() << "ok in Protocol 3: "<< elapsed<< " "<< data.size();
                }
                else {
                    check_error_cnt++;
                }
                if(check_error_cnt == 1) {
                    data_length = cspc_protocol->lsn * 2;
                    if (data.size() < 10 + data_length) return;

                    if(calculateChecksum3(data.left(8) + data.mid(10, data_length)) == cspc_protocol->check) {
                        float start_angle = ((cspc_protocol->fsa) >> 1)/64.0;
                        float end_angle= ((cspc_protocol->lsa) >> 1)/64.0;
                        double inc_angle = 0;
                        bool isParse = true;
    //                    qDebug()<< " "<< start_angle << " " << end_angle;
                        //2.0 计算角度
                        if(cspc_protocol->lsn != 0)
                        {
                            //qDebug() << startAngle << " " << endAngle;
                            if(start_angle > end_angle)
                            {
                                inc_angle = (360 - start_angle + end_angle)/(cspc_protocol->lsn-1);
                            }
                            else
                            {
                                inc_angle = (end_angle - start_angle)/((cspc_protocol->lsn-1) == 0 ? 1:(cspc_protocol->lsn-1));
                            }

                            if(start_angle > end_angle && end_angle > 26) {
                                   isParse = false;
                                   qDebug() << "angle error";
                            }
                        }

                        if((cspc_protocol->mt&0x01) == 0x01) {/*起始包*/

                            //2.1 计算距离
                            double spd = (cspc_protocol->mt>>1)/10.0;
                            for(int m=0; m<cspc_protocol->lsn; m++)  {

                                vprotocolData.push_back(protocolData);
                                emit transmitPointCloudData(vprotocolData);
                                protocolData.data.clear();
                                protocolData.speed = 0;
                                vprotocolData.clear();


                                quint16 down= (cspc_protocol->data[2*m]>>2)&0x3f;
                                uint16_t up = 0x00ff&cspc_protocol->data[2*m+1];//左移高位会自动补1
                                up = up<<6;

                                float tmp_depth = up | down;

                                float tmp_angle = start_angle ;
    //                            qDebug()<< "start_angle: "<<tmpAngle;
                                uint tmp_indensity = 255;
                                tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;
                                PolarData dat;
                                dat.angle = tmp_angle;
                                dat.deepth = tmp_depth;
                                dat.indensity = tmp_indensity;
                                dat.HrDenote = data[2*m]&0x01;
                                protocolData.speed = spd;
                                protocolData.version = ver;
                                protocolData.mcuVoltage = voltage;
                                protocolData.healthCode = health;
                                protocolData.data.push_back(dat);

                            }
                        }
                        else {/*点云包*/
                            //2.1 计算距离
                            for(int m=0; m<cspc_protocol->lsn; m++)
                            {
                                quint16 down= (cspc_protocol->data[2*m]>>2)&0x3f;
                                uint16_t up = 0x00ff&cspc_protocol->data[2*m+1];//左移高位会自动补1
                                up = up<<6;

                                float tmp_depth = up | down;
                                float tmp_angle = start_angle + m*inc_angle ;

                                uint tmp_indensity = 255;
                                tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;

                                /*if(tmpAngle > 370) {
                                    qDebug()<<tmpAngle;
                                }*/

                                PolarData dat;
                                dat.angle = tmp_angle;
                                dat.deepth = tmp_depth;
                                dat.indensity = tmp_indensity;
                                dat.HrDenote = cspc_protocol->data[2*m]&0x01;
                                protocolData.data.push_back(dat);

                            }

                        }


                        quint64 elapsed =  timer.elapsed();
                        timer.start();

                        data  = data.mid(10+data_length);
                    }
                    else {
                        check_error_cnt++;
                    }
                }

                if(check_error_cnt == 2) {
                    qDebug() << "error in Protocol 3.";
                    data  = data.mid(2);
                }

            }
            else if (header1 == 0xAA && header2 == 0x66) {
                // 协议3
                if (data.size() < 10) return;

                cspc_protocol_t *cspc_protocol = (cspc_protocol_t*)data.data();

                int data_length = cspc_protocol->lsn * 4;
                if (data.size() < 10 + data_length) return;


                if (calculateChecksumCalib(data.left(8) + data.mid(10, data_length)) == cspc_protocol->check) {

                    float start_angle = ((cspc_protocol->fsa) >> 1)/64.0;
                    float end_angle= ((cspc_protocol->lsa) >> 1)/64.0;
                    double inc_angle = 0;
                    bool isParse = true;
                    //qDebug()<< " "<< startAngle << " " << endAngle;
                    //2.0 计算角度
                    if(cspc_protocol->lsn != 0)
                    {
                        //qDebug() << startAngle << " " << endAngle;
                        if(start_angle > end_angle)
                        {
                            inc_angle = (360 - start_angle + end_angle)/(cspc_protocol->lsn-1);
                        }
                        else
                        {
                            inc_angle = (end_angle - start_angle)/((cspc_protocol->lsn-1) == 0 ? 1:(cspc_protocol->lsn-1));
                        }

                        if(start_angle > end_angle && end_angle > 26) {
                               isParse = false;
                               qDebug() << "angle error";
                        }
                    }

                    if((cspc_protocol->mt&0x01) == 0x01) {/*起始包*/

                        //2.1 计算距离
                        double spd = (cspc_protocol->mt>>1)/10.0;
                        for(int m=0; m<cspc_protocol->lsn; m++)  {

                            vprotocolData.push_back(protocolData);
                            emit transmitPointCloudData(vprotocolData);
                            protocolData.data.clear();
                            protocolData.speed = 0;

                            vprotocolData.clear();



                            uint16_t depth = (cspc_protocol->data[4*m+3]<<8 | cspc_protocol->data[4*m+2]);

                            float tmp_depth = depth/100.0*15.55;

                            float tmp_angle = start_angle ;
                            uint tmp_indensity = ((cspc_protocol->data[4*m+1])<<8) | (cspc_protocol->data[4*m]);
                            tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;
                            PolarData dat;
                            dat.angle = tmp_angle;
                            dat.deepth = tmp_depth;
                            dat.indensity = tmp_indensity;
                            dat.HrDenote = data[3*m]&0x01;
                            protocolData.speed = spd;
                            protocolData.version = ver;
                            protocolData.mcuVoltage = voltage;
                            protocolData.healthCode = health;
                            protocolData.data.push_back(dat);





                        }
                    }
                    else {/*点云包*/
                        //2.1 计算距离
                        for(int m=0; m<cspc_protocol->lsn; m++)
                        {
                            uint16_t depth = (cspc_protocol->data[4*m+3]<<8 | cspc_protocol->data[4*m+2]);

                            float tmp_depth = depth/100.0*15.55;

                            float tmp_angle = start_angle + m*inc_angle ;
                            uint tmp_indensity = ((cspc_protocol->data[4*m+1])<<8) | (cspc_protocol->data[4*m]);
                            tmp_angle = tmp_angle > 360 ? tmp_angle - 360.0:tmp_angle;

                            /*if(tmpAngle > 370) {
                                qDebug()<<tmpAngle;
                            }*/

                            PolarData dat;
                            dat.angle = tmp_angle;
                            dat.deepth = tmp_depth;
                            dat.indensity = tmp_indensity;
                            protocolData.data.push_back(dat);


                           /*
                            QByteArray str;
                            str.append(0xF1);
                            str.append(0xF1);
                            str.append(0xF1);
                            str.append(0xF1);
                            str.append(0xF1);
                            str.append(0xF1);
                            float pid_speed = tmp_indensity*20.0/360/100;
                            uint32_t pid_u32 = 0;
                            memcpy(&pid_u32, &pid_speed, 4);
                            str.append((char*)&pid_u32,4);
                            FeedbackInfo(str);
                            */

                        }

                    }
                    quint64 elapsed =  timer.elapsed();
                    timer.start();

                    data  = data.mid(10+data_length);
                    //qDebug() << "ok in Protocol 4: "<< elapsed<< " "<< data.size();
                }
                else {
                    qDebug() << "error in Protocol 4.";
                    data  = data.mid(2);
                }

            }
            else if (header1 == 0xFA && (header2 >= 0xA0 &&  header2 < 0xFA)) {

                if (data.size() < 22) return;
                rock_protocol_t  *rock_protocol = (rock_protocol_t*)data_addr;

                float tmp_spd = 0;
                if(rock_protocol->index == 0xF9) {
                    for(int m=0; m<4; m++) {
                        float tmp_angle = (rock_protocol->index-0xA0)*4 + m;

                        uint16_t tmp_depth = (uint16_t)(0x3FFF&((uint16_t)rock_protocol->data[m]));
                        tmp_spd = (1.0*100000)/rock_protocol->spd;

                        PolarData dat;
                        dat.angle = tmp_angle;
                        dat.deepth = tmp_depth;
                        dat.indensity = 0xFF&(rock_protocol->data[m]>>24);
                        dat.HrDenote = 0;
                        protocolData.data.push_back(dat);

                    }
                    protocolData.speed = tmp_spd;
                    protocolData.version = ver;
                    protocolData.mcuVoltage = voltage;
                    protocolData.healthCode = health;
                    vprotocolData.push_back(protocolData);

                    //qDebug();

                    emit transmitPointCloudData(vprotocolData);
                    protocolData.data.clear();


                    vprotocolData.clear();

                }
                else {

                    for(int m=0; m<4; m++) {
                        float tmp_angle = (rock_protocol->index-0xA0)*4 + m;
                        uint16_t tmp_depth = (uint16_t)(0x3FFF&((uint16_t)rock_protocol->data[m]));

                        PolarData dat;
                        dat.angle = tmp_angle;
                        dat.deepth = tmp_depth;
                        dat.indensity = 0xFF&(rock_protocol->data[m]>>24);
                        dat.HrDenote = 0;
                        protocolData.data.push_back(dat);

                    }
                }

                data  = data.mid(22);


            }
            else if (header1 == 0x55 && header2 == 0xAA) {

                if (data.size() < 8) return;

                camsense_protocol_t *cspc_protocol = (camsense_protocol_t*)data.data();

                uint8_t data_type = cspc_protocol->information&0x07;
                int data_format_length = 2;
                if((data_type&0x01) == 0x01) {//包含强度信息
                    if((data_type&0x04) == 0x04) {
                        data_format_length = 4;
                    }
                    else {
                        data_format_length = 3;
                    }
                }
                else {
                    data_format_length = 2;
                }



                int data_length = cspc_protocol->data_number * data_format_length;
                if (data.size() < 12 + data_length)  {
                    return;
                }

                uint16_t check_sum = *((uint16_t*)(cspc_protocol->data+data_length+2));
                uint16_t check_calc = 0;
                int32_t  chk32 = 0;
                uint16_t buff_length = (10 + data_length)/2;
                uint32_t *datajgs = new uint32_t[buff_length];

                for(int i=0; i<buff_length; i++) {
                    datajgs[i] = *(data_addr+2*i) + ((*(data_addr+2*i+1))<<8);//  sendbuff[2*i] + (sendbuff[2*i+1]<<8);
                }

                for(int i=0; i<buff_length; i++) {
                    chk32 = (chk32 << 1) + datajgs[i];
                }
                check_calc = (chk32 & 0x7FFF) + ( chk32 >> 15 );
                check_calc = check_calc & 0x7FFF;


                if(check_calc == check_sum) {
                    float spd = cspc_protocol->speed/60.0f/64.0f;
                    float first_angle = (cspc_protocol->first_angle-0xA000)/64.0f;
                    float last_angle = (*((uint16_t*)(cspc_protocol->data+data_length))-0xA000)/64.0f;

                    float delta_angle = 0;
                    if(last_angle < first_angle) {
                        delta_angle = (last_angle+360-first_angle)/(cspc_protocol->data_number-1);
                    }
                    else {
                        delta_angle = (last_angle-first_angle)/(cspc_protocol->data_number-1);
                    }

                    PolarData dat;
                    static float last_delta_angle = 0.0f;
                    for(int m=0; m<cspc_protocol->data_number; m++) {
                        dat.angle = first_angle + m*delta_angle;
                        if(dat.angle > 360.0) {
                            dat.angle -= 360.0f;
                        }

                        dat.deepth = (*((uint16_t*)&cspc_protocol->data[m*data_format_length]))&0x3FFF;
                        dat.indensity = (*((uint16_t*)(&cspc_protocol->data[m*data_format_length]+2)));
                        dat.HrDenote = 0;//(*((uint16_t*)&cspc_protocol->data[m*data_format_length]))>>14;
                        uint8_t zero_flag = (*((uint16_t*)&cspc_protocol->data[m*data_format_length]))>>14;


                        dat.deepth = dat.deepth;
                        protocolData.speed = spd;
                        protocolData.version = 0;
                        protocolData.mcuVoltage = 330;
                        protocolData.healthCode = 0;
                        protocolData.data.push_back(dat);


                        /*if((fabs(last_delta_angle-360)<2 &&  dat.angle<2)) {
                            vprotocolData.push_back(protocolData);
                            emit transmitPointCloudData(vprotocolData);
                            qDebug()<< protocolData.data.size();
                            //qDebug()<<" ";

                            protocolData.data.clear();
                            protocolData.speed = spd;
                            vprotocolData.clear();
                        }*/
                        if(zero_flag == 0x01) {
                            vprotocolData.push_back(protocolData);
                            emit transmitPointCloudData(vprotocolData);
                            //qDebug()<< protocolData.data.size();

                            protocolData.data.clear();
                            protocolData.speed = spd;
                            vprotocolData.clear();
                        }



                        last_delta_angle = dat.angle;
                    }





                    //qDebug()<<"spd: "<<spd<<"first: "<<(first_angle-0xA000)/64.0f<<"last: "<<(last_angle-0xA000)/64.0f;
                }
                else {
                    qDebug()<<"check error";
                }
                delete []datajgs;
                data  = data.mid(12 + data_length);

            }
            else {
                //qDebug() << "Unknown protocol or incomplete data.";
                //qDebug() << "remove: " << hex<<header1<<" " <<hex<<header2;
                data  = data.mid(1);

            }



        }
    }

#if 0
    void ProcessProtocol::ParseProtocol()
    {
        //qDebug()<<thName<<": ParseProtocol id"<< QThread::currentThreadId();
        int parseDataStatus = kParseNone;

        QByteArray tmp_data = GetSerialPtr()->readAll();
        /*if(isRecordDataToDisk == false) {
            static QTime lastTimeMs;
            QTime startTime = QTime::currentTime();

            QFile file("RecordData.txt");
            file.open(QIODevice::WriteOnly | QIODevice::Append);
            QTextStream text_stream(&file);
            text_stream << lastTimeMs.msecsTo(startTime) <<" :"<<tmp_data.toHex(' ') << "\r\n";
            file.flush();
            file.close();
            lastTimeMs = startTime;
        }*/

        /*粘包*/
        ringBuff += tmp_data;//GetSerialPtr()->readAll();
        /*获取长度*/
        int length = ringBuff.length();


        /*转化为uint8_t 数组*/
        uint8_t *data = (uint8_t*)ringBuff.data();
        /*解析数据*/
        for(int n=0; n<length; n++) {
            /*剩余长度*/
            int leftDataNum = length - n;
            /*获取头标识*/

            if(data[n] == kFrameHead1) {
                if(leftDataNum < 6) {/*小于获取长度信息的索引值 继续接收数据*/
                    return;
                }
                parseDataStatus = kParseData1;
            }
            else if(data[n] == kFrameHead2) {
                if(leftDataNum < 5) {/*小于获取长度信息的索引值 继续接收数据*/
                    return;
                }
                parseDataStatus = kParseData2;
            }
            else if(data[n] == kFrameHead3) {
                if(leftDataNum < 4) {/*小于获取长度信息的索引值 继续接收数据*/
                    return;
                }
                parseDataStatus = kParseData3;
            }
            else {
                parseDataStatus = kParseNone;
            }

            /*判断数据长度与解析数据*/
            switch(parseDataStatus) {
                case kParseData1 :
                    //qDebug() << "entering parse 1...";
                    //header(1) + cmd(1) + id(1) + checkXor(1) + byteNumber(2) + data(n)
                    //header(1) + 0x5a(1) + num(2) + sum(2) + type(1) + data(n)
                    //header(1) + 0x5a(1) + 0x04(1) + 0x00(1) + 保留(2) + 0x06(1) + xor(1) + MSB(1) + LSB(1) + MSB(1)
                    if(data[n+1] == 0x5A && data[n+6] == 0x01) {/*获取雷达信息*/
                        int dataNum = data[n+3]<<8 | data[n+2];
                        int frameNum = 7 + dataNum;
                        if(leftDataNum < frameNum) {/*长度不够获取起始包*/
                            //qDebug() << "lidar information return!";
                            return;
                        }

                        uint16_t checkSum = 0,check = data[n+5]<<8 | data[n+4];
                        for(int m=0; m<frameNum; m++) {
                            if(m == 4 || m == 5) {
                                continue;
                            }
                            checkSum += data[n+m];
                        }
                        if(checkSum == check) {
                            /*get lidar device info*/
                            //qDebug() << "current version: " << data[n+26];
                            ver = data[n+26];
                            ringBuff.remove(0,n+frameNum);
                            qDebug() << "lidar information!";
                        }
                        else {
                            ringBuff.remove(0,n+7);
                            qDebug() << "lidar information check err!";
                        }


                    }
                    else if(data[n+1] == 0x5A && data[n+2] == 0x04) {
                        if(leftDataNum < 11) {/*长度不够获取起始包*/
                            //qDebug() << "lidar health code return!";
                            return;
                        }
                        uint16_t tmpVoltage = 0;
                        if(((data[n+4]<<8) + data[n+5]) == 0) {
                            tmpVoltage = 0;
                        }
                        else {
                            tmpVoltage = ~(((data[n+4]<<8) + data[n+5]) + 122);
                        }


                        voltage = tmpVoltage;
                        QByteArray volCode;

                        health = data[n+9] | (data[n+10]<<8);
                        volCode.append(data[n+10]);
                        volCode.append(data[n+9]);

                        ringBuff.remove(0,n+11);
                        if(health != 0) {
                            QString tmp;

                            for(uint t=0; t<16; t++) {
                                if((health&(1<<t)) != 0) {
                                    tmp += ddsSensorCode.at((1<<t))+" | ";
                                }
                            }
                            qWarning() << "DDS Code! " << tmp;
                        }

                    }
                    else if((data[n+1]&0x02) == 0x02 && (data[n+1]&0x01) == 0x00 && (data[n+1]&0xC0) == 0x00) {/*获取不同的应答信息  根据cmd和id区分...*/
                        int dataNum = data[n+5]<<8 | data[n+4];
                        int frameNum = 6 + dataNum*2;
                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 3000+6+10) {//2048+6+10
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar interaction callback return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }

                        uint8_t checkSum = 0,check = data[n+3];
                        for(int m=0; m<frameNum; m++) {
                            if(m != 3) {
                                checkSum ^= data[n+m];
                            }
                        }
                        //qDebug()<<"receive interactions";
                        if(checkSum == check) {
                            /*get lidar device info*/
                            if((data[n+1]&0x22) == 0x22) {
                                QByteArray str;
                                if(data[n+2] == 0xAC) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    transmitHistogramData(str);
                                }
                                else if(data[n+2] == 0xAD) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    qDebug() << "rec greymap size : " << str.size();
                                    transmitGreymapData(str);
                                }
                                else if(data[n+2] == 0xAE) {
                                    str.append((char*)&data[n+6],dataNum*2);/*data*/
                                    transmitCalibrationData(str);
                                }
                            }
                            else {
                                QByteArray str;
                                //str.push_back(data[n+0]);
//                                str.push_back(data[n+1]);/*cmd*/
//                                str.push_back(data[n+2]);/*id*/
//                                str.append((char*)&data[n+6],dataNum*2);/*data*/

                                str.append((char*)&data[n],6+dataNum*2);
                                FeedbackInfo(str);
                            }

                            //transmitGreymapData(str);
                            //transmitCalibrationData(str);


                            ringBuff.remove(0,n+frameNum);

                            //qDebug() << "lidar interaction callback!";
                        }
                        else {
                            checkSumCnt++;
                            qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
                            qDebug() << "lidar interaction callback check err!";
                            ringBuff.remove(0,n+6);
                        }

                    }
                    else if(data[n+1] == 0x01 ) {
                        uint8_t sum = 0;
                        uint8_t check =0;
                        /*sum = 0xA5  + 0x01 + data[n+2] + data[n+3];
                        if(sum == data[n+4]) {
                            QByteArray str;

                            str.append((char*)&data[n+2],2);
                            transmitCalibrationData(str);
                            ringBuff.remove(0,n+5);
                        }
                        else {
                            ringBuff.remove(0,n+5);
                        }

                        check = uint8_t(~sum);*/
                        sum = 0xA5  + 0x01 + data[n+2] + data[n+3]+ data[n+4] + data[n+5];
                        if(sum == data[n+6]) {
                            QByteArray str;

                            str.append((char*)&data[n+2],4);
                            transmitCalibrationData(str);
                            ringBuff.remove(0,n+7);
                        }
                        else {
                            ringBuff.remove(0,n+7);
                        }
                    }
                    else {

                        qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
                        ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/
                        //qDebug() << "remove header";
                    }
                    n = -1;
                    length = ringBuff.length();
                    //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/

                    break;
                case kParseData2 :
//                    if(data[n+1] == 0x34 && data[n+2] == 0x81 && data[n+3] == 0x28 && data[n+4] == 0x00) {
//                        uint16_t sum = 0;
//                        uint8_t check =0;
//                        for(int m=1; m<0x28+5; m++) {
//                            sum += data[n+m];
//                        }
//                        check = uint8_t(~sum);

//                        if(check == data[n+5+0x28]) {
//                            //qDebug()<<"5300!";
//                            QByteArray str;
//                            str.append((char*)&data[n],46);
//                            transmitCalibrationData(str);
//                            ringBuff.remove(data[n],n+6+0x28);
//                        }
//                        else {
//                            ringBuff.remove(0,5);
//                        }
//                    }


                    break;
                case kParseData3 :
                    if(data[n+1] == 0x55 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
                        int dataNum = data[n+3];
                        int frameNum = 10 + dataNum*3;

                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 1024) {
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar scan return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }
                        else {
                            uint16_t check = data[n+9]<<8 | data[n+8];
                            uint8_t checkSumH = 0,checkSumL = 0;
                            checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
                            checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
                            for(int m=0; m<dataNum*3; m=m+3) {
                                /*进行异或校验提取*/
                                checkSumL ^= data[n+10+m]^data[n+10+m+1];
                                checkSumH ^= data[n+10+m+2];
                            }
                            if((checkSumH<<8|checkSumL) == check) {
                                /*get lidar pointcloud info*/
                                float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
                                float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
                                double incAngle = 0;
                                bool isParse = true;
                                //qDebug()<< " "<< startAngle << " " << endAngle;
                                //2.0 计算角度
                                if(dataNum != 0)
                                {
                                    //qDebug() << startAngle << " " << endAngle;
                                    if(startAngle > endAngle)
                                    {
                                        incAngle = (360 - startAngle + endAngle)/(dataNum-1);
                                    }
                                    else
                                    {
                                        incAngle = (endAngle - startAngle)/((dataNum-1) == 0 ? 1:(dataNum-1));
                                    }

                                    if(startAngle > endAngle && endAngle > 26) {
                                           isParse = false;
                                           qDebug() << "nn: " << n << " "; //ringBuff.toHex() ;
                                    }
                                }
                                if(isParse == true) {
                                    if((data[n+2]&0x01) == 0x01) {/*起始包*/
                                        //qDebug()<<" ";
                                        //2.1 计算距离
                                        double spd = (data[n+2]>>1)/10.0;
                                        for(int m=0; m<dataNum; m++)
                                        {
                                            quint16 down= (data[n+10+3*m+1]>>2)&0x3f;
                                            uint16_t up = 0x00ff&data[n+10+3*m+2];//左移高位会自动补1
                                            up = up<<6;

                                            float tmpdepth = up | down;

                                            //tmpdepth = sqrt(tmpdepth*tmpdepth - 15.6*64.0);
                                            //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                            float tmpAngle = startAngle ;
                                            //qDebug()<< "start_angle: "<<tmpAngle;
                                            uint tmpIndensity = ((data[n+10+3*m+1]&0x03)<<6) | (((data[n+10+3*m])>>2)&0x3f);
                                            tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                            PolarData dat;
                                            dat.angle = tmpAngle;
                                            dat.deepth = tmpdepth;
                                            dat.indensity = tmpIndensity;
                                            dat.HrDenote = data[n+10+3*m]&0x01;
                                            protocolData.speed = spd;
                                            protocolData.version = ver;
                                            protocolData.mcuVoltage = voltage;
                                            protocolData.healthCode = health;
                                            protocolData.data.push_back(dat);
                                            vprotocolData.push_back(protocolData);
                                            /*if(protocolData.data.size() > 760) {
                                                qDebug()<<protocolData.data.size();
                                            }*/
                                            //qDebug()<< "update";

                                            /*struct timeval tv;
                                            struct timezone tz;
                                            static double time_ms_last;

                                            gettimeofday(&tv, &tz); //获取当前时间
                                            double time_ms = (double)tv.tv_usec/1000.0;
                                            double delta_time = time_ms-time_ms_last;
                                            if(delta_time < 0) {
                                                delta_time += 1000;
                                            }
                                            qDebug() << delta_time;
                                            time_ms_last = time_ms;*/

                                            //qDebug() << "total size: " <<protocolData.data.size();

                                            /*for(uint i=0; i<protocolData.data.size()-1; i++) {
                                                if(protocolData.data[i+1].angle < protocolData.data[i].angle && protocolData.data[i].angle>10 && protocolData.data[i+1].angle>10 ) {
                                                    for(uint j=0; j<protocolData.data.size(); j++) {
                                                        qDebug()<< "angle error: " << protocolData.data[j].angle;

                                                    }
                                                    break;
                                                }

                                            }*/



                                            emit transmitPointCloudData(vprotocolData);
                                            //emit ProcessSendToMainPointCloud(vprotocolData);
                                            protocolData.data.clear();
                                            protocolData.speed = 0;

                                            vprotocolData.clear();
                                            //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
                                        }
                                    }
                                    else {/*点云包*/
                                        //2.1 计算距离
                                        for(int m=0; m<dataNum; m++)
                                        {
                                            quint16 down= (data[n+3*m+1+10]>>2)&0x3f;
                                            uint16_t up = 0x00ff&data[n+3*m+2+10];//左移高位会自动补1
                                            up = up<<6;

                                            float tmpdepth = up | down;
                                            //tmpdepth = sqrt((4*tmpdepth*tmpdepth)/(64.0) - 15.6*64.0);
                                            //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                            float tmpAngle = startAngle + m*incAngle ;

                                            uint tmpIndensity = ((data[n+3*m+1+10]&0x03)<<6) | (((data[n+3*m+10])>>2)&0x3f);
                                            tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;

                                            /*if(tmpAngle > 370) {
                                                qDebug()<<tmpAngle;
                                            }*/

                                            PolarData dat;
                                            dat.angle = tmpAngle;
                                            dat.deepth = tmpdepth;
                                            dat.indensity = tmpIndensity;
                                            dat.HrDenote = data[n+10+3*m]&0x01;
                                            protocolData.data.push_back(dat);



                                           // qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
                                        }
//                                        for(uint i=0; i<protocolData.data.size()-1; i++) {
//                                            if(protocolData.data[i+1].angle < protocolData.data[i].angle && protocolData.data[i].angle>10 && protocolData.data[i+1].angle>10 ) {
//                                                /*for(uint j=0; j<protocolData.data.size(); j++) {
//                                                    qDebug()<< "angle error: " << protocolData.data[j].angle;

//                                                }*/
//                                                qDebug()<< "stop";
//                                                while(1) {
//                                                    //QString currentDate_time = QDateTime::currentDateTime().toString("hh:mm:ss");
//                                                    //qDebug() << currentDate_time;
//                                                    //Sleep(500);
//                                                    qDebug()<< "loop";
//                                                }
//                                                //break;
//                                            }

//                                        }
                                    }
                                }
                                //qDebug() << "finished!";
                                ringBuff.remove(0,n+frameNum);
                            }
                            else {
                                 //QString currentDate_time = QDateTime::currentDateTime().toString("hh:mm:ss");
                                 //qDebug() << currentDate_time << n << ringBuff.toHex() ;
                                 qDebug() << "lidar scan check err!";
                                 ringBuff.remove(0,n+10);
                            }
                        }
                    }
                    else if(data[n+1] == 0x66 && ((data[n+2]&0x01) == 0x01 || (data[n+2]&0x01) == 0x00)) {/*起始包*/  /*点云包*/
                        int dataNum = data[n+3];
                        int frameNum = 10 + dataNum*4;

                        if(leftDataNum < frameNum ) {//&& (frameNum < 1024)) {
                            /*1:长度不够获取整个数据包*/
                            /*2:当长度异常但数据比较短则通过校验方式抛掉*/
                            /*3:当长度异常并且过长的数据抛掉*/
                            if(frameNum > 1024) {
                                ringBuff.remove(0,n+2);
                                qDebug() << "lidar calibration return!";
                                return;/*这段优化将获取的数据长度与实际长度不匹配的bug 需要继续跟踪优化*/
                            }
                            return;

                        }
                        else {
                            uint16_t check = data[n+9]<<8 | data[n+8];
                            uint8_t checkSumH = 0,checkSumL = 0;
                            checkSumL = data[n+0]^data[n+2]^data[n+4]^data[n+6];
                            checkSumH = data[n+1]^data[n+3]^data[n+5]^data[n+7];
                            for(int m=0; m<dataNum*4; m=m+4) {
                                /*进行异或校验提取*/
                                checkSumL ^= data[n+10+m]^data[n+10+m+1];
                                checkSumH ^= data[n+10+m+2]^data[n+10+m+3];
                            }
                            if((checkSumH<<8|checkSumL) == check) {
                                /*get lidar pointcloud info*/
                                float startAngle = ((data[n+4] | (data[n+5]<<8)) >> 1)/64.0;
                                float endAngle = ((data[n+6] | (data[n+7]<<8)) >> 1)/64.0;
                                //qDebug()<< "s: "<< startAngle << "e: " << endAngle;
                                double incAngle = 0;
                                bool isParse = true;
                                //2.0 计算角度
                                if(dataNum != 0)
                                {
                                    if(startAngle > endAngle)
                                    {
                                        incAngle = (360 - startAngle + endAngle)/(dataNum-1);
                                    }
                                    else
                                    {
                                        incAngle = (endAngle - startAngle)/(dataNum-1);
                                    }
                                    if(endAngle < startAngle && endAngle > 26) {
                                        isParse = false;
                                        qDebug() << "nn: " << n << " ";//ringBuff.toHex() ;
                                    }
                                }
                                if(isParse == true) {
                                    if((data[n+2]&0x01) == 0x01) {/*起始包*/
                                        //2.1 计算距离
                                        double spd = (data[n+2]>>1)/10.0;;
                                        for(int m=0; m<dataNum; m++)
                                        {
                                            uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

                                            float tmpdepth = up/100.0*15.55;
                                            //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);

                                            float tmpAngle = startAngle ;

                                            uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
                                            tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                            PolarData dat;
                                            dat.angle = tmpAngle;
                                            dat.deepth = tmpdepth;
                                            dat.indensity = tmpIndensity;
                                            protocolData.speed = spd;
                                            protocolData.version = ver;
                                            protocolData.mcuVoltage = voltage;
                                            protocolData.healthCode = health;
                                            protocolData.data.push_back(dat);
                                            vprotocolData.push_back(protocolData);
                                            emit transmitPointCloudData(vprotocolData);
                                            //emit ProcessSendToMainPointCloud(vprotocolData);
                                            protocolData.data.clear();
                                            protocolData.speed = 0;

                                            vprotocolData.clear();
                                            //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity << " "<< (data[n+2]>>1);
                                        }
                                    }
                                    else {/*点云包*/
                                    //2.1 计算距离
                                    for(int m=0; m<dataNum; m++)
                                    {
                                        uint32_t up = (data[n+10+4*m+3]<<8 | data[n+10+4*m+2]);

                                        float tmpdepth = up/100.0*15.55;
                                        //tmpdepth = (4*tmpdepth*tmpdepth-64)/(4*tmpdepth);
                                        //tmpdepth = sqrt(tmpdepth*tmpdepth - 64.0);
                                        float tmpAngle = startAngle + m*incAngle ;

                                        uint16_t tmpIndensity = data[n+10+4*m+1]<<8 | data[n+10+4*m+0] ;
                                        tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                                        PolarData dat;
                                        dat.angle = tmpAngle;
                                        dat.deepth = tmpdepth;
                                        dat.indensity = tmpIndensity;
                                        protocolData.data.push_back(dat);
                                        //qDebug() << tmpAngle << " " <<tmpdepth<< " " <<tmpIndensity;
                                    }
                                }
                                }
                                ringBuff.remove(0,n+frameNum);
                            }
                            else {
                                 qDebug() << "lidar calibration check err!";
                                 ringBuff.remove(0,n+10);
                            }
                        }
                    }
                    else {
//                        qDebug() << "n: " << n << " ";//<< ringBuff.toHex() ;
//                        qDebug() << "lidar calibration no header!";
                        ringBuff.remove(0,n+1);/*去掉头字节  防止每次进入*/

                    }
                    n = -1;
                    length = ringBuff.length();
                    //ParseProtocol();/*继续从缓冲区中解析数据 不等待响应槽函数*/
                    break;
                default:
                    break;
            }

        }


    }

#endif
    void ProcessProtocol::TransmitData(QByteArray str)//通过槽函数处理的才是多线程中的函数
    {
        //qDebug()<<thName<<": TransmitData id"<< QThread::currentThreadId();
        Q_UNUSED(str)
        if(str.size() == 0) {
            return;
        }
        if(GetSerialPtr()->isOpen())
        {
            GetSerialPtr()->write(str);
        }
    }
    void ProcessProtocol::SetProtocolType(int protocolType)
    {
        qDebug()<<thName<<": SetProtocolType id"<< QThread::currentThreadId() << " "<<protocolType ;
        protocolTyp = protocolType;
    }
    void ProcessProtocol::RecordData(bool isOk)
    {
        isRecordDataToDisk = isOk;
        QFile file("RecordData.txt");
        if(file.open(QIODevice::WriteOnly | QIODevice::Append)) {
            QTextStream text_stream(&file);
            QString currentDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh-mm-ss");
            text_stream << currentDate << " "<< isRecordDataToDisk <<"\r\n";
            file.flush();
            file.close();
        }
    }
}

#pragma pack(1) //单字节对齐

typedef struct _Frame
{
    uint8_t head;//head也可以为uint16、uint32。这里假设为0xA5（随便你怎么定）
    uint8_t type;//数据类型标记（推荐定义为枚举类型）
    uint8_t len;//下面的data内存区的长度
    uint8_t *data;
    //data[len]为校验和
}Frame;//报文主体结构

#pragma pack() //对齐结束

#pragma pack(1) //单字节对齐
typedef struct _Info
{
    uint16_t voltage;//电压
    float current;//电流
    uint32_t time;//运行时间
}Info;
#pragma pack() //对齐结束

typedef struct {
    int16_t minLen;
    int16_t maxLen;
}DidLenLimit;

QByteArray frameBuf;

#define FRAME_HEAD_LEN          3
#define FRAME_CHECK_LEN         1


int getHeadOffset(const char* data, int count)
{
    for(int m=0; m<count; m++) {
        if(*(uint8_t *)data == 0xA5) {
            return m;
        }
    }
    return -1;
}


/***************************************************************
函数名：checksum
描  述：u8和校验
输入值：*data 数据指针 len 数据长度
输出值：无
返回值：uint8 cs
***************************************************************/
uint8_t checkSum(uint8_t *data,uint8_t len)
{
    uint8_t cs = 0;
    while(len-- > 0)
        cs += *(data++);
    return cs;
}

bool typeVaild(uint8_t type)
{
    if(type >= 0 && type <= 12) {
        return true;
    }
    else {
        return false;
    }
}

DidLenLimit getValidLenScope(uint8_t type)
{
    Q_UNUSED(type)
    DidLenLimit limitType;
    limitType.maxLen = 1000;
    limitType.minLen = 1;
    return limitType;
}

/*
功能：根据type解析data域的数据
参数：数据类型type， data数据域指针， len数据域长度
*/
     //parseDataField(pFrame->type, pFrame->data, pFrame->len);//见附录示例函数
void parseDataField(uint8_t type, uint8_t *data, uint8_t len)
{
    if(1 == type)//data域是个float
    {
        float power = *((float*)data);//读出收到的float数据

    }

    if(2 == type)//data域是个struct Info
    {
        Info *pInfo = (Info *)data;
        uint16_t vol = pInfo->voltage;//从收到的报文中取出：电压
        float cur = pInfo->current;//电流
        uint32_t t = pInfo->time;//运行时间
    }
}

/*如果想把这段代码写的更规范，可以用switch-case

switch(type)
{
    case 1: parseFloat(data);break;
    case 2: parseInfo(data);break;
    default:;
}

更专业的写法可以把type和处理函数做成数组（或者Map映射结构），解析时由type索引到处理函数的指针。代码会更简洁，只需一行代码就能处理所有的type
map[type](data);*/

/*
功能：处理一条head打头的报文（有可能是假head）
参数：报文缓冲首地址pFrame，缓存中未处理的字节数leftLen
返回：报文合法则返回本条报文的长度；报文不合法返回-1（尾巴校验错误、数据域、type错误等等）；无法确定合法不合法回0（例如报文不完整）
FRAME_HEAD_LEN 包括了head type len这3个字段的长度
*/
int16_t parseDataFromOneFrame(const Frame *pFrame, int16_t leftLen)
{
    if(!typeVaild(pFrame->type))//查询type的值是否在合法范围内
        return -1;//如果是个假head，很容易就会走到这里

    if(leftLen <= FRAME_HEAD_LEN)//字节数太少，无法做进一步判定
        return 0;//只能当做不完整包

    //查询type对应的data域的长度的合法范围
    DidLenLimit lenScope = getValidLenScope(pFrame->type);

    //尚未收到校验位，无法判定是否合法
    if(leftLen < FRAME_HEAD_LEN + lenScope.minLen + FRAME_CHECK_LEN)
    {
        return 0;//当做不完整包
    }

    //len域脱离了合法范围
    if((pFrame->len > lenScope.maxLen) || (pFrame->len < lenScope.minLen))
    {
        return -1;//len有误，要么是假head，要么报文出错
    }
    //len范围合法，但尚未收到校验位，无法判定整个报文是否合法
    if(leftLen < FRAME_HEAD_LEN + pFrame->len + FRAME_CHECK_LEN)
    {
        return 0;//当做不完整包
    }

    //计算校验位
    uint16_t calValue = checkSum((uint8_t*)pFrame, FRAME_HEAD_LEN + pFrame->len);
    //与收到的校验位比对
    if(pFrame->data[pFrame->len] != calValue)//
    {
        return -1;//校验失败
    }
    //至此校验成功,根据type解析出data域数据
    parseDataField(pFrame->type, pFrame->data, pFrame->len);

    //至此，报文合法，并且data已提取完成，返回该条报文的总长度
    return FRAME_HEAD_LEN + pFrame->len + FRAME_CHECK_LEN;//合法报文的实际长度;
}

/*
对于单片机平台，可以每收到一个字节，或者每一个传输中断，都调一下本函数
对于操作系统平台，可以在每次接收事件时，调一下本函数
*/
void receiveBytes(QByteArray bytes)
{
    frameBuf.append(bytes);//把收到的字节都缓存起来

    while(1)//该循环把缓存分析完之后，会退出。（分析完缓存，并不不是说缓存数据都删干净了，有可能该循环退出以后，缓存中尚存在一个不完整包，等下次进入本函数后会拼接起来）
    {
        int stc_offset = getHeadOffset((const char*)frameBuf.constData(), frameBuf.count());//在未处理掉的缓冲区中寻找head，返回报文头的位置。没有找到报文头则返回负数

        if(stc_offset < 0)//整个缓冲区都找不到报文头
        {
            frameBuf.clear();//全是废报文，清空缓冲区
            break;
        }
        else
        {
            frameBuf.remove(0, stc_offset);//删除head前面的废报文
        }

        //开始处理head打头的报文（有可能是假head，下面会处理掉这种情况）  先判断frameBuf.count()长度要大于Frame需求的大小
        if(frameBuf.count() < FRAME_HEAD_LEN) {//不完整包，则退出while循环等待下次拼接
            break;
        }
        int8_t retval = parseDataFromOneFrame((const Frame *)frameBuf.constData(), frameBuf.count());//解析did等

        if(retval > 0)//报文合法
        {
            frameBuf.remove(0, retval);//把这条合法报文从缓存中删掉
        }
        else if(0 == retval)//不完整包，则退出while循环等待下次拼接
        {
            break;
        }
        else // <0, head打头但是报文有误（包括但不限于：校验失败、数据域长度与type不匹配、type不可识别等）
        {
            frameBuf.remove(0, 1);//删掉一个字节，然后立即进行下一轮循环，继续向下分析
        }
    }
}










